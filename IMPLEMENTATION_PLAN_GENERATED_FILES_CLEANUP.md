# Implementation Plan: Generated Files Clean-up & Harmonisation (UI)

## Introduction  
This plan guides implementors through consolidating all generated artefacts (Relay, future GraphQL codegen, etc.) into one canonical directory while deleting legacy folders and updating all dependent paths. The goal is to reduce repository bloat, prevent stale artefacts, and speed up CI without adding shims or flags.

> **Scope** – UI workspace only (`ui/`). No code/config is changed while authoring this document; these instructions are for a later, reviewed PR.

## Background  
Historically Relay, Prism<PERSON>, Swagger and other generators emitted files into `ui/lib/relay/__generated__` and other `ui/lib/**` trees. As more source code migrated to `ui/src`, two problems emerged:  
1. **Duplicate/Stale Artefacts** – old generated files stayed in `ui/lib/**` after refactors, hiding genuine compile errors and increasing repo size/CI time.  
2. **Hard-coded Imports** – dozens of modules import generated types via the obsolete `@/src/relay/__generated__/…` alias which does not match the desired `@/relay/__generated__/…` mapping.

Future work (Priority 1 / 3) will make `ui/src` the single canonical tree, so generated output must live there as well. This plan describes exactly how to get there and why each step is required.

---

## 1 Current State Audit

| # | Generated Folder (non-`node_modules`) | File count | Size | Notes |
|---|---------------------------------------|-----------:|------:|-------|
| 1 | `ui/src/relay/__generated__` | 101 | 1.6 MB | Only active Relay artefacts. |

*Discovery commands*
```bash
# list all generated dirs (excluding node_modules)
find . -type d -name "__generated__" | grep -v node_modules
# file count & disk usage
find ui/src/relay/__generated__ -type f | wc -l     # 101
du -sh ui/src/relay/__generated__                   # 1.6M
```
No other Prisma, Swagger or generic `generated` folders were found outside `node_modules`.

### 1.1 Import Mapping Evidence

`ripgrep` shows **80 files** importing artefacts via the *non-canonical* alias `@/src/relay/__generated__/…`.
```bash
rg -l "@/src/relay/__generated__" ui/src | wc -l   # 80
```
No references to the historic path `@/lib/relay/__generated__` remain.

A CSV of the 80 files is available via:
```bash
rg -l "@/src/relay/__generated__" ui/src > /tmp/non-canonical-imports.csv
```

---

## 2 Canonical Location Decision

Chosen path: **`ui/src/relay/__generated__`**

Rationale: resides inside `src`, already used by Relay compiler, aligns with existing TypeScript path alias `@/relay/*` defined in `ui/tsconfig.json`.

---

## 3 Planned Work Breakdown

### TL;DR – Change Checklist
1. **Verify generator output** – Confirm `relay.config.json` already points to `src/relay/__generated__`; correct it only if drifted.
2. **Update scripts** – Ensure `pnpm relay` relies on that config (or add `--artifactDirectory` flag for belt-and-braces safety).
3. **Rewrite imports** – Codemod all `@/src/relay/__generated__/*` paths to `@/relay/__generated__/*`.
4. **Deprecate `@/src/*` alias** – Mark as deprecated immediately and schedule removal once migration is green.
5. **Delete legacy folders** – Remove (or confirm absence of) `ui/lib/relay/__generated__` and any other `__generated__` outside `src/relay` (idempotent).
6. **Harden ignore rules** – Add canonical & block-list rules to root `.gitignore`; whitelist test mocks.
7. **Add CI validation** – New `pnpm validate:generated` script + optional pre-commit hook to fail if non-canonical imports/folders re-appear.


### 3.1 Verify Generator Configurations (already correct)

Task | Details
-----|---------
Relay | **Verify** `relay.config.json` already contains `"artifactDirectory": "./src/relay/__generated__"`; fix only if drifted.
GraphQL Codegen (if later adopted) | Point `generates:` paths to the same folder.
Prisma / Swagger (N/A today) | Verify future config mirrors the canonical path if client artefacts are ever emitted to UI.
`package.json` scripts | Ensure `pnpm relay` invokes `relay-compiler --artifactDirectory src/relay/__generated__`.
IDE / VSCode | Update any *Run Task* / *Problem Matcher* that hard-codes the old lib path.

**Why** – Aligns every generator and developer tool to a single source of truth so no artefacts are emitted elsewhere, eliminating drift and duplicate code.

### 3.2 Rewrite Imports

**Why** – Guarantees that all source files reference the canonical artefact location, avoiding fragile absolute paths and ensuring any missing import errors surface during the build.

1. **Codemod** (preferred) – run once in `ui/`:
   ```bash
   npx jscodeshift -t ./scripts/codemods/relay-import-path.js \
     "src/**/*.@(ts|tsx|mts|cts|graphql)"
   ```
   where transformer changes:
   * `@/src/relay/__generated__/X` → `@/relay/__generated__/X`
2. **ts-morph script** (fallback) – for safer AST-based edits; see `scripts/ts-morph/relay-imports.ts`.

### 3.3 Delete Legacy Folders

**Why** – Legacy folders contain stale code, inflate repository size and can silently satisfy imports, hiding real errors. Their removal forces unresolved paths to be fixed rather than compiled with outdated artefacts.

```bash
# (paths no longer exist but scripted for idempotency)
rm -rf ui/lib/relay/__generated__
rm -rf ui/lib/__generated__
```

### 3.4 Harden `.gitignore`

**Why** – Prevents regenerated noise from creeping back into the repo and CI, keeping the single-source-of-truth invariant intact.

Add (or ensure) the following **in the repository-root `.gitignore`** (duplicate in `ui/.gitignore` if monorepo conventions require):
```
# Relay / GraphQL artefacts
ui/src/relay/__generated__/

# Allow unit-test mocks to live alongside tests
!**/__tests__/**/__generated__/

# Block old locations
ui/lib/**/__generated__/
```

### 3.5 Deprecate `@/src/*` Alias

*Stage 1* – Keep alias but add `// TODO: remove – legacy generated-path migration` comment in `tsconfig.json` & `jest.config.cjs`.

*Stage 2* – After `pnpm validate:generated` passes in CI with zero matches, delete the alias.

### 3.6 Verification Steps (to be automated in CI)

```bash
pnpm relay && pnpm check   # regenerate & TS type-check
pnpm test                  # Jest suites
pnpm build                 # Next.js/Vite production build
pnpm lint                  # ESLint (built-in rules only)
# custom validation – fails if stray paths or folders exist
pnpm validate:generated
```
All production artefacts must exist **only** under `src/relay/__generated__` after these commands (unit-test mocks under `**/__tests__/**/__generated__` are exempt).

---

## 4 Risk Matrix

| Risk | Impact | Mitigation |
|------|--------|------------|
| Stale `graphql` tags not re-compiled | Build errors | Run `pnpm relay` in CI; fail if any artefacts older than source. |
| Storybook stories referencing old path | Storybook compile fail | Add Storybook check to CI after codemod. |
| Hidden import via dynamic `require()` | Runtime crash | Grep for `require('.*__generated__')` after codemod. |
| Developer regenerates to wrong folder due to local config | Fragmentation | `postinstall` script echoes warning if `artifactDirectory` differs. |

---

## 5 Rollback Strategy

* The removed folders are still in **git history**.  `git checkout <sha> -- <path>` restores any artefact instantly.
* Codemod changes will be committed separately; revert via `git revert` if a blocker arises.
* Relay compiler config change can be toggled back by editing one line in `relay.config.json`.

---

## 6 Acceptance Criteria

1. `pnpm relay && pnpm check` succeeds with generated artefacts exclusively in `src/relay/__generated__`.
2. All Jest tests pass; **none are skipped or marked flaky**.
3. `pnpm build` completes without warnings related to missing artefacts.
4. ESLint passes with *built-in rules only*; no custom rules added.
5. Two-Attempts Rule honoured – *if two approaches fail to fix an issue, work stops and guidance is requested before a third is attempted.*

*(Two-Attempts Rule repeated for clarity: If two different strategies cannot resolve a generated-file problem, the implementor **MUST STOP**, document both failed attempts and the error, and seek approval. A third attempt is prohibited without explicit sign-off.)*

---

## 7 Timeline & Owners

Phase | Duration | Owner
------|----------|------
Audit confirmation (this doc) | 0.5 d | @dev-lead
Codemod authoring & dry-run | 0.5 d | @frontend-arch
Config updates + CI tweaks | 0.5 d | @frontend-arch
Verification & PR review | 1 d | @qa
Buffer / rollback | 0.5 d | —

---

## 8 Appendix A – Commands Reference

```bash
# list non-canonical imports in detail
rg "@/src/relay/__generated__/" ui/src

# confirm no stray generated dirs after cleanup
find ui -type d -name "__generated__" | grep -v "src/relay/__generated__"
```

---

> End of plan
