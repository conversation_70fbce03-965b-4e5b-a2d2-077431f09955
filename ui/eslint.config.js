import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import relay from 'eslint-plugin-relay';
import { fixupPluginRules } from '@eslint/compat';
import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';
import react from 'eslint-plugin-react';
import unicorn from 'eslint-plugin-unicorn';

// Import custom rules for infinite loop prevention
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load custom rules using createRequire for CommonJS modules
const require = createRequire(import.meta.url);

const customRules = {
    'relay-fragment-dependencies': require(path.join(__dirname, 'eslint-custom-rules/relay-fragment-dependencies.cjs')),
    'react-html-structure-validation': require(path.join(__dirname, 'eslint-custom-rules/react-html-structure-validation.cjs'))
};

export default tseslint.config(
    { ignores: ['dist'] },
    {
        // TODO: add recommended config.  Disabled this temporarily as it generates a lot of errors.
        // extends: [js.configs.recommended, ...tseslint.configs.recommended, relay.configs.recommended],
        extends: [...tseslint.configs.recommended, ...tseslint.configs.recommendedTypeChecked, relay.configs.recommended],
        files: ['**/*.{ts,tsx}'],
        languageOptions: {
            parser: tseslint.parser,
            ecmaVersion: 2020,
            globals: globals.browser,
            parserOptions: {
                project: './tsconfig.json',
                tsconfigRootDir: __dirname
            }
        },
        settings: {
            'import/resolver': {
                typescript: { project: './tsconfig.json' }
            }
        },
        plugins: {
            'react-hooks': reactHooks,
            'react-refresh': reactRefresh,
            react,
            unicorn,
            relay: fixupPluginRules(relay),
            custom: {
                rules: customRules
            }
        },
        rules: {
            ...reactHooks.configs.recommended.rules,
            'react-refresh/only-export-components': [
                'warn',
                {
                    allowConstantExport: true,
                    allowExportNames: [
                        // Allow GraphQL fragments with common naming patterns
                        '*Fragment',
                        '*Fragment*',
                        'TimeSheetDetailRow_payStubDetailFragment',
                        'TimeSheetDetailTableView_payStubFragment',
                        'TimeSheetGrid_timeSheetFragment',
                        'PayStubTable_payStubFragment',
                        'TimeSheetSettingsFragment',
                        'TimesheetDetailPageFragment',
                        'TimesheetToolbar_timeSheetFragment',
                        'TimesheetRosterCustomViewsFragment',
                        'EmployerRosterGridCustomViewsFragment',
                        // Allow shared constants and types
                        'numericColumnUids',
                        'editableColumnUids',
                        'TableColumn'
                    ]
                }
            ],
            'relay/graphql-syntax': 'error',
            'relay/graphql-naming': 'error',
            'relay/must-colocate-fragment-spreads': 'warn',
            'relay/no-future-added-value': 'warn',
            'relay/unused-fields': 'warn',
            'relay/function-required-argument': 'warn',
            'relay/hook-required-argument': 'warn',
            // Replace custom infinite loop prevention with community rules
            'react/jsx-no-bind': [
                'warn',
                {
                    ignoreRefs: true,
                    allowArrowFunctions: false,
                    allowBind: false,
                    allowFunctions: false,
                    ignoreDOMComponents: false
                }
            ],
            'unicorn/no-new-array': 'warn',
            // Fragment dependency validation rules
            'custom/relay-fragment-dependencies': [
                'error',
                {
                    srcPath: path.join(__dirname, 'src'),
                    enforceFragmentSpreading: true,
                    enforceFragmentNaming: true
                }
            ],
            // HTML structure validation rules
            'custom/react-html-structure-validation': [
                'error',
                {
                    checkTableStructure: true,
                    checkErrorBoundaries: true
                }
            ],
            // Enhanced React Hooks rules for infinite loop prevention
            'react-hooks/exhaustive-deps': 'error',
            'react-hooks/rules-of-hooks': 'error',
            
            // Phase 5: Enforce no console statements
            'no-console': 'error',
            // ✅ Enable type-aware rules explicitly
            '@typescript-eslint/no-unsafe-assignment': 'error',
            '@typescript-eslint/no-unsafe-argument': 'error',
            '@typescript-eslint/no-unsafe-call': 'error',
            '@typescript-eslint/no-unsafe-member-access': 'error',
            '@typescript-eslint/no-explicit-any': 'error',
            // Enforce consistent type assertions to prevent unsafe casts
            '@typescript-eslint/consistent-type-assertions': [
                'error',
                {
                    assertionStyle: 'as',
                    objectLiteralTypeAssertions: 'never'
                }
            ],
            // Ban deprecated PayStub mutation helper to prevent accidental usage
            'no-restricted-imports': [
                'error',
                {
                    paths: [
                        {
                            name: '@/src/mutations/timesheet/AddSinglePayStubMutation',
                            message: 'Deprecated: use addEmptyPayStubMutation (AddEmptyPayStubMutation.ts) instead.'
                        }
                    ],
                    patterns: ['**/AddSinglePayStubMutation']
                }
            ],

            // Disable overly aggressive setState rule – handled adequately by react-hooks/exhaustive-deps
            // 'no-restricted-syntax': [
            //     'error',
            //     {
            //         selector: "CallExpression[callee.name=/^set[A-Z]/]:not(:has(CallExpression[callee.name='useEffect']))",
            //         message: 'Avoid calling setState directly in render. Use useEffect or event handlers to prevent infinite loops.'
            //     }
            // ]
        }
    },
    // Strict rules for TimesheetDetail components - Phase 3 implementation
    {
        files: ['src/components/TimesheetDetail/**/*.{ts,tsx}'],
        rules: {
            '@typescript-eslint/no-explicit-any': 'error',
            '@typescript-eslint/no-unsafe-assignment': 'error',
            '@typescript-eslint/no-unsafe-argument': 'error',
            '@typescript-eslint/no-unsafe-call': 'error',
            '@typescript-eslint/no-unsafe-member-access': 'error',
            '@typescript-eslint/no-unsafe-return': 'error',
            // Forbid any type assertions that bypass type safety
            '@typescript-eslint/consistent-type-assertions': [
                'error',
                {
                    assertionStyle: 'as',
                    objectLiteralTypeAssertions: 'never'
                }
            ],
            // Custom rule for fragment reference safety
            'no-restricted-syntax': [
                'error',
                {
                    selector: "TSAsExpression[typeAnnotation.typeName.name='any']",
                    message: 'Type assertion to "any" is forbidden in TimesheetDetail components. Fix the underlying type issue instead.'
                },
                {
                    selector: "TSAsExpression[expression.type='Identifier'][typeAnnotation.typeAnnotation.typeName.name=/Fragment\\$key/]",
                    message: 'Fragment reference casting detected. Ensure proper fragment composition instead of unsafe type assertions.'
                }
            ]
        }
    },
    // Override rules for test files
    {
        files: ['**/*.test.{ts,tsx}', '**/*.spec.{ts,tsx}', '**/__tests__/**'],
        rules: {
            'react/jsx-no-bind': 'off',
            'unicorn/no-new-array': 'off',
            'no-restricted-syntax': 'off' // Allow setState in test environments
        }
    }
);
