{"name": "eprlive-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "env-cmd -f .env.development vite --port 3001", "build": "relay-compiler  && tsc -b && vite build", "check": "node -e \"require('child_process').execSync('tsc -b', {stdio: 'inherit'}); console.log('\\u2705 TypeScript check completed successfully - no errors found!');\"", "type-check": "pnpm run check", "lint": "eslint .", "preview": "env-cmd -f .env.development vite preview", "clean": "rimraf dist .vite node_modules/.vite", "clean:all": "rimraf dist .vite node_modules", "start": "relay-compiler && env-cmd -f .env.development pnpm preview --port 3001", "staging": "relay-compiler && env-cmd -f .env.staging pnpm preview --port 3001", "prod": "relay-compiler && env-cmd -f .env.production pnpm preview --port 3001", "schema-refresh": "get-graphql-schema http://localhost:5100/graphql > ./src/relay/schema.graphql", "graphql:codegen": "graphql codegen", "graphql:coverage": "graphql coverage", "graphql:validate": "graphql validate", "relay": "relay-compiler", "relay:check-types": "node scripts/check-relay-types.js", "relay:sync-types": "node scripts/sync-relay-types.js", "postinstall": "pnpm relay:sync-types", "prepare": "pnpm exec lefthook install -f", "precommit": "pnpm relay:check-types", "test": "jest --config=jest.config.cjs", "test:run": "jest --config=jest.config.cjs", "test:all": "jest --config=jest.config.cjs", "test:ci": "jest --ci --config=jest.config.cjs", "test:coverage": "jest --coverage --config=jest.config.cjs", "test:unit": "jest --testPathPattern='.*\\.test\\.ts$' --config=jest.config.cjs", "test:unit:watch": "jest --watch --testPathPattern='.*\\.test\\.ts$' --config=jest.config.cjs", "test:integration": "jest --testPathPattern='.*\\.integration\\.test\\.tsx$' --config=jest.config.cjs", "test:integration:watch": "jest --watch --testPathPattern='.*\\.integration\\.test\\.tsx$' --config=jest.config.cjs", "test:upload": "jest --testPathPattern='TimesheetDetail/UploadTimeSheet' --config=jest.config.cjs", "test:upload:watch": "jest --watch --testPathPattern='TimesheetDetail/UploadTimeSheet' --config=jest.config.cjs", "test:update": "jest --updateSnapshot --config=jest.config.cjs", "test:performance": "jest --testPathPattern='.*\\.performance\\.test\\.tsx$' --config=jest.config.cjs --testTimeout=30000", "test:e2e": "jest --testPathPattern='.*\\.e2e\\.test\\.tsx$' --config=jest.config.cjs --testTimeout=30000", "lint:infinite-loops": "eslint src/ --ext .ts,.tsx --rule 'react/jsx-no-bind: warn' --rule 'react-hooks/exhaustive-deps: error'", "lint:fragments": "eslint src/ --ext .ts,.tsx --rule 'custom/relay-fragment-dependencies: error'", "lint:timesheet": "eslint src/components/TimesheetDetail/ src/context/TimesheetUIContext.tsx --ext .ts,.tsx", "lint:timesheet:core": "eslint src/components/TimesheetDetail/TimesheetDetail*.tsx src/components/TimesheetDetail/PayStub*.tsx src/components/TimesheetDetail/TimeSheet*.tsx src/context/TimesheetUIContext.tsx --ext .ts,.tsx", "lint:timesheet:fixed": "eslint src/context/TimesheetUIContext.tsx src/components/TimesheetDetail/TimesheetDetailView.tsx --ext .ts,.tsx", "lint:html-structure": "eslint src/ --ext .ts,.tsx --rule 'custom/react-html-structure-validation: error'", "validate:all": "pnpm relay && pnpm check && pnpm lint", "validate:generated": "node scripts/validate-generated-files.js", "prod-start": "pnpm build && pnpm start", "build:prod": "pnpm build", "prettier-format-all": "prettier --write \"{src,app}/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "prettier": "prettier --write", "prettier:check": "prettier --check \"{src,app}/**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@adobe/react-spectrum": "^3.41.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@graphql-codegen/cli": "^5.0.5", "@internationalized/date": "^3.8.0", "@lexical/html": "^0.31.0", "@lexical/link": "^0.31.0", "@lexical/list": "^0.31.0", "@lexical/react": "^0.31.0", "@lexical/rich-text": "^0.31.0", "@lexical/selection": "^0.31.0", "@lexical/utils": "^0.31.0", "@progress/kendo-data-query": "^1.7.1", "@progress/kendo-drawing": "^1.10.1", "@progress/kendo-font-icons": "^2.3.0", "@progress/kendo-licensing": "^1.5.1", "@progress/kendo-react-animation": "^4.14.1", "@progress/kendo-react-buttons": "^4.14.1", "@progress/kendo-react-common": "^4.14.1", "@progress/kendo-react-data-tools": "^4.14.1", "@progress/kendo-react-dateinputs": "^4.14.1", "@progress/kendo-react-dialogs": "^4.14.1", "@progress/kendo-react-dropdowns": "^4.14.1", "@progress/kendo-react-editor": "^4.14.1", "@progress/kendo-react-excel-export": "^4.14.1", "@progress/kendo-react-grid": "^4.14.1", "@progress/kendo-react-indicators": "^4.14.1", "@progress/kendo-react-inputs": "^4.14.1", "@progress/kendo-react-intl": "^4.14.1", "@progress/kendo-react-labels": "^4.14.1", "@progress/kendo-react-layout": "^4.14.1", "@progress/kendo-react-pdf": "^4.14.1", "@progress/kendo-react-popup": "^4.14.1", "@progress/kendo-react-progressbars": "^4.14.1", "@progress/kendo-react-tooltip": "^4.14.1", "@progress/kendo-react-treeview": "^4.14.1", "@progress/kendo-svg-icons": "^4.0.0", "@progress/kendo-theme-material": "^4.43.0", "@radix-ui/react-icons": "^1.3.2", "@react-spectrum/label": "^3.16.14", "@react-spectrum/listbox": "^3.15.0", "@react-spectrum/toast": "3.0.2", "@react-stately/collections": "^3.12.3", "@react-types/calendar": "^3.7.0", "@react-types/shared": "^3.29.0", "@spectrum-icons/illustrations": "^3.6.21", "@spectrum-icons/workflow": "^4.2.20", "@tanstack/react-table": "^8.21.3", "@telerik/kendo-intl": "^2.3.1", "@types/node": "22.15.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "eslint": "^9.26.0", "fast-xml-parser": "^5.2.1", "file-saver": "^2.0.5", "html-react-parser": "^5.2.3", "idb": "^8.0.3", "install": "^0.13.0", "js-cookie": "^3.0.5", "lexical": "^0.31.0", "libphonenumber-js": "^1.12.7", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "npm-check-updates": "^18.0.1", "papaparse": "^5.5.2", "react": "^19.1.0", "react-aria-components": "^1.8.0", "react-csv": "^2.2.2", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-relay": "^19.0.0", "react-router": "^7.5.3", "react-router-dom": "^7.5.3", "react-tooltip": "^5.28.1", "relay-runtime": "^19.0.0", "uuid": "^11.1.0", "uuid4": "^2.0.3", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.1", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.26.0", "@react-router/dev": "^7.5.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/babel__core": "^7.20.5", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/papaparse": "^5.3.15", "@types/react": "^19.1.2", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19.1.3", "@types/react-html-parser": "^2.0.7", "@types/react-relay": "18.2.1", "@types/relay-runtime": "19.0.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.9.0", "babel-plugin-relay": "^19.0.0", "babel-plugin-transform-vite-meta-env": "^1.0.3", "date-fns": "^4.1.0", "env-cmd": "^10.1.0", "eslint": "^9.26.0", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-relay": "^1.8.3", "eslint-plugin-unicorn": "^53.0.0", "glob": "^11.0.2", "globals": "^16.0.0", "graphql": "^16.11.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lefthook": "^1.11.14", "madge": "^8.0.0", "prettier": "^3.5.3", "relay-compiler": "^19.0.0", "relay-compiler-language-typescript": "^15.0.1", "relay-test-utils": "^19.0.0", "rimraf": "^6.0.1", "sass": "^1.87.0", "sharp": "^0.34.1", "svgo": "^3.3.2", "ts-jest": "^29.3.3", "ts-morph": "^26.0.0", "typescript": "5.8.3", "typescript-eslint": "^8.31.1", "vite": "^6.3.4", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-relay": "^2.1.0"}, "lint-staged": {"*.{ts,tsx}": ["pnpm relay", "pnpm check", "pnpm lint --fix --max-warnings=0"]}, "packageManager": "pnpm@10.10.0", "pnpm": {"overrides": {}}}