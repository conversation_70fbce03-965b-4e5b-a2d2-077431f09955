/**
 * Babel configuration specifically for Vite.
 * This configuration is used by the custom 'vite:relay-custom' plugin in vite.config.ts
 * and primarily ensures that files processed for Relay output ES modules compatible with Vite.
 */
module.exports = {
    presets: [
        [
            '@babel/preset-env',
            {
                targets: { esmodules: true }, // Target modern ES module environments
                modules: false // IMPORTANT: Prevents Babel from transforming ES module syntax (import/export) to CommonJS.
                               // Vite handles ES modules natively, so we want <PERSON><PERSON> to preserve them.
            }
        ],
        '@babel/preset-typescript', // For TypeScript syntax
        ['@babel/preset-react', { runtime: 'automatic' }] // For JSX and React-specific transformations
    ],
    plugins: [
        [
            '@babel/plugin-transform-runtime',
            {
                corejs: false, // Not using core-js for polyfills here, assuming Vite/environment handles it or it's done elsewhere.
                helpers: true, // Use Babel's external runtime helpers (e.g., _defineProperty) instead of inlining them.
                regenerator: true, // Use regenerator-runtime for async/await if needed (though modern targets often don't require it explicitly).
                useESModules: true, // IMPORTANT: Tells the plugin to import helpers from their ES module versions (e.g., '@babel/runtime/helpers/esm/defineProperty').
                                  // This is crucial for Vite's ESM workflow.
                absoluteRuntime: false // Helpers will be imported relative to @babel/runtime.
            }
        ],
        // Add babel-plugin-relay with schema option for consistency
        ['babel-plugin-relay', { schema: './src/relay/schema.graphql' }]
    ]
};
