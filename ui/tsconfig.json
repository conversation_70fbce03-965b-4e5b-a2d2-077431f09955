{
    "compilerOptions": {
        "target": "es5",
        "lib": [
            "dom",
            "dom.iterable",
            "esnext"
        ],
        "allowJs": true,
        "skipLibCheck": true,
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "noEmit": true,
        "esModuleInterop": true,
        "module": "esnext",
        "moduleResolution": "bundler",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "jsx": "preserve",
        "incremental": true,
        "baseUrl": ".",
        "paths": {
            "@components/*": [
                "./src/components/*"
            ],
            "@interfaces/*": [
                "src/interfaces/*"
            ],
            "@/lib": [
                "./src/lib/index"
            ],
            "@/lib/*": [
                "./src/lib/*"
            ],
            "@/errorHandling": [
                "./src/errorHandling/index"
            ],
            "@/errorHandling/*": [
                "./src/errorHandling/*"
            ],
            "@/*": [
                "./*"
            ],
            // TODO: remove – legacy generated-path migration
            "@/src/*": [
                "./src/*"
            ],
            "@/relay/*": [
                "./src/relay/*"
            ],
            "@ui/*": [
                "./src/components/UI/*"
            ],
            "react": [
                "./node_modules/@types/react"
            ]
        }
    },
    "include": [
        "**/*.ts",
        "**/*.tsx"
    ],
    "exclude": [
        "node_modules"
    ]
}
