import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { transformSync } from '@babel/core';

// Import central alias configuration
const aliases = require('./aliases.cjs');

// https://vite.dev/config/
export default defineConfig({
    optimizeDeps: {
        include: []
    },
    plugins: [
        react(),
        {
            name: 'vite:relay-custom',
            transform(src, id) {
                // Ensure this condition accurately captures ONLY the files you want Babel to process for Relay+React+TS
                if (id.match(/\.(tsx|jsx|ts|js)$/) && src.includes('graphql`')) {
                    const out = transformSync(src, {
                        plugins: [['babel-plugin-relay', { schema: './src/relay/schema.graphql' }]],
                        configFile: path.resolve(__dirname, 'babel.config.vite.cjs'),
                        code: true,
                        filename: id,
                        sourceMaps: true,
                        ast: false
                    });
                    if (!out?.code) {
                        throw new Error(`vite-plugin-relay: Failed to transform ${id}`);
                    }
                    return {
                        code: out.code,
                        map: out.map
                    };
                }
                return null;
            }
        },
        ViteImageOptimizer({
            jpeg: {
                quality: 75
            },
            jpg: {
                quality: 75
            },
            png: {
                quality: 80
            },
            svg: {
                plugins: [{ name: 'removeViewBox' }, { name: 'sortAttrs', params: { order: ['xmlnsOrder'] } }]
            }
        })
    ],
    base: '/e24/',
    server: {
        port: 3001,
        host: true,
        allowedHosts: ['.eprlive.com']
    },
    preview: {
        port: 3001
    },
    define: {
        'process.env': {},
        global: 'globalThis'
    },
    resolve: {
        alias: {
            // Canonical aliases from central configuration (loaded first to take precedence)
            ...Object.fromEntries(
                Object.entries(aliases).map(([key, value]) => [
                    key,
                    path.resolve(__dirname, value as string)
                ])
            ),
            // Legacy aliases (kept for backward compatibility)
            '@components': path.resolve(__dirname, './src/components'),
            '@interfaces': path.resolve(__dirname, './src/interfaces'),
            '@ui': path.resolve(__dirname, './src/components/UI'),
            '@': path.resolve(__dirname, './'),
            '@/src': path.resolve(__dirname, './src')
        }
    },
    build: {
        minify: true,
        cssMinify: true,
        cssCodeSplit: true,
        chunkSizeWarningLimit: 600,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['react', 'react-dom', 'react-router', 'react-relay', 'relay-runtime', 'react-csv', 'axios', 'js-cookie'],
                    lodash: ['lodash']
                }
            }
        }
    }
});
