const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Verifying Timesheet Enhancements...\n');

const verifications = [
  {
    name: 'Dependency Management',
    check: verifyDependencyManagement
  },
  {
    name: '<PERSON>rro<PERSON>ling',
    check: verifyErrorHandling
  },
  {
    name: 'Upload Migration',
    check: verifyUploadMigration
  },
  {
    name: 'Performance Infrastructure',
    check: verifyPerformanceInfrastructure
  },
  {
    name: 'Code Quality',
    check: verifyCodeQuality
  }
];

async function runVerifications() {
  let allPassed = true;

  for (const verification of verifications) {
    console.log(`\n🔧 Checking ${verification.name}...`);
    
    try {
      const result = await verification.check();
      if (result.success) {
        console.log(`✅ ${verification.name}: PASSED`);
        if (result.details) {
          result.details.forEach(detail => console.log(`   ${detail}`));
        }
      } else {
        console.log(`❌ ${verification.name}: FAILED`);
        if (result.errors) {
          result.errors.forEach(error => console.log(`   ERROR: ${error}`));
        }
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${verification.name}: ERROR - ${error.message}`);
      allPassed = false;
    }
  }

  console.log(`\n${allPassed ? '🎉' : '💥'} Overall Status: ${allPassed ? 'PASSED' : 'FAILED'}`);
  
  if (!allPassed) {
    console.log('\n📋 Fix the failing verifications before proceeding with deployment.');
    process.exit(1);
  }

  console.log('\n✨ All enhancements verified successfully! Ready for deployment.');
}

function verifyDependencyManagement() {
  const errors = [];
  const details = [];

  // Check for required scripts
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredScripts = ['relay:check-types', 'relay:sync-types'];
  
  requiredScripts.forEach(script => {
    if (!packageJson.scripts[script]) {
      errors.push(`Missing script: ${script}`);
    } else {
      details.push(`✓ Script found: ${script}`);
    }
  });

  // Check for required files
  const requiredFiles = [
    'scripts/check-relay-types.js',
    'scripts/sync-relay-types.js'
  ];

  requiredFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      errors.push(`Missing file: ${file}`);
    } else {
      details.push(`✓ File exists: ${file}`);
    }
  });

  // Test type checking - only if scripts exist
  if (packageJson.scripts['relay:check-types']) {
    try {
      execSync('pnpm relay:check-types', { stdio: 'pipe' });
      details.push('✓ Type checking script functional');
    } catch (error) {
      errors.push('Type checking script failed');
    }
  }

  return { success: errors.length === 0, errors, details };
}

function verifyErrorHandling() {
  const errors = [];
  const details = [];

  // Check for required files
  const requiredFiles = [
    'src/utils/error-handling.ts',
    'src/hooks/useErrorRecovery.ts',
    'src/components/ErrorBoundary/TimesheetErrorBoundary.tsx',
    'src/components/UI/ErrorDisplay.tsx'
  ];

  requiredFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      errors.push(`Missing file: ${file}`);
    } else {
      details.push(`✓ File exists: ${file}`);
    }
  });

  // Check for enhanced error handling in context
  const contextFile = 'src/context/TimesheetUIContext.tsx';
  if (fs.existsSync(contextFile)) {
    const content = fs.readFileSync(contextFile, 'utf8');
    if (content.includes('enhancedErrors') || content.includes('errorRecovery')) {
      details.push('✓ Enhanced error handling in context');
    } else {
      errors.push('Enhanced error handling not found in context');
    }
  } else {
    errors.push('TimesheetUIContext.tsx not found');
  }

  return { success: errors.length === 0, errors, details };
}

function verifyUploadMigration() {
  const errors = [];
  const details = [];

  // Check that ModifiablePayStub references are removed
  try {
    const grepResult = execSync('grep -r "ModifiablePayStub" src/ || true', { encoding: 'utf8' });
    const remainingRefs = grepResult.trim().split('\n').filter(line => 
      line && !line.includes('// TODO:') && !line.includes('test') && !line.includes('.test.')
    );

    if (remainingRefs.length > 0) {
      errors.push(`ModifiablePayStub references still exist: ${remainingRefs.length}`);
      remainingRefs.forEach(ref => errors.push(`  - ${ref}`));
    } else {
      details.push('✓ All ModifiablePayStub references removed');
    }
  } catch (error) {
    details.push('✓ No ModifiablePayStub references found');
  }

  // Check for fragment-based upload components
  const uploadFile = 'src/components/TimesheetDetail/UploadTimeSheet/UploadTimeSheet.tsx';
  if (fs.existsSync(uploadFile)) {
    const content = fs.readFileSync(uploadFile, 'utf8');
    if (content.includes('useFragment') && content.includes('bulkAddPayStubs')) {
      details.push('✓ Upload component uses fragments and bulk mutations');
    } else {
      errors.push('Upload component not properly migrated to fragments');
    }
  } else {
    errors.push('Upload component file not found');
  }

  // Check for fragment files
  const fragmentsDir = 'src/components/TimesheetDetail/UploadTimeSheet/fragments';
  if (fs.existsSync(fragmentsDir)) {
    details.push('✓ Upload fragments directory exists');
  } else {
    errors.push('Upload fragments directory not found');
  }

  return { success: errors.length === 0, errors, details };
}

function verifyPerformanceInfrastructure() {
  const errors = [];
  const details = [];

  // Check for performance monitoring files
  const requiredFiles = [
    'src/utils/performance-monitor.ts',
    'src/hooks/usePerformanceMonitor.ts',
    'src/test-utils/performance-helpers.ts'
  ];

  requiredFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      errors.push(`Missing file: ${file}`);
    } else {
      details.push(`✓ File exists: ${file}`);
    }
  });

  // Check for performance tests
  const performanceTestFile = 'src/components/TimesheetDetail/__tests__/performance.test.tsx';
  if (fs.existsSync(performanceTestFile)) {
    details.push('✓ Performance tests exist');
  } else {
    errors.push('Performance tests not found');
  }

  // Check for performance scripts
  const performanceScript = 'scripts/run-performance-tests.js';
  if (fs.existsSync(performanceScript)) {
    details.push('✓ Performance test script exists');
  } else {
    errors.push('Performance test script not found');
  }

  return { success: errors.length === 0, errors, details };
}

function verifyCodeQuality() {
  const errors = [];
  const details = [];

  // Run TypeScript check
  try {
    execSync('pnpm relay', { stdio: 'pipe' });
    details.push('✓ Relay compilation successful');
  } catch (error) {
    errors.push('Relay compilation failed');
  }

  try {
    execSync('pnpm check', { stdio: 'pipe' });
    details.push('✓ TypeScript compilation successful');
  } catch (error) {
    errors.push('TypeScript compilation failed');
  }

  // Check for E2E test
  const e2eTestFile = 'src/components/TimesheetDetail/__tests__/e2e-complete-enhanced-flow.test.tsx';
  if (fs.existsSync(e2eTestFile)) {
    details.push('✓ E2E integration test exists');
  } else {
    errors.push('E2E integration test not found');
  }

  return { success: errors.length === 0, errors, details };
}

// Additional helper functions for specific verifications

function verifyRelayFragments() {
  const errors = [];
  const details = [];

  try {
    // Check for generated fragment types
    const generatedDir = 'src/relay/__generated__';
    if (fs.existsSync(generatedDir)) {
      const fragmentFiles = fs.readdirSync(generatedDir).filter(file => 
        file.includes('Fragment') && file.endsWith('.graphql.ts')
      );
      
      if (fragmentFiles.length > 0) {
        details.push(`✓ Found ${fragmentFiles.length} generated fragment files`);
      } else {
        errors.push('No fragment files found in generated directory');
      }
    } else {
      errors.push('Generated Relay types directory not found');
    }
  } catch (error) {
    errors.push(`Error checking fragments: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function verifyTestCoverage() {
  const errors = [];
  const details = [];

  // Check for comprehensive test coverage
  const testDirs = [
    'src/components/TimesheetDetail/__tests__',
    'src/utils/__tests__',
    'src/hooks/__tests__'
  ];

  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      const testFiles = fs.readdirSync(dir).filter(file => file.endsWith('.test.tsx') || file.endsWith('.test.ts'));
      details.push(`✓ Found ${testFiles.length} test files in ${dir}`);
    }
  });

  return { success: errors.length === 0, errors, details };
}

// Enhanced verification with additional checks
async function runEnhancedVerifications() {
  console.log('\n🔍 Running Enhanced Verifications...\n');

  const enhancedChecks = [
    { name: 'Relay Fragments', check: verifyRelayFragments },
    { name: 'Test Coverage', check: verifyTestCoverage }
  ];

  for (const check of enhancedChecks) {
    console.log(`\n🔧 Checking ${check.name}...`);
    
    try {
      const result = await check.check();
      if (result.success) {
        console.log(`✅ ${check.name}: PASSED`);
        if (result.details) {
          result.details.forEach(detail => console.log(`   ${detail}`));
        }
      } else {
        console.log(`⚠️  ${check.name}: ISSUES FOUND`);
        if (result.errors) {
          result.errors.forEach(error => console.log(`   WARNING: ${error}`));
        }
      }
    } catch (error) {
      console.log(`❌ ${check.name}: ERROR - ${error.message}`);
    }
  }
}

// Main execution
async function main() {
  try {
    await runVerifications();
    await runEnhancedVerifications();
    
    console.log('\n📊 Verification Summary:');
    console.log('   - Dependency Management: Automated');
    console.log('   - Error Handling: Enhanced');
    console.log('   - Upload Migration: Fragment-based');
    console.log('   - Performance: Monitored');
    console.log('   - Code Quality: Validated');
    console.log('\n🚀 System ready for deployment!');
    
  } catch (error) {
    console.error('\n💥 Verification failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Verification script error:', error);
    process.exit(1);
  });
}

module.exports = {
  runVerifications,
  verifyDependencyManagement,
  verifyErrorHandling,
  verifyUploadMigration,
  verifyPerformanceInfrastructure,
  verifyCodeQuality
};