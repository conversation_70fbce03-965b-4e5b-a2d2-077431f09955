const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Pre-Deployment Check - Timesheet Enhancements\n');

// Pre-deployment checklist items
const deploymentChecks = [
  {
    name: 'Build System',
    check: verifyBuildSystem,
    critical: true
  },
  {
    name: 'Test Suite',
    check: verifyTestSuite,
    critical: true
  },
  {
    name: 'Performance Budgets',
    check: verifyPerformanceBudgets,
    critical: true
  },
  {
    name: 'Security Scan',
    check: verifySecurityScan,
    critical: false
  },
  {
    name: 'Documentation',
    check: verifyDocumentation,
    critical: false
  },
  {
    name: 'Environment Configuration',
    check: verifyEnvironmentConfig,
    critical: true
  },
  {
    name: 'Database Compatibility',
    check: verifyDatabaseCompatibility,
    critical: true
  },
  {
    name: 'API Compatibility',
    check: verifyApiCompatibility,
    critical: true
  },
  {
    name: 'Type Safety (Unsafe Casts)',
    check: checkForUnsafeTypeCasting,
    critical: true
  }
];

async function runPreDeploymentChecks() {
  let criticalFailures = 0;
  let warnings = 0;
  const results = [];

  console.log('🔍 Running Pre-Deployment Verification...\n');

  for (const check of deploymentChecks) {
    console.log(`Checking ${check.name}...`);
    
    try {
      const result = await check.check();
      results.push({ name: check.name, ...result, critical: check.critical });
      
      if (result.success) {
        console.log(`✅ ${check.name}: PASSED`);
        if (result.details) {
          result.details.forEach(detail => console.log(`   ${detail}`));
        }
      } else {
        if (check.critical) {
          criticalFailures++;
          console.log(`❌ ${check.name}: CRITICAL FAILURE`);
        } else {
          warnings++;
          console.log(`⚠️  ${check.name}: WARNING`);
        }
        if (result.errors) {
          result.errors.forEach(error => console.log(`   ${error}`));
        }
      }
    } catch (error) {
      if (check.critical) {
        criticalFailures++;
        console.log(`❌ ${check.name}: CRITICAL ERROR - ${error.message}`);
      } else {
        warnings++;
        console.log(`⚠️  ${check.name}: ERROR - ${error.message}`);
      }
      results.push({ 
        name: check.name, 
        success: false, 
        errors: [error.message], 
        critical: check.critical 
      });
    }
    
    console.log(''); // Add spacing
  }

  // Generate deployment report
  generateDeploymentReport(results, criticalFailures, warnings);

  // Final decision
  if (criticalFailures > 0) {
    console.log(`💥 DEPLOYMENT BLOCKED: ${criticalFailures} critical failures detected`);
    console.log('🛑 Fix critical issues before proceeding with deployment.');
    process.exit(1);
  } else {
    console.log(`🎉 DEPLOYMENT APPROVED: All critical checks passed`);
    if (warnings > 0) {
      console.log(`⚠️  Note: ${warnings} non-critical warnings detected`);
    }
    console.log('✅ System ready for production deployment!');
  }
}

function verifyBuildSystem() {
  const errors = [];
  const details = [];

  try {
    // Test Relay compilation
    console.log('   Running Relay compiler...');
    execSync('pnpm relay', { stdio: 'pipe' });
    details.push('✓ Relay compilation successful');

    // Test TypeScript compilation
    console.log('   Running TypeScript compiler...');
    execSync('pnpm check', { stdio: 'pipe' });
    details.push('✓ TypeScript compilation successful');

    // Test build process
    console.log('   Testing build process...');
    execSync('pnpm build', { stdio: 'pipe' });
    details.push('✓ Production build successful');

    // Verify build artifacts
    const distDir = 'dist';
    if (fs.existsSync(distDir)) {
      const buildFiles = fs.readdirSync(distDir);
      if (buildFiles.length > 0) {
        details.push(`✓ Build artifacts generated (${buildFiles.length} files)`);
      } else {
        errors.push('Build artifacts directory is empty');
      }
    } else {
      errors.push('Build artifacts directory not found');
    }

  } catch (error) {
    errors.push(`Build process failed: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function verifyTestSuite() {
  const errors = [];
  const details = [];

  try {
    console.log('   Running complete test suite...');
    
    // Run all tests with coverage
    const testOutput = execSync('pnpm test:all --coverage --watchAll=false', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });

    details.push('✓ All tests passed');

    // Check for specific critical tests
    const criticalTests = [
      'e2e-complete-enhanced-flow.test.tsx',
      'performance.test.tsx',
      'accessibility.test.tsx'
    ];

    criticalTests.forEach(test => {
      if (testOutput.includes(test)) {
        details.push(`✓ Critical test found: ${test}`);
      } else {
        errors.push(`Critical test missing or failed: ${test}`);
      }
    });

    // Verify no fragment errors in test output
    if (testOutput.includes('fragment reference')) {
      errors.push('Fragment reference errors detected in test output');
    } else {
      details.push('✓ No fragment reference errors in tests');
    }

  } catch (error) {
    errors.push(`Test suite failed: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function verifyPerformanceBudgets() {
  const errors = [];
  const details = [];

  try {
    // Run performance tests
    console.log('   Checking performance budgets...');
    
    if (fs.existsSync('scripts/run-performance-tests.js')) {
      const perfOutput = execSync('node scripts/run-performance-tests.js', { 
        stdio: 'pipe',
        encoding: 'utf8'
      });

      if (perfOutput.includes('All budgets met')) {
        details.push('✓ All performance budgets met');
      } else if (perfOutput.includes('Budget exceeded')) {
        errors.push('Performance budgets exceeded');
      } else {
        details.push('✓ Performance tests completed');
      }
    } else {
      errors.push('Performance test script not found');
    }

    // Check bundle size
    const bundleFile = 'dist/assets/index-*.js';
    try {
      const bundleStats = execSync(`ls -la ${bundleFile}`, { encoding: 'utf8' });
      const sizeMB = parseInt(bundleStats.split(' ')[4]) / (1024 * 1024);
      
      if (sizeMB > 2) { // 2MB budget
        errors.push(`Bundle size too large: ${sizeMB.toFixed(2)}MB (budget: 2MB)`);
      } else {
        details.push(`✓ Bundle size within budget: ${sizeMB.toFixed(2)}MB`);
      }
    } catch (error) {
      // Bundle file check is optional if build hasn't run
      details.push('Bundle size check skipped (build artifacts not found)');
    }

  } catch (error) {
    errors.push(`Performance check failed: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function verifySecurityScan() {
  const errors = [];
  const details = [];

  try {
    // Check for security vulnerabilities
    console.log('   Running security audit...');
    
    const auditOutput = execSync('pnpm audit --audit-level moderate', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });

    if (auditOutput.includes('0 vulnerabilities')) {
      details.push('✓ No security vulnerabilities found');
    } else {
      // Parse audit output for severity
      if (auditOutput.includes('high') || auditOutput.includes('critical')) {
        errors.push('High or critical security vulnerabilities found');
      } else {
        details.push('⚠️ Low/moderate vulnerabilities found (acceptable)');
      }
    }

  } catch (error) {
    // Audit command may exit with non-zero for vulnerabilities
    if (error.message.includes('high') || error.message.includes('critical')) {
      errors.push('Critical security vulnerabilities detected');
    } else {
      details.push('✓ Security audit completed');
    }
  }

  return { success: errors.length === 0, errors, details };
}

function verifyDocumentation() {
  const errors = [];
  const details = [];

  // Check for required documentation
  const requiredDocs = [
    '.ai-workflow/timesheet-enhancements/release-checklist.md',
    '.ai-workflow/timesheet-enhancements/rollback-plan.md',
    'ui/docs/TIMESHEET-ARCHITECTURE.md',
    'CLAUDE.md'
  ];

  requiredDocs.forEach(doc => {
    if (fs.existsSync(doc)) {
      details.push(`✓ Documentation exists: ${doc}`);
    } else {
      errors.push(`Missing documentation: ${doc}`);
    }
  });

  // Check README updates
  if (fs.existsSync('README.md')) {
    const readme = fs.readFileSync('README.md', 'utf8');
    if (readme.includes('timesheet') || readme.includes('enhancement')) {
      details.push('✓ README includes timesheet information');
    } else {
      errors.push('README should be updated with timesheet enhancement details');
    }
  }

  return { success: errors.length === 0, errors, details };
}

function verifyEnvironmentConfig() {
  const errors = [];
  const details = [];

  // Check for environment configuration files
  const configFiles = [
    'vite.config.ts',
    'package.json',
    'tsconfig.json'
  ];

  configFiles.forEach(file => {
    if (fs.existsSync(file)) {
      details.push(`✓ Configuration file exists: ${file}`);
    } else {
      errors.push(`Missing configuration file: ${file}`);
    }
  });

  // Verify package.json scripts
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredScripts = ['build', 'test', 'relay', 'check'];
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts[script]) {
      details.push(`✓ Script configured: ${script}`);
    } else {
      errors.push(`Missing required script: ${script}`);
    }
  });

  return { success: errors.length === 0, errors, details };
}

function verifyDatabaseCompatibility() {
  const errors = [];
  const details = [];

  // Check that no database migrations are required for frontend changes
  // This is primarily a frontend enhancement
  details.push('✓ No database changes required for frontend enhancements');

  // Verify GraphQL schema compatibility
  if (fs.existsSync('src/relay/__generated__/schema.graphql')) {
    details.push('✓ GraphQL schema file exists');
  } else {
    errors.push('GraphQL schema file not found');
  }

  // Check for any database-related dependencies
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.dependencies && !Object.keys(packageJson.dependencies).some(dep => 
    dep.includes('sequelize') || dep.includes('prisma') || dep.includes('mongoose')
  )) {
    details.push('✓ No direct database dependencies in frontend');
  }

  return { success: errors.length === 0, errors, details };
}

function verifyApiCompatibility() {
  const errors = [];
  const details = [];

  // Check that GraphQL operations are valid
  try {
    // Verify generated types exist
    const generatedDir = 'src/relay/__generated__';
    if (fs.existsSync(generatedDir)) {
      const generatedFiles = fs.readdirSync(generatedDir);
      const mutationFiles = generatedFiles.filter(file => file.includes('Mutation'));
      const queryFiles = generatedFiles.filter(file => file.includes('Query'));
      
      details.push(`✓ Generated mutations: ${mutationFiles.length}`);
      details.push(`✓ Generated queries: ${queryFiles.length}`);
    } else {
      errors.push('Generated Relay types directory not found');
    }

    // Check for critical mutations
    const criticalMutations = [
      'BulkAddPayStubsMutation',
      'ModifyTimeSheetMutation'
    ];

    criticalMutations.forEach(mutation => {
      const mutationFile = `src/mutations/timesheet/${mutation}.ts`;
      if (fs.existsSync(mutationFile)) {
        details.push(`✓ Critical mutation exists: ${mutation}`);
      } else {
        errors.push(`Missing critical mutation: ${mutation}`);
      }
    });

  } catch (error) {
    errors.push(`API compatibility check failed: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function checkForUnsafeTypeCasting() {
  const errors = [];
  const details = [];

  try {
    console.log('   Checking for unsafe type casts in TimesheetDetail components...');
    
    const timesheetDetailPath = path.join(__dirname, '../src/components/TimesheetDetail');
    
    if (!fs.existsSync(timesheetDetailPath)) {
      errors.push('TimesheetDetail components directory not found');
      return { success: false, errors, details };
    }
    
    const files = fs.readdirSync(timesheetDetailPath, { recursive: true })
      .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
      .filter(file => !file.includes('.test.') && !file.includes('.spec.'));

    let totalViolations = 0;
    let filesWithViolations = 0;

    files.forEach(file => {
      const filePath = path.join(timesheetDetailPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      let fileViolations = 0;

      lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        
        // Check for unsafe type casts (excluding commented lines)
        if (!trimmedLine.startsWith('//') && !trimmedLine.startsWith('/*')) {
          // Check for "as any" usage
          if (trimmedLine.includes(' as any')) {
            errors.push(`${file}:${index + 1}: Unsafe 'as any' cast: ${trimmedLine}`);
            fileViolations++;
            totalViolations++;
          }
          
          // Check for fragment reference casting
          if (trimmedLine.includes('as unknown as') && trimmedLine.includes('$key')) {
            errors.push(`${file}:${index + 1}: Unsafe fragment reference cast: ${trimmedLine}`);
            fileViolations++;
            totalViolations++;
          }
          
          // Check for unsafe assignment patterns
          if (trimmedLine.includes('as ') && 
              (trimmedLine.includes('Fragment$key') || trimmedLine.includes('Fragment$data'))) {
            errors.push(`${file}:${index + 1}: Unsafe Relay type cast: ${trimmedLine}`);
            fileViolations++;
            totalViolations++;
          }
        }
      });

      if (fileViolations > 0) {
        filesWithViolations++;
      } else {
        details.push(`✓ ${file}: No unsafe casts found`);
      }
    });

    if (totalViolations === 0) {
      details.push(`✓ All ${files.length} TimesheetDetail components are type-safe`);
      details.push('✓ No unsafe type casts (as any) detected');
      details.push('✓ No unsafe fragment reference casts detected');
    } else {
      errors.unshift(`Found ${totalViolations} unsafe type casts in ${filesWithViolations} files`);
    }

    // Additional check for fragment reference errors in generated types
    const generatedTypesPath = path.join(__dirname, '../src/relay/__generated__');
    if (fs.existsSync(generatedTypesPath)) {
      details.push('✓ Generated Relay types directory exists');
    } else {
      errors.push('Generated Relay types directory not found - run pnpm relay');
    }

  } catch (error) {
    errors.push(`Type safety check failed: ${error.message}`);
  }

  return { success: errors.length === 0, errors, details };
}

function generateDeploymentReport(results, criticalFailures, warnings) {
  const timestamp = new Date().toISOString();
  const reportPath = `deployment-report-${timestamp.split('T')[0]}.md`;

  const report = `# Pre-Deployment Report - Timesheet Enhancements

**Generated:** ${timestamp}
**Status:** ${criticalFailures === 0 ? 'APPROVED' : 'BLOCKED'}

## Summary
- **Critical Failures:** ${criticalFailures}
- **Warnings:** ${warnings}
- **Total Checks:** ${results.length}

## Check Results

${results.map(result => `
### ${result.name} ${result.critical ? '(Critical)' : '(Optional)'}
**Status:** ${result.success ? '✅ PASSED' : result.critical ? '❌ FAILED' : '⚠️ WARNING'}

${result.details ? result.details.map(detail => `- ${detail}`).join('\n') : ''}
${result.errors ? result.errors.map(error => `- ❌ ${error}`).join('\n') : ''}
`).join('\n')}

## Deployment Decision
${criticalFailures === 0 ? 
  '✅ **APPROVED FOR DEPLOYMENT**' : 
  '❌ **DEPLOYMENT BLOCKED** - Fix critical issues before proceeding'
}

${warnings > 0 ? `⚠️ **Note:** ${warnings} non-critical warnings should be addressed in future releases.` : ''}

## Next Steps
${criticalFailures === 0 ? 
  `1. Proceed with deployment
2. Monitor post-deployment metrics
3. Address any warnings in future releases` :
  `1. Fix critical failures listed above
2. Re-run pre-deployment checks
3. Ensure all critical checks pass before deployment`
}

---
*Generated by pre-deployment-check.js*
`;

  fs.writeFileSync(reportPath, report);
  console.log(`\n📋 Deployment report generated: ${reportPath}`);
}

// Main execution
if (require.main === module) {
  runPreDeploymentChecks().catch(error => {
    console.error('Pre-deployment check error:', error);
    process.exit(1);
  });
}

module.exports = {
  runPreDeploymentChecks,
  verifyBuildSystem,
  verifyTestSuite,
  verifyPerformanceBudgets,
  verifySecurityScan,
  verifyDocumentation,
  verifyEnvironmentConfig,
  verifyDatabaseCompatibility,
  verifyApiCompatibility
};