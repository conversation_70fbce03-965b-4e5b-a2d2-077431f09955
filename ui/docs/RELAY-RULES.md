# Relay Code Review & Check-in Rules

## Purpose

To ensure consistency, maintainability, and type safety in our Relay codebase, all developers submitting or reviewing code involving Relay fragments, hooks, queries, or mutations must adhere to the following rules. These are based on Relay best practices and lessons learned.

## Mandatory Rules

1. **Relay Compiler Execution:**

    - The Relay compiler (`pnpm relay`) **MUST** be run successfully after any modification to `graphql` tags (fragments, queries, mutations) or the GraphQL schema.
    - Generated `__generated__` files **MUST** be included in the commit/Pull Request.
    - _Reviewer Check:_ Verify generated files correspond to GraphQL changes.

2. **Fragment Naming Convention:**

    - All fragments defined using the `graphql` tag **MUST** follow the convention: **`<FileName>_<fragmentName>`**.
    - _Reviewer Check:_ Verify fragment names match the file they are defined in.

3. **Fragment Colocation:**

    - Fragments **MUST** be defined in the same file as the React component that primarily uses them via `useFragment`.

4. **`useFragment` Usage:**

    - The `useFragment` hook **MUST** be called unconditionally at the top level of the component function body (cannot be inside `if`, loops, or nested functions).
    - `useFragment` **MUST** be called with the fragment definition corresponding to the component's direct data needs.
    - If a fragment reference prop can be `null` or `undefined`, **still call** `useFragment` on it unconditionally. The hook gracefully returns `null` for a missing key. **Never** put `useFragment` inside a conditional; instead, call it at top level and then conditionally render fallback UI based on its **result** (check if the returned data is `null`).

5. **Fragment Composition & Data Flow:**

    - A parent component's fragment **MUST** spread the fragments of any direct child components it renders _and passes Relay data to_.
    - A parent component **MUST** resolve its _own_ fragment using `useFragment`.
    - The **resolved data object** from the parent's `useFragment` call **MUST** be passed as the fragment reference prop to the child component.
    - Type casting fragment references (`someRef as unknown as Target$key`) is **STRICTLY FORBIDDEN**. The need for such a cast indicates an error in fragment composition or data flow that must be fixed.
    - _Reviewer Check:_ Trace the data flow between parent and child Relay components to ensure the **chained `useFragment` pattern** is followed. Verify that the parent fragment spreads the child's fragment and passes its own resolved data object (not the parent's `$key`) down to the child. Ensure no `$key` casts are present.
    - Fragment references (`$key` types) are opaque. **DO NOT** attempt to access data properties (e.g., `.id`, `.name`) directly on a fragment reference before it has been resolved by `useFragment`. Filtering or sorting lists of fragment references based on unresolved data properties within a parent component is **FORBIDDEN**. Pass the reference to a child component for resolution.

6. **List Fragment Spreads:**

    - When spreading a fragment onto a field that represents a list/array, the `@relay(plural: true)` directive **MUST** be used. (e.g., `listOfItems { ...ItemFragment @relay(plural: true) }`).
    - _Reviewer Check:_ Look for fragment spreads on list fields and verify the directive is present.

7. **Type Safety:**

    - Generated Relay types (`...$key`, `...$data`, mutation inputs/outputs, etc.) **MUST** be used.
    - Using `any` for Relay-specific types (fragment refs, resolved data, mutation variables/responses) is **FORBIDDEN**.

8. **Type Definitions & Inference:**

    - Relay v19 no longer includes built-in TypeScript type declarations. You **MUST** explicitly install and use the matching type definition packages (e.g., `@types/react-relay` and `@types/relay-runtime`). Ensure these packages' versions align with the Relay release (for Relay 19, use `@types/react-relay@18.2.1` and `@types/relay-runtime@19.0.1`).
    - **Type inference in `useFragment`:** In some cases, TypeScript cannot infer the fragment's data type automatically (for example, when passing a fragment reference that could be `null` or one obtained from another fragment's data). If you encounter an `unknown` or empty object type from a `useFragment` call, provide the fragment reference type as a generic parameter. **Example:** `useFragment<ChildFragment$key>(ChildFragment, parentData.childRef)`.
    - **Handling nullable fragment refs:** If a fragment reference prop is optional or `null`, call `useFragment` with it regardless (per Rule 4). The hook returns `null` for a null input. Use the hook's return in your conditional logic rather than gating the hook call. If TypeScript still complains after you check for `null`, you may use a type assertion **after** the runtime check to tell the compiler the data is present. For instance:

        ```typescript
        const data = useFragment(SomeFragment, props.maybeRef);
        if (!data) return null;
        const safeData = data as SomeFragment$data;
        ```

        This assertion is only acceptable immediately after a null-check. Include a comment and a `TODO` to remove it once improved types or patterns eliminate the need.

    - **No unsafe casting:** Under no circumstances should you cast fragment references to arbitrary types (especially not using `as unknown as`). Such casts mask real issues and violate Relay's type safety (see Pitfall 5). Always address type errors by fixing fragment composition or by providing correct type information, **not** by forcing a cast.
    - _Reviewer Check:_ Verify the project includes the appropriate Relay type definition packages. Check any `useFragment` usage with potentially ambiguous types (nested fragments or nullable references) for proper generic parameters or safe handling. **Ensure that any type assertions used after runtime checks are clearly commented as temporary workarounds.** Flag any improper type casts (e.g., usage of `as unknown as` or `any`) as errors.

9. **Global Data Dependencies & Query Consolidation:**

    - **Understand the global context providers:** Our application uses `RelayEnvironmentLayout` which provides both the Relay environment (`RelayEnvironmentProvider`) and global field metadata (`FieldMetadataProvider`). These are essential for all Relay operations and many components.
    - **Do NOT bypass RelayEnvironmentLayout:** Components requiring Relay functionality **MUST** be descendants of `RelayEnvironmentLayout`. Creating separate route layouts to bypass it will break Relay functionality.
    - **Avoid duplicate data fetching:** Before adding new queries, check if the data is already available through existing global providers:
      - **Field definitions:** Available globally via `FieldMetadataContext` from `RelayEnvironmentLayout`
      - **User data:** May be available through global user stores/contexts
    - **When consolidating queries is appropriate:** Only consolidate queries when:
      - You need the same data in the same component
      - The data has the same lifecycle (loaded/updated together)
      - It reduces network requests without creating inappropriate coupling
    - **When NOT to consolidate:** Do not consolidate queries if:
      - It would bypass existing global providers
      - The data has different access patterns or update frequencies
      - It creates tight coupling between unrelated data domains
    - **Best practice for component-specific data:** Use `useLazyLoadQuery` or `useQueryLoader` for component-specific data while relying on global providers for shared resources like field definitions, user data, or application configuration.
    - **Consolidation implementation pattern:** When consolidating REST APIs into GraphQL global queries:
      1. Add the GraphQL query fields to `RelayEnvironmentLayoutQuery`
      2. Create a new context provider (following the `FieldMetadataProvider` pattern)
      3. Process GraphQL data in `RelayEnvironmentLayout` and provide through context
      4. Update components to use the context instead of REST calls
      5. Maintain existing store structure for backward compatibility during transition
    - **Example:** The migration from `/api/UserInfo` REST calls to GraphQL `userInfo` query demonstrates this pattern. User data is now fetched globally in `RelayEnvironmentLayoutQuery` alongside field definitions, provided via `UserInfoProvider` context, and consumed by Layout and other components instead of making duplicate REST calls.
    - _Reviewer Check:_ Verify that new queries don't duplicate data already available through global providers. Ensure components properly utilize existing context providers rather than fetching the same data independently. For GraphQL consolidation, verify that REST calls are removed and replaced with context usage.

10. **Schema Synchronization:**

    - If changes depend on backend GraphQL schema updates, the updated `schema.graphql` (or equivalent) **MUST** be included in the commit/Pull Request.

11. **List Mutation Patterns - Mandatory Connection Usage:**

    **Scenario**: When implementing mutations that add, remove, or modify items in a list (e.g., adding PayStubs to a TimeSheet, adding Timesheets to a roster, adding items to any collection).

    **MANDATORY REQUIREMENT**:

    - **Backend MUST return edge-based payloads** using proper Relay edge types (`SomeEntityEdge`) rather than returning the full parent object with the complete list.
    - **Frontend MUST model lists as Relay connections** using `@connection` directive with proper connection keys.
    - **Frontend MUST use Relay's built-in directives** (`@prependEdge`, `@appendEdge`, `@deleteEdge`) for list mutations rather than manual ConnectionHandler operations.
    - **Optimistic updates MUST use `optimisticResponse`** with proper edge structure rather than manual store manipulation.

    **FORBIDDEN APPROACHES**:
    - ❌ Returning full parent objects with complete child lists from mutations
    - ❌ Manual `setLinkedRecords()` operations for list updates
    - ❌ Using `updater` functions for simple list additions/removals when Relay directives exist
    - ❌ "Expedient" solutions that bypass proper Relay connection patterns

    **CORRECT PATTERN**:

    **Backend Payload**:
    ```csharp
    public class AddItemPayload
    {
        public ItemEdge? ItemEdge { get; }  // ✅ Return edge, not full parent
        public IEnumerable<string>? Errors { get; }
    }
    ```

    **Frontend Fragment**:
    ```graphql
    fragment Parent_items on Parent
    @argumentDefinitions(first: { type: "Int", defaultValue: 500 }) {
        items(first: $first) @connection(key: "Parent_items") {  # ✅ Use @connection
            edges {
                node {
                    id
                    ...Item_data
                }
            }
        }
    }
    ```

    **Frontend Mutation**:
    ```graphql
    mutation AddItemMutation($input: AddItemInput!, $connections: [ID!]!) {
        addItem(input: $input) {
            itemEdge @prependEdge(connections: $connections) {  # ✅ Use @prependEdge
                node {
                    id
                    ...Item_data
                }
            }
            errors
        }
    }
    ```

    **Frontend Implementation**:
    ```typescript
    const connectionID = ConnectionHandler.getConnectionID(parentId, 'Parent_items');

    commitMutation(environment, {
        variables: {
            input,
            connections: [connectionID]  // ✅ Pass connection ID
        },
        optimisticResponse: {  // ✅ Use optimisticResponse, not optimisticUpdater
            addItem: {
                itemEdge: {
                    node: {
                        id: tempId,
                        __typename: 'Item',
                        // ... optimistic field values
                    }
                }
            }
        }
    });
    ```

    **WHY THIS RULE EXISTS**:
    - **Data Integrity**: Prevents cache replacement that loses unsaved user changes
    - **Relay Alignment**: Uses framework as intended, ensuring future compatibility
    - **Maintainability**: Reduces custom code and leverages battle-tested Relay patterns
    - **Performance**: Smaller payloads and automatic optimizations
    - **Error Handling**: Built-in rollback and conflict resolution

    **REFERENCE IMPLEMENTATIONS**:
    - ✅ Timesheet Roster add/delete operations (follow this pattern)
    - ❌ Original AddEmptyPayStub implementation (violated this pattern)

    **ENFORCEMENT**:
    - Code reviews MUST verify list mutations follow this pattern
    - Any "expedient" manual connection handling will be rejected
    - Architecture reviews MUST include connection pattern validation

    **EXCEPTIONS**:
    None. This pattern MUST be followed for all list mutations regardless of timeline pressure or perceived complexity.

    - _Reviewer Check:_ Verify that list mutations use edge-based payloads, proper `@connection` fragments, and Relay directives (`@prependEdge`, `@appendEdge`, `@deleteEdge`). Reject any manual ConnectionHandler operations for simple list updates. Ensure optimistic updates use `optimisticResponse` rather than manual store manipulation.

14. **GraphQL-Generated Types Usage:**

    **MANDATORY:**
    - **Use Generated Input Types**: All mutations **MUST** use GraphQL-generated input types (e.g., `ModifyPayStubInput`) instead of custom interfaces or `any`.
    - **Barrel Imports**: Import GraphQL types through stable barrel exports (`@/types/graphql-*`) not direct `__generated__` imports.
    - **Exact Field Names**: Object properties **MUST** match GraphQL schema field names exactly (use `stHours`, not `sTHours`).
    - **Conversion Functions**: Implement explicit conversion functions between domain models and GraphQL types at operation boundaries.

    **FORBIDDEN:**
    - ❌ Custom interfaces duplicating GraphQL types
    - ❌ Direct `__generated__` imports in business logic
    - ❌ Field name mismatches with GraphQL schema
    - ❌ Mixing domain model and GraphQL fields in same object

    **PATTERN:**
    ```typescript
    // ✅ Barrel export
    export type { ModifyPayStubInput } from '@/relay/__generated__/...';

    // ✅ Use generated type with exact field names
    const input: ModifyPayStubInput = {
        stHours: domain.hours.standard,  // Matches GraphQL schema
        otHours: domain.hours.overtime
    };

    // ✅ Explicit conversion
    function convertToGraphQL(domain: PayStubDomainModel): ModifyPayStubInput { ... }
    ```

    **WHY:** Prevents field name mismatches, ensures schema consistency, enables safe refactoring.

    - _Reviewer Check:_ Verify mutations use generated types, field names match schema exactly, and conversion functions exist at boundaries.

15. **Fragment-Domain Model Boundary Management:**

    **SCENARIO:** Components that need both Relay fragment data and domain model state management, such as timesheet components that maintain draft changes alongside server data.

    **MANDATORY PATTERN:**
    - Use wrapper components at fragment/domain boundaries
    - Never cast domain models to fragment references (`domainModel as any`)
    - Implement explicit conversion functions between domain and GraphQL types
    - Pass fragment references alongside domain models when needed
    - Use Zustand for state management following existing codebase patterns

    **CORRECT EXAMPLE:**
    ```typescript
    // ✅ CORRECT: Dual-prop pattern with Zustand
    interface PayStubRowProps {
        payStub: PayStubDomainModel;               // Domain model for UI logic
        payStubFragmentRef: PayStubTable_payStubFragment$key; // Fragment reference for nested components
        timesheetId: string;
    }

    const PayStubRow: React.FC<PayStubRowProps> = ({ payStub, payStubFragmentRef, timesheetId }) => {
        // ✅ Use custom hook for clean data merging
        const displayData = useMergedPayStub(payStub, timesheetId);
        
        // ✅ Selective Zustand subscriptions for performance
        const updatePayStubDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
        const isExpanded = useTimesheetUIStore(state => {
            const key = state.createScopedKey(timesheetId, payStub.id);
            return state.expandedPayStubs.has(key);
        });

        return (
            <div>
                <input
                    value={displayData.stHours}
                    onChange={(e) => updatePayStubDraft(timesheetId, payStub.id, {
                        hours: { ...displayData.hours, standard: parseFloat(e.target.value) }
                    })}
                />
                {isExpanded && (
                    <TimeSheetDetailTableView
                        payStub={payStubFragmentRef}  // ✅ Pass fragment reference to Relay component
                        // ... other props
                    />
                )}
            </div>
        );
    };

    // ✅ Explicit conversion functions at mutation boundaries
    function convertDomainToModifyInput(
        payStubs: Map<string, Partial<PayStubDomainModel>>
    ): ModifyPayStubInput[] {
        return Array.from(payStubs.entries()).map(([id, draft]) => ({
            id: id,
            employeeId: draft.employee?.id || 0,
            stHours: draft.hours?.standard,
            otHours: draft.hours?.overtime,
            // ... other fields with exact GraphQL schema names
        }));
    }
    ```

    **FORBIDDEN APPROACHES:**
    ```typescript
    // ❌ FORBIDDEN: Direct domain → fragment casting
    <TimeSheetDetailTableView payStub={domainModel as any} />

    // ❌ FORBIDDEN: Mixing domain model and GraphQL fields
    const input = {
        id: domain.id,
        sTHours: domain.hours.standard,  // Wrong field name (should be stHours)
        customField: domain.customField  // Not in GraphQL schema
    };

    // ❌ FORBIDDEN: Manual merge logic scattered across components
    const PayStubRow = ({ payStub }) => {
        const draftData = useTimesheetUIStore(state => state.getDraftForPayStub(payStub.id));
        const displayData = useMemo(() => ({ ...payStub, ...draftData }), [payStub, draftData]); // DRY violation
    };
    ```

    **WHY THIS RULE EXISTS:**
    - **Type Safety**: Prevents runtime fragment errors from unsafe casting
    - **Maintainability**: Clear separation between data sources and UI state
    - **Performance**: Zustand selector-based subscriptions reduce re-renders
    - **Consistency**: Aligns with existing codebase patterns (rosterFilterStore.ts, Store.ts)
    - **Refactoring Safety**: Explicit conversion functions enable safe schema changes

    **IMPLEMENTATION HELPERS:**
    - Use `useMergedPayStub` custom hook for consistent data merging
    - Use `useTimesheetUIStore` with selective subscriptions for performance
    - Implement conversion functions in `ui/src/utils/domain-graphql-converters.ts`
    - Follow timesheet scoping patterns for multi-instance safety

    - _Reviewer Check:_ Verify components use dual-prop pattern at fragment/domain boundaries, fragment references are never cast to domain models, explicit conversion functions exist at mutation boundaries, and Zustand patterns follow existing codebase conventions.

## Recommended Practices (Strongly Encouraged)

12. **Error & Loading States:**

    - Utilize React Suspense (with appropriate `fallback` props) for loading states around components using `useLazyLoadQuery`, `useQuery`, or other suspending hooks.
    - Employ React Error Boundaries to catch data-fetching or rendering errors gracefully.

13. **Mutations:**

    - Clearly define mutation inputs and expected responses.
    - Implement `optimisticResponse` where feasible for better UX, ensuring its structure closely matches the expected server response payload.
    - **For list mutations, MUST follow Rule 11 connection patterns** - use edge-based payloads and Relay directives.
    - Use `updater` or `optimisticUpdater` functions only for complex store updates that cannot be handled by Relay directives.

_Adherence to these rules and recommended practices is critical for preventing runtime errors, ensuring type safety, and maintaining a healthy, understandable Relay codebase._
