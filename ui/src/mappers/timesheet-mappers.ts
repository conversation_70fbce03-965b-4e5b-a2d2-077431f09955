/**
 * Timesheet Mappers - Phase 2: Conversion Layer
 *
 * This file provides reliable mappers between domain models and GraphQL types,
 * implementing the conversion layer in our layered type architecture.
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ UI Components (Domain Models)           │ ← Rich, UI-friendly types
 * ├─────────────────────────────────────────┤
 * │ Conversion Layer (Mappers)              │ ← THIS FILE
 * ├─────────────────────────────────────────┤
 * │ GraphQL Operations (Wire Format)        │ ← Exact server contract
 * └─────────────────────────────────────────┘
 */

import type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput,
    TIMESHEET_FIELDS,
    PAYSTUB_FIELDS,
    PAYSTUB_DETAIL_FIELDS
} from '../types/graphql-timesheet';

import type { TimesheetDomainModel, PayStubDomainModel, PayStubDetailDomainModel, EmployeeDomainModel } from '../types/timesheet-domain';
// TimesheetIdConverter removed - using Global IDs directly
import { RelayIdService } from '../services/RelayIdService';

// =============================================================================
// GRAPHQL → DOMAIN MODEL CONVERTERS
// =============================================================================

/**
 * Converts GraphQL ModifyTimeSheetInput to TimesheetDomainModel
 * Used when receiving data from GraphQL operations
 */
export function convertTimesheetFromGraphQL(input: ModifyTimeSheetInput): TimesheetDomainModel {
    // Handle both new separate arrays pattern and legacy payStubs pattern
    const payStubs: PayStubDomainModel[] = [];
    
    // New pattern: separate arrays (addPayStubs, modifyPayStubs, deletePayStubIds)
    if (input.addPayStubs) {
        input.addPayStubs.forEach(addPayStub => {
            // Convert AddPayStubInput to ModifyPayStubInput-like format for domain model
            const modifyPayStub: ModifyPayStubInput = {
                id: addPayStub.id || '',
                employeeId: addPayStub.employeeId,
                employeeName: addPayStub.employeeName,
                name: addPayStub.name,
                stHours: addPayStub.stHours,
                otHours: addPayStub.otHours,
                dtHours: addPayStub.dtHours,
                bonus: addPayStub.bonus,
                expenses: addPayStub.expenses,
                details: addPayStub.details || []
            };
            payStubs.push(convertPayStubFromGraphQL(modifyPayStub));
        });
    }
    
    if (input.modifyPayStubs) {
        input.modifyPayStubs.forEach(modifyPayStub => {
            payStubs.push(convertPayStubFromGraphQL(modifyPayStub));
        });
    }
    
    // Note: Legacy payStubs field is not supported in the GraphQL schema
    // Only the new separate arrays pattern is available

    return {
        id: String(input.id),
        numericId: RelayIdService.toNumericId(input.id as unknown as string),
        name: input.name ?? '',
        status: input.status ?? 'draft',
        type: input.type ?? '',
        payPeriodEndDate: new Date().toISOString().split('T')[0], // Not available in GraphQL input
        employerGuid: input.employerGuid,

        payStubs,

        settings: {
            showDTHoursColumn: input.showDTHoursColumn ?? false,
            showCostCenterColumn: input.showCostCenterColumn ?? false,
            showBonusColumn: input.showBonusColumn ?? false,
            showExpensesColumn: input.showExpensesColumn ?? false,
            showEarningsCodesColumn: input.showEarningsCodesColumn ?? false,
            readOnly: input.readOnly ?? false
        },

        meta: {
            canEdit: !(input.readOnly ?? false),
            lastModified: input.modificationDate ? new Date(input.modificationDate) : undefined,
            hoursWorked: payStubs.reduce((total, stub) => total + stub.hours.total, 0),
            totalCount: payStubs.length
        }
    };
}

/**
 * Converts GraphQL ModifyPayStubInput to PayStubDomainModel
 * Used when receiving PayStub data from GraphQL operations
 */
export function convertPayStubFromGraphQL(input: ModifyPayStubInput): PayStubDomainModel {
    // employeeId is already a Global ID string from GraphQL
    const employeeGlobalId = input.employeeId;

    const details: PayStubDetailDomainModel[] = (input.details ?? []).map((detailInput: ModifyPayStubDetailInput) =>
        convertPayStubDetailFromGraphQL(detailInput, employeeGlobalId)
    );

    // Calculate aggregated hours from details
    const aggregatedHours = details.reduce(
        (acc, detail) => ({
            standard: acc.standard + detail.hours.standard,
            overtime: acc.overtime + detail.hours.overtime,
            doubletime: acc.doubletime + detail.hours.doubletime,
            total: acc.total + detail.hours.total
        }),
        { standard: 0, overtime: 0, doubletime: 0, total: 0 }
    );

    // Use input hours if available, otherwise use aggregated from details
    const hours = {
        standard: input.stHours ?? aggregatedHours.standard,
        overtime: input.otHours ?? aggregatedHours.overtime,
        doubletime: input.dtHours ?? aggregatedHours.doubletime,
        total: (input.stHours ?? 0) + (input.otHours ?? 0) + (input.dtHours ?? 0) || aggregatedHours.total
    };

    const amounts = details.reduce(
        (acc, detail) => ({
            bonus: acc.bonus + detail.amounts.bonus,
            expenses: acc.expenses + detail.amounts.expenses
        }),
        { bonus: 0, expenses: 0 }
    );

    return {
        id: input.id ?? '',
        employeeId: employeeGlobalId,
        employeeName: input.employeeName ?? '',
        name: input.name ?? '',

        hours,
        amounts: {
            bonus: input.bonus ?? amounts.bonus,
            expenses: input.expenses ?? amounts.expenses
        },

        details,

        employee: {
            id: employeeGlobalId,
            firstName: '',
            lastName: '',
            fullName: input.employeeName ?? '',
            externalEmployeeId: employeeGlobalId,
            active: true
        },

        ui: {
            expanded: input.expanded ?? false,
            isEditing: input.inEdit ?? false,
            hasErrors: false,
            isSelected: false,
            isTemporary: !input.id
        }
    };
}

/**
 * Converts GraphQL ModifyPayStubDetailInput to PayStubDetailDomainModel
 * Used when receiving PayStubDetail data from GraphQL operations
 */
export function convertPayStubDetailFromGraphQL(input: ModifyPayStubDetailInput, employeeId: string): PayStubDetailDomainModel {
    const workDate = input.workDate || new Date().toISOString().split('T')[0];
    const dayName = new Date(workDate).toLocaleDateString('en-US', { weekday: 'long' });

    const hours = {
        standard: input.stHours ?? 0,
        overtime: input.otHours ?? 0,
        doubletime: input.dtHours ?? 0,
        total: (input.stHours ?? 0) + (input.otHours ?? 0) + (input.dtHours ?? 0)
    };

    return {
        id: input.id ?? '',
        payStubId: input.payStubId ?? '',
        reportLineItemId: input.reportLineItemId ?? undefined,

        workDate,
        dayName,
        name: input.name ?? '',

        hours,

        job: {
            jobCode: input.jobCode ?? undefined,
            costCenter: input.costCenter ?? undefined,
            hourlyRate: input.hourlyRate ?? undefined
        },

        agreements: {
            agreementId: input.agreementId ?? undefined,
            classificationId: input.classificationId ?? undefined,
            subClassificationId: input.subClassificationId ?? undefined
        },

        amounts: {
            bonus: input.bonus ?? 0,
            expenses: input.expenses ?? 0
        },

        earnings: {
            earningsCode: input.earningsCode ?? undefined,
            earningsCodeText: input.earningsCode ?? undefined // TODO: Map to readable text
        },

        employeeId,

        ui: {
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            isTemporary: !input.id,
            validationErrors: []
        }
    };
}

// =============================================================================
// DOMAIN MODEL → GRAPHQL CONVERTERS
// =============================================================================

/**
 * Converts TimesheetDomainModel to GraphQL ModifyTimeSheetInput
 * Used when preparing data for GraphQL mutations
 */
export function convertTimesheetToGraphQL(model: TimesheetDomainModel): ModifyTimeSheetInput {
    return {
        id: RelayIdService.toGlobalId('TimeSheet', model.numericId ?? RelayIdService.toNumericId(model.id)), // Convert to Global ID for GraphQL
        employerGuid: model.employerGuid,
        name: model.name,
        status: model.status,
        type: model.type,
        // payPeriodEndDate: Not available in GraphQL input type

        // For domain model conversion, we put all PayStubs in modifyPayStubs
        // In practice, the separate arrays pattern should be used at the operation level
        modifyPayStubs: model.payStubs.map(convertPayStubToGraphQL),

        readOnly: model.settings.readOnly,
        showDTHoursColumn: model.settings.showDTHoursColumn,
        showCostCenterColumn: model.settings.showCostCenterColumn,
        showBonusColumn: model.settings.showBonusColumn,
        showExpensesColumn: model.settings.showExpensesColumn,
        showEarningsCodesColumn: model.settings.showEarningsCodesColumn,

        modificationDate: model.meta.lastModified?.toISOString()
    };
}

/**
 * Converts PayStubDomainModel to GraphQL ModifyPayStubInput
 * Used when preparing PayStub data for GraphQL mutations
 */
export function convertPayStubToGraphQL(model: PayStubDomainModel): ModifyPayStubInput {
    const result: ModifyPayStubInput = {
        id: model.id,
        employeeId: model.employeeId, // Pass Global ID directly
        employeeName: model.employeeName,
        name: model.name,

        // CRITICAL: Use correct GraphQL field names
        stHours: model.hours.standard,
        otHours: model.hours.overtime,
        dtHours: model.hours.doubletime,

        bonus: model.amounts.bonus,
        expenses: model.amounts.expenses,

        details: model.details.map(convertPayStubDetailToGraphQL)

        // UI state removed - should not be sent to server
        // These fields are managed separately in the UI layer
    };

    return result;
}

/**
 * Converts PayStubDetailDomainModel to GraphQL ModifyPayStubDetailInput
 * Used when preparing PayStubDetail data for GraphQL mutations
 */
export function convertPayStubDetailToGraphQL(model: PayStubDetailDomainModel): ModifyPayStubDetailInput {
    return {
        id: model.id,
        payStubId: model.payStubId,
        reportLineItemId: model.reportLineItemId,

        workDate: model.workDate,
        name: model.name,

        // CRITICAL: Use correct GraphQL field names
        stHours: model.hours.standard,
        otHours: model.hours.overtime,
        dtHours: model.hours.doubletime,

        jobCode: model.job.jobCode,
        costCenter: model.job.costCenter,
        hourlyRate: model.job.hourlyRate,

        agreementId: model.agreements.agreementId,
        classificationId: model.agreements.classificationId,
        subClassificationId: model.agreements.subClassificationId,

        bonus: model.amounts.bonus,
        expenses: model.amounts.expenses,

        earningsCode: model.earnings.earningsCode,
        
        delete: false // Default value - deletion is handled through deletePayStubIds array
    };
}

// =============================================================================
// PARTIAL/DRAFT CONVERTERS
// =============================================================================

/**
 * Converts partial domain model updates to GraphQL input
 * Used for incremental updates where only some fields have changed
 */
export function convertPayStubDraftToGraphQL(
    draft: Partial<PayStubDomainModel>,
    existingPayStub?: PayStubDomainModel
): Partial<ModifyPayStubInput> {
    const base = existingPayStub ? convertPayStubToGraphQL(existingPayStub) : {};

    const result: Partial<ModifyPayStubInput> = {
        ...base
    };

    // Apply draft changes
    if (draft.id !== undefined) result.id = draft.id;
    if (draft.employeeId !== undefined) result.employeeId = draft.employeeId; // Pass Global ID directly
    if (draft.employeeName !== undefined) result.employeeName = draft.employeeName;
    if (draft.name !== undefined) result.name = draft.name;

    if (draft.hours) {
        if (draft.hours.standard !== undefined) result.stHours = draft.hours.standard;
        if (draft.hours.overtime !== undefined) result.otHours = draft.hours.overtime;
        if (draft.hours.doubletime !== undefined) result.dtHours = draft.hours.doubletime;
    }

    if (draft.amounts) {
        if (draft.amounts.bonus !== undefined) result.bonus = draft.amounts.bonus;
        if (draft.amounts.expenses !== undefined) result.expenses = draft.amounts.expenses;
    }

    if (draft.details) {
        result.details = draft.details.map(convertPayStubDetailToGraphQL);
    }

    if (draft.ui) {
        if (draft.ui.expanded !== undefined) result.expanded = draft.ui.expanded;
        if (draft.ui.isEditing !== undefined) result.inEdit = draft.ui.isEditing;
    }

    return result;
}

/**
 * Converts partial PayStubDetail domain model updates to GraphQL input
 */
export function convertPayStubDetailDraftToGraphQL(
    draft: Partial<PayStubDetailDomainModel>,
    existingDetail?: PayStubDetailDomainModel
): Partial<ModifyPayStubDetailInput> {
    const base = existingDetail ? convertPayStubDetailToGraphQL(existingDetail) : {};

    const result: Partial<ModifyPayStubDetailInput> = {
        ...base
    };

    // Apply draft changes
    if (draft.id !== undefined) result.id = draft.id;
    if (draft.payStubId !== undefined) result.payStubId = draft.payStubId;
    if (draft.workDate !== undefined) result.workDate = draft.workDate;
    if (draft.name !== undefined) result.name = draft.name;

    if (draft.hours) {
        if (draft.hours.standard !== undefined) result.stHours = draft.hours.standard;
        if (draft.hours.overtime !== undefined) result.otHours = draft.hours.overtime;
        if (draft.hours.doubletime !== undefined) result.dtHours = draft.hours.doubletime;
    }

    if (draft.job) {
        if (draft.job.jobCode !== undefined) result.jobCode = draft.job.jobCode;
        if (draft.job.costCenter !== undefined) result.costCenter = draft.job.costCenter;
        if (draft.job.hourlyRate !== undefined) result.hourlyRate = draft.job.hourlyRate;
    }

    if (draft.agreements) {
        if (draft.agreements.agreementId !== undefined) result.agreementId = draft.agreements.agreementId;
        if (draft.agreements.classificationId !== undefined) result.classificationId = draft.agreements.classificationId;
        if (draft.agreements.subClassificationId !== undefined) result.subClassificationId = draft.agreements.subClassificationId;
    }

    if (draft.amounts) {
        if (draft.amounts.bonus !== undefined) result.bonus = draft.amounts.bonus;
        if (draft.amounts.expenses !== undefined) result.expenses = draft.amounts.expenses;
    }

    if (draft.earnings) {
        if (draft.earnings.earningsCode !== undefined) result.earningsCode = draft.earnings.earningsCode;
    }

    return result;
}

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Validates that a domain model can be safely converted to GraphQL
 * Checks for required fields and data integrity
 */
export function validateTimesheetForGraphQL(model: TimesheetDomainModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!model.id && !model.numericId) {
        errors.push('Timesheet must have an id');
    }

    if (!model.employerGuid) {
        errors.push('Timesheet must have an employerGuid');
    }

    if (!model.name) {
        errors.push('Timesheet must have a name');
    }

    // Validate PayStubs
    model.payStubs.forEach((payStub: PayStubDomainModel, index: number) => {
        const validation = validatePayStubForGraphQL(payStub);
        if (!validation.isValid) {
            errors.push(...validation.errors.map((err) => `PayStub[${index}]: ${err}`));
        }
    });

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validates that a PayStub domain model can be safely converted to GraphQL
 */
export function validatePayStubForGraphQL(model: PayStubDomainModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!model.employeeId) {
        errors.push('PayStub must have an employeeId');
    } else if (!RelayIdService.isGlobalId(model.employeeId)) {
        errors.push('PayStub must have a valid Employee Global ID');
    }

    if (!model.employeeName) {
        errors.push('PayStub must have an employeeName');
    }

    // Validate hours are non-negative
    if (model.hours.standard < 0) errors.push('Standard hours cannot be negative');
    if (model.hours.overtime < 0) errors.push('Overtime hours cannot be negative');
    if (model.hours.doubletime < 0) errors.push('Doubletime hours cannot be negative');

    // Validate details
    model.details.forEach((detail: PayStubDetailDomainModel, index: number) => {
        const validation = validatePayStubDetailForGraphQL(detail);
        if (!validation.isValid) {
            errors.push(...validation.errors.map((err) => `Detail[${index}]: ${err}`));
        }
    });

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validates that a PayStubDetail domain model can be safely converted to GraphQL
 */
export function validatePayStubDetailForGraphQL(model: PayStubDetailDomainModel): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!model.workDate) {
        errors.push('PayStubDetail must have a workDate');
    }

    if (!model.payStubId) {
        errors.push('PayStubDetail must have a payStubId');
    }

    if (!model.employeeId) {
        errors.push('PayStubDetail must have an employeeId');
    }

    // Validate date format (YYYY-MM-DD)
    if (model.workDate && !/^\d{4}-\d{2}-\d{2}$/.test(model.workDate)) {
        errors.push('WorkDate must be in YYYY-MM-DD format');
    }

    // Validate hours are non-negative
    if (model.hours.standard < 0) errors.push('Standard hours cannot be negative');
    if (model.hours.overtime < 0) errors.push('Overtime hours cannot be negative');
    if (model.hours.doubletime < 0) errors.push('Doubletime hours cannot be negative');

    return {
        isValid: errors.length === 0,
        errors
    };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Creates a minimal GraphQL input for adding a new PayStub
 */
export function createNewPayStubGraphQLInput(employeeId: string, employeeName: string): ModifyPayStubInput {
    return {
        id: '', // Will be generated by server
        employeeId,
        employeeName,
        name: '',
        stHours: 0,
        otHours: 0,
        dtHours: 0,
        bonus: 0,
        expenses: 0,
        expanded: true,
        inEdit: true,
        details: []
    };
}

/**
 * Creates a minimal GraphQL input for adding a new PayStubDetail
 */
export function createNewPayStubDetailGraphQLInput(payStubId: string, workDate: string): ModifyPayStubDetailInput {
    return {
        id: '', // Will be generated by server
        payStubId,
        workDate,
        name: '',
        stHours: 0,
        otHours: 0,
        dtHours: 0,
        bonus: 0,
        expenses: 0,
        delete: false
    };
}

/**
 * Safely converts unknown data to domain model with validation
 */
export function safeConvertToTimesheetDomain(
    input: unknown
): { success: true; data: TimesheetDomainModel } | { success: false; error: string } {
    try {
        // Basic validation
        if (!input || typeof input !== 'object') {
            return { success: false, error: 'Input must be an object' };
        }

        // Try to convert assuming it's GraphQL input
        const graphqlInput = input as ModifyTimeSheetInput;
        const domainModel = convertTimesheetFromGraphQL(graphqlInput);

        // Validate the result
        const validation = validateTimesheetForGraphQL(domainModel);
        if (!validation.isValid) {
            return { success: false, error: validation.errors.join(', ') };
        }

        return { success: true, data: domainModel };
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown conversion error'
        };
    }
}
