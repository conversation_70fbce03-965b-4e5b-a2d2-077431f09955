/**
 * Bulk Upload Mutation for PayStubs
 *
 * This mutation handles bulk upload of PayStubs using the existing ModifyTimeSheet
 * mutation with optimistic updates and enhanced error handling.
 */

import { graphql, commitMutation } from 'react-relay';
import type { Environment } from 'relay-runtime';
import type {
    BulkAddPayStubsMutation$variables,
    BulkAddPayStubsMutation$data,
    BulkAddPayStubsMutation
} from '@/src/types/graphql-timesheet';
import { RelayIdService } from '@/src/services/RelayIdService';
import { createOptimisticPayStubInStore, addPayStubToConnection } from './optimisticUpdaters';
import type { ModifyTimeSheetInput } from '@/src/types/graphql-timesheet';

/**
 * The bulk upload mutation uses the existing ModifyTimeSheet GraphQL operation
 * but provides a clean interface for upload scenarios
 */
const mutation = graphql`
    mutation BulkAddPayStubsMutation($input: ModifyTimeSheetInput!, $first: Int = 500) {
        modifyTimeSheet(input: $input) {
            timeSheet {
                id
                payStubs(first: $first) @connection(key: "BulkAddPayStubs_payStubs") {
                    edges {
                        node {
                            id
                            employeeId
                            name
                            # totalHours is computed from individual hour fields
                        }
                    }
                }
            }
        }
    }
`;

/**
 * Input interface for bulk PayStub upload
 * Simplified interface that maps to the ModifyTimeSheet structure
 */
export interface BulkUploadInput {
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    payStubs: Array<{
        employeeId: string; // Use Global ID consistently
        name?: string;
        details: Array<{
            workDate: string;
            stHours?: number;
            otHours?: number;
            dtHours?: number;
            agreementId?: number;
            classificationId?: number;
            subClassificationId?: number;
            earningsCode?: string;
        }>;
    }>;
}

/**
 * Converts BulkUploadInput to ModifyTimeSheetInput format
 */
function convertToModifyTimeSheetInput(input: BulkUploadInput): ModifyTimeSheetInput {
    return {
        id: RelayIdService.toGlobalId('TimeSheet', input.numericId),
        timeSheetId: input.timeSheetId,
        employerGuid: input.employerGuid,
        addPayStubs: input.payStubs.map((payStub) => ({
            employeeId: payStub.employeeId, // Pass Global ID directly - already in correct format
            name: payStub.name,
            // totalHours is computed automatically from details
            details: payStub.details.map((detail) => ({
                workDate: detail.workDate,
                stHours: detail.stHours || null,
                otHours: detail.otHours || null,
                dtHours: detail.dtHours || null,
                agreementId: detail.agreementId || null,
                classificationId: detail.classificationId || null,
                subClassificationId: detail.subClassificationId || null,
                earningsCode: detail.earningsCode || null
            }))
        }))
    };
}

/**
 * Executes the bulk upload operation with optimistic updates
 *
 * @param environment - Relay environment
 * @param input - Bulk upload input data
 * @returns Promise that resolves when the operation completes
 */
export function bulkAddPayStubs(environment: Environment, input: BulkUploadInput): Promise<void> {
    return new Promise((resolve, reject) => {
        const modifyInput = convertToModifyTimeSheetInput(input);

        commitMutation<BulkAddPayStubsMutation>(environment, {
            mutation,
            variables: { input: modifyInput },

            optimisticUpdater: (store) => {
                const timeSheet = store.get(input.timeSheetId);
                if (!timeSheet) {
                    console.warn(`TimeSheet ${input.timeSheetId} not found for optimistic update`);
                    return;
                }

                // Create optimistic PayStub records for immediate UI feedback
                input.payStubs.forEach((payStubData, index) => {
                    const tempId = `client:temp:upload:${Date.now()}_${index}`;
                    const totalHours = payStubData.details.reduce(
                        (sum, detail) => sum + (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0),
                        0
                    );

                    const payStubId = createOptimisticPayStubInStore(store, {
                        id: tempId,
                        employeeId: payStubData.employeeId, // Use Global ID directly - consistent with server schema
                        name: payStubData.name,
                        totalHours
                    });

                    addPayStubToConnection(store, input.timeSheetId, payStubId);
                });
            },

            updater: (store, response) => {
                // Handle server response
                if (!response?.modifyTimeSheet?.timeSheet) {
                    console.warn('No response data from modifyTimeSheet mutation');
                    return;
                }

                // The mutation response should automatically update the Relay store
                // No additional manual updates needed here
            },

            onCompleted: (response) => {
                if (!response?.modifyTimeSheet?.timeSheet) {
                    reject(new Error('Upload failed: No response from server'));
                    return;
                }

                console.log(`Successfully uploaded ${input.payStubs.length} PayStubs`);
                resolve();
            },

            onError: (error) => {
                console.error('Bulk upload failed:', error);
                reject(error);
            }
        });
    });
}

/**
 * Validates upload input before processing
 *
 * @param input - Input to validate
 * @throws Error if validation fails
 */
export function validateBulkUploadInput(input: BulkUploadInput): void {
    if (!input.timeSheetId || !input.numericId || !input.employerGuid) {
        throw new Error('TimeSheet ID, numeric ID, and employer GUID are required');
    }

    if (!input.payStubs || input.payStubs.length === 0) {
        throw new Error('At least one PayStub is required for upload');
    }

    for (const payStub of input.payStubs) {
        if (!payStub.employeeId || typeof payStub.employeeId !== 'string') {
            throw new Error('Employee ID is required for all PayStubs and must be a Global ID string');
        }

        if (!payStub.details || payStub.details.length === 0) {
            throw new Error(`PayStub for employee ${payStub.employeeId} must have at least one detail record`);
        }

        for (const detail of payStub.details) {
            if (!detail.workDate) {
                throw new Error('Work date is required for all PayStub details');
            }

            const hasHours = (detail.stHours || 0) > 0 || (detail.otHours || 0) > 0 || (detail.dtHours || 0) > 0;
            if (!hasHours) {
                console.warn(`PayStub detail for ${detail.workDate} has no hours - this may be intentional`);
            }
        }
    }
}

/**
 * Safe wrapper around bulkAddPayStubs with validation
 *
 * @param environment - Relay environment
 * @param input - Bulk upload input data
 * @returns Promise that resolves when the operation completes
 */
export async function safeBulkAddPayStubs(environment: Environment, input: BulkUploadInput): Promise<void> {
    validateBulkUploadInput(input);
    return bulkAddPayStubs(environment, input);
}
