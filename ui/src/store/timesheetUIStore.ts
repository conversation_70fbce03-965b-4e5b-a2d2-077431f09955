/**
 * Timesheet UI Store - Zustand-based State Management
 *
 * Phase 0 Implementation: Zustand Store Architecture Setup
 *
 * This store replaces React Context patterns with Zustand for improved performance,
 * persistence, and consistency with existing codebase patterns. It manages timesheet UI state
 * including draft changes, expansion state, editing state, and error handling.
 *
 * Key Features:
 * - Selective subscriptions for optimal performance
 * - In-memory state management (no persistence)
 * - Multi-timesheet scoping for safety
 * - Consistent patterns with existing stores (rosterFilterStore.ts, Store.ts)
 * - Comprehensive error handling and validation
 */

import { create } from 'zustand';
import { commitMutation } from 'react-relay';
import type { Environment } from 'relay-runtime';
import type { FlatPayStubDetailDraft, FlatPayStubDraft, PayStubDetailDraftKeys } from '@/src/types';
import { validateFlatDraftField } from '@/src/types';
import { safeNumericValue, getEffectiveValueForTotals } from '@/src/utils/flat-type-utilities';
import type { commitModifyTimeSheetMutation } from '@/relay/__generated__/commitModifyTimeSheetMutation.graphql';
import { flatToModifyInput } from '../utils/schema-aware-converters';
import { addEmptyPayStubMutation } from '../mutations/timesheet/AddEmptyPayStubMutation';
import type { ValidationError } from '@/src/utils/validationUtils';
import { debounce, type DebouncedFunction } from '@/src/utils/debounce';
import { RelayIdService } from '../services/RelayIdService';

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================

/**
 * Error information for timesheet operations
 */
export interface TimesheetError {
    message: string;
    code?: string;
    field?: string;
    severity: 'error' | 'warning' | 'info';
    timestamp: number;
    retryable?: boolean;
}

// Import the existing UI types from the project
import type { PayStubUI, PayStubDetailUI } from '@/src/types/relay-ui-extensions';
// Import centralized server type
import type { ServerPayStub } from '@/src/types/timesheet';

/**
 * Represents a fully modifiable PayStub with all draft changes merged
 * Used as output from selectModifiablePayStubs
 * This type is compatible with PayStubUI for seamless integration with existing code
 */
type ModifiablePayStub = PayStubUI;

/**
 * Represents a fully modifiable PayStub detail with all draft changes merged
 * Used as output from selectModifiablePayStubs
 * This type is compatible with PayStubDetailUI for seamless integration with existing code
 */
type ModifiablePayStubDetail = PayStubDetailUI;

/**
 * State interface for timesheet UI management
 * Designed with multi-timesheet scoping for safety and performance
 */
interface TimesheetUIStore {
    // Current active timesheet ID (for multi-timesheet scope safety)
    activeTimesheetId: string | null;

    // Version counter for draft changes - incremented on any draft mutation
    // Used for memoization to ensure stable references in useSyncExternalStore
    draftsVersion: number;

    // Phase 1: Dual flat draft maps architecture (replaces legacy dual-map system)
    // Detail-level drafts in flat format (in-memory only) - keyed by timesheetId:detailId
    detailDrafts: Map<string, FlatPayStubDetailDraft>;

    // PayStub-level drafts in flat format - keyed by timesheetId:payStubId
    payStubDrafts: Map<string, FlatPayStubDraft>;

    // UI state (in-memory only) - keyed by timesheetId:payStubId
    expandedPayStubs: Set<string>;
    editingPayStubs: Set<string>;

    // Error state - keyed by timesheetId:payStubId
    errorsByPayStubId: Map<string, TimesheetError>;
    validationErrorsByPayStubId: Map<string, ValidationError[]>;

    // Loading state
    isSaving: boolean;
    savingPayStubIds: Set<string>;
    lastSaved: number | null;

    // Global error state
    globalError: string | null;

    // Employee selector state
    showingEmployeeSelector: boolean;
    selectedEmployeeForAdd: string | null;

    // Phase 5: Singleton debouncer cache for validation - keyed by timesheetId
    validationDebouncers: Map<string, DebouncedFunction<[payload: any], void>>;

    // Phase 2: Additional UI state for complete Context migration
    selectedEmployees: Map<string, Set<string>>; // timesheetId -> Set<employeeId>
    dateRanges: Map<string, { startDate: string; endDate: string }>; // timesheetId -> date range
    viewModes: Map<string, string>; // timesheetId -> viewMode
    columnVisibility: Map<string, Map<string, boolean>>; // timesheetId -> columnName -> visible
    employeeSelectorVisible: Map<string, boolean>; // timesheetId -> visible
    selectedPayStubs: Map<string, Set<string>>; // timesheetId -> Set<payStubId>
    markedForDeletion: Set<string>; // scoped keys for PayStubs marked for deletion

    // Actions with timesheet scoping for multi-instance safety
    setActiveTimesheet: (timesheetId: string) => void;
    // Phase 1: Updated to use flat types
    updatePayStubDraft: (timesheetId: string, payStubId: string, changes: Partial<FlatPayStubDraft>) => void;
    toggleExpansion: (timesheetId: string, payStubId: string) => void;
    setEditingState: (timesheetId: string, payStubId: string, isEditing: boolean) => void;
    setError: (timesheetId: string, payStubId: string, error: TimesheetError | null) => void;
    setValidationErrors: (timesheetId: string, payStubId: string, errors: ValidationError[]) => void;
    clearValidationErrors: (timesheetId: string, payStubId: string) => void;
    clearValidationErrorsForField: (timesheetId: string, payStubId: string, detailId?: string, columnUid?: string) => void;
    setGlobalError: (error: string | null) => void;
    clearError: (timesheetId: string, payStubId: string) => void;

    // Phase 5: Singleton debouncer management
    getValidationDebouncer: (timesheetId: string, handler: (payload: any) => void) => DebouncedFunction<[payload: any], void>;
    clearValidationDebouncer: (timesheetId: string) => void;
    stopEditingPayStub: (timesheetId: string) => void;
    startEditingPayStub: (timesheetId: string, payStubId: string) => void;

    // Employee selector actions (global state)
    showEmployeeSelector: (timesheetId: string) => void;
    hideEmployeeSelector: () => void;
    selectEmployeeForNewPayStub: (timesheetId: string, employeeId: string) => void;
    addEmployeeWithMutation: (
        timesheetId: string,
        employeeId: string,
        environment: Environment,
        numericId: number,
        employerGuid: string,
        employeeName?: string
    ) => Promise<void>;

    // Phase 2: Complete Context API migration - All missing functions
    collapseAllPayStubs: (timesheetId: string) => void;
    setExpansion: (timesheetId: string, payStubId: string, expanded: boolean) => void;
    setSelectedEmployees: (timesheetId: string, employeeIds: string[]) => void;
    toggleEmployeeFilter: (timesheetId: string, employeeId: string) => void;
    setDateRange: (timesheetId: string, startDate: string, endDate: string) => void;
    setViewMode: (timesheetId: string, mode: string) => void;
    setColumnVisibility: (timesheetId: string, columnName: string, isVisible: boolean) => void;
    setEmployeeSelectorVisible: (timesheetId: string, visible: boolean) => void;
    markForDeletion: (timesheetId: string, payStubId: string) => void;
    unmarkForDeletion: (timesheetId: string, payStubId: string) => void;
    clearAllMarkForDeletion: (timesheetId: string) => void;
    // Phase 1: Updated to use flat types
    addNewPayStub: (timesheetId: string, employee: { id: string; fullName?: string }, data: Partial<FlatPayStubDraft>) => void;
    addEmptyPayStub: (timesheetId: string, employeeId: string) => void;
    deletePayStub: (timesheetId: string, payStubId: string) => void;

    // Mutation actions
    // CI GUARD: Temporarily typed as 'never' to prevent new usage during migration
    // TODO: Remove completely after Phase 2 migration is complete
    saveAllChanges: never;
    clearAllDrafts: (timesheetId?: string) => void; // Clear all or specific timesheet
    clearDraftForPayStub: (timesheetId: string, payStubId: string) => void;

    // Red-Team Recommendation: New flat draft actions
    setDetailDraft: (timesheetId: string, detailId: string, draft: FlatPayStubDetailDraft) => void;
    updateDetailField: (timesheetId: string, detailId: string, field: PayStubDetailDraftKeys, value: unknown, payStubId: string, originalDetail?: { workDate?: string }) => void;
    clearDetailDraft: (timesheetId: string, detailId: string) => void;

    // Computed selectors (for performance) - timesheet-scoped
    hasDraftChanges: (timesheetId: string) => boolean;
    // Phase 1: Updated to return flat types
    getDraftForPayStub: (timesheetId: string, payStubId: string) => FlatPayStubDraft | undefined;

    // Red-Team Recommendation: New flat draft selectors
    getDetailDraft: (timesheetId: string, detailId: string) => FlatPayStubDetailDraft | undefined;
    hasDetailDraft: (timesheetId: string, detailId: string) => boolean;
    getAllDetailDrafts: (timesheetId: string) => Map<string, FlatPayStubDetailDraft>;
    getExpandedState: (timesheetId: string, payStubId: string) => boolean;
    getEditingState: (timesheetId: string, payStubId: string) => boolean;
    getErrorForPayStub: (timesheetId: string, payStubId: string) => TimesheetError | null;

    // Phase 2: Additional getters for complete Context API migration
    getSelectedEmployees: (timesheetId: string) => string[];
    getDateRange: (timesheetId: string) => { startDate: string; endDate: string } | undefined;
    getViewMode: (timesheetId: string) => string | undefined;
    getColumnVisibility: (timesheetId: string, columnName: string) => boolean;
    isEmployeeSelectorVisible: (timesheetId: string) => boolean;
    isMarkedForDeletion: (timesheetId: string, payStubId: string) => boolean;
    getValidationErrors: (timesheetId: string, payStubId: string) => ValidationError[];
    getAllValidationErrors: (timesheetId: string) => Map<string, ValidationError[]>;
    getBlockingValidationErrorCount: (timesheetId: string) => number;
    isSavingPayStub: (timesheetId: string, payStubId: string) => boolean;

    // Stable selector for detail drafts - returns memoized array to prevent re-renders
    getDetailDraftsArrayForPayStub: (timesheetId: string, payStubId: string) => readonly FlatPayStubDetailDraft[];

    // Footer totals selector (Phase 2: derived from timesheet data + draft changes)
    selectFooterTotals: (
        timesheetId: string,
        timesheetData?: any
    ) => {
        stHours: number;
        otHours: number;
        dtHours: number;
        totalHours: number;
        bonus: number;
        expenses: number;
    };

    // Helper to create scoped keys
    createScopedKey: (timesheetId: string, payStubId: string) => string;

    // NEW: Data-gathering selector for complete PayStub data with drafts merged
    selectModifiablePayStubs: (timesheetId: string, serverPayStubs: readonly ServerPayStub[]) => readonly PayStubUI[];

    // Performance and debugging
    getStoreStats: () => {
        totalDrafts: number;
        totalExpanded: number;
        totalEditing: number;
        totalErrors: number;
        timesheetScopes: string[];
    };

    // Remap all internal draft / UI keys that still point at a temporary PayStub id
    // to the real server-assigned id.
    remapPayStubId: (timesheetId: string, oldPayStubId: string, newPayStubId: string) => void;
}

// =============================================================================
// PHASE 1: MIGRATION UTILITIES (moved to legacy-bridge.ts)
// =============================================================================

// ⚠️ Legacy migration utilities moved to src/utils/legacy-bridge.ts
// This section will be completely removed in Phase 5

// =============================================================================
// MEMOIZATION CACHE FOR STABLE SELECTORS
// =============================================================================

/**
 * Version-based cache for detail draft arrays
 * Ensures stable references for useSyncExternalStore compatibility
 *
 * Phase 2 Performance Fix: Version-based caching eliminates the risk of
 * evicting the current version, preventing infinite loop regressions.
 */
const detailDraftCache = new Map<string, readonly FlatPayStubDetailDraft[]>();

/**
 * Cache cleanup threshold - much higher since we use version-based keys
 * No risk of current version eviction with version-based keys
 */
const CACHE_CLEANUP_THRESHOLD = 500;

/**
 * Tracks the current draft version for cache management
 */
let lastCleanupVersion = 0;

/**
 * Build detail array with memoization based on drafts version
 * Returns the same reference for identical version/timesheet/payStub combinations
 * Phase 1: Updated to use detailDrafts instead of draftChanges
 */
function buildDetailArray(
    state: Pick<TimesheetUIStore, 'detailDrafts' | 'draftsVersion'>,
    timesheetId: string,
    payStubId: string
): readonly FlatPayStubDetailDraft[] {
    const key = `${timesheetId}:${payStubId}:${state.draftsVersion}`;

    // Return cached result if available - SAME REFERENCE for same version
    if (detailDraftCache.has(key)) {
        return detailDraftCache.get(key)!;
    }

    // Build new array only when version has changed
    const arr: FlatPayStubDetailDraft[] = [];
    const prefix = `${timesheetId}:`;

    // Phase 1: Use new detailDrafts map instead of draftChanges
    const entries = Array.from(state.detailDrafts.entries());
    for (const [k, draft] of entries) {
        if (k.startsWith(prefix) && draft.payStubId === payStubId) {
            arr.push(draft);
        }

        // Warn about drafts missing payStubId (development only)
        if (process.env.NODE_ENV !== 'production' && k.startsWith(prefix) && draft.payStubId === undefined) {
            console.warn('[DETAIL-DRAFT] Missing payStubId in draft:', {
                timesheetId,
                detailId: k,
                expectedPayStubId: payStubId
            });
        }
    }

    const frozen = Object.freeze(arr);

    // Phase 2 Performance Fix: Version-based cache management
    // This prevents eviction of current version and eliminates infinite loop risk
    detailDraftCache.set(key, frozen);

    // Periodic cleanup of old versions (safe since current version is never evicted)
    if (detailDraftCache.size >= CACHE_CLEANUP_THRESHOLD && state.draftsVersion > lastCleanupVersion + 50) {
        cleanupOldCacheEntries(state.draftsVersion);
        lastCleanupVersion = state.draftsVersion;
    }

    return frozen;
}

/**
 * Clean up old cache entries while preserving recent versions
 * Phase 2 Performance Fix: Safe cleanup that never evicts current/recent versions
 */
function cleanupOldCacheEntries(currentVersion: number): void {
    const keysToDelete: string[] = [];
    const versionThreshold = currentVersion - 10; // Keep last 10 versions

    for (const key of Array.from(detailDraftCache.keys())) {
        // Extract version from key (format: "timesheetId:payStubId:version")
        const parts = key.split(':');
        const version = parseInt(parts[parts.length - 1], 10);

        // Only delete entries older than threshold
        if (!isNaN(version) && version < versionThreshold) {
            keysToDelete.push(key);
        }
    }

    // Delete old entries
    for (const key of keysToDelete) {
        detailDraftCache.delete(key);
    }

    if (process.env.NODE_ENV === 'development' && keysToDelete.length > 0) {
    }
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Helper function to calculate payStub totals including draft changes and orphan drafts
 * Enhanced to include orphan draft details that don't have corresponding server data
 */
// Interface for orphan draft details
interface OrphanDraftDetail {
    id: string;
    stHours?: number;
    otHours?: number;
    dtHours?: number;
    totalHours?: number;
    bonus?: number;
    expenses?: number;
    _isOrphanDraft: true;
}

// Performance optimization: Cache for grouped orphan drafts
let _orphanDraftsByPayStubCache: Map<string, Map<string, OrphanDraftDetail[]>> | null = null;
let _cacheTimesheetId: string | null = null;
let _cacheDraftsVersion: number = -1;

function getOrphanDraftsByPayStub(
    timesheetId: string,
    detailDrafts: Map<string, FlatPayStubDetailDraft>,
    draftsVersion: number
): Map<string, OrphanDraftDetail[]> {
    // Check if cache is valid
    if (_orphanDraftsByPayStubCache && _cacheTimesheetId === timesheetId && _cacheDraftsVersion === draftsVersion) {
        return _orphanDraftsByPayStubCache.get(timesheetId) || new Map();
    }

    // Rebuild cache
    if (!_orphanDraftsByPayStubCache) {
        _orphanDraftsByPayStubCache = new Map();
    }

    const prefix = `${timesheetId}:`;
    const orphanDraftsByPayStub = new Map<string, OrphanDraftDetail[]>();

    for (const [flatDetailKey, flatDraft] of Array.from(detailDrafts.entries())) {
        if (!flatDetailKey.startsWith(prefix)) continue;

        // Extract detailId from scoped key
        const detailId = flatDetailKey.substring(prefix.length);

        // Skip if draft is marked for deletion or temporary
        if (flatDraft._uiDelete === true || flatDraft._uiIsTemporary === true) continue;

        // Skip if no payStubId (orphan drafts MUST have payStubId)
        if (!flatDraft.payStubId) continue;

        // Group by payStubId for O(1) lookup
        if (!orphanDraftsByPayStub.has(flatDraft.payStubId)) {
            orphanDraftsByPayStub.set(flatDraft.payStubId, []);
        }

        orphanDraftsByPayStub.get(flatDraft.payStubId)!.push({
            id: detailId,
            stHours: flatDraft.stHours ?? undefined,
            otHours: flatDraft.otHours ?? undefined,
            dtHours: flatDraft.dtHours ?? undefined,
            bonus: flatDraft.bonus ?? undefined,
            expenses: flatDraft.expenses ?? undefined,
            _isOrphanDraft: true as const
        });
    }

    // Update cache
    _orphanDraftsByPayStubCache.set(timesheetId, orphanDraftsByPayStub);
    _cacheTimesheetId = timesheetId;
    _cacheDraftsVersion = draftsVersion;

    return orphanDraftsByPayStub;
}

function calculatePayStubTotalsWithDrafts(
    payStub: PayStubUI | { details?: readonly PayStubDetailUI[]; id?: string },
    timesheetId: string,
    state: Pick<TimesheetUIStore, 'detailDrafts' | 'createScopedKey' | 'draftsVersion'>
) {
    const details = payStub?.details || [];

    // Get pre-grouped orphan drafts for O(1) lookup by payStubId
    const orphanDraftsByPayStub = getOrphanDraftsByPayStub(timesheetId, state.detailDrafts, state.draftsVersion);
    const serverDetailIds = new Set(details.map((detail) => detail.id).filter(Boolean));

    // Get orphan drafts for this specific payStub (O(1) lookup)
    const allOrphanDrafts = payStub.id ? orphanDraftsByPayStub.get(payStub.id) || [] : [];

    // Filter out orphan drafts that actually exist as server details
    const orphanDrafts = allOrphanDrafts.filter((draft) => !serverDetailIds.has(draft.id));

    // Combine server details with orphan drafts
    const source = [...details, ...orphanDrafts];

    if (source.length === 0) {
        return { stHours: 0, otHours: 0, dtHours: 0, totalHours: 0, bonus: 0, expenses: 0 };
    }

    const totals = source.reduce(
        (
            totals: { stHours: number; otHours: number; dtHours: number; totalHours: number; bonus: number; expenses: number },
            detail: PayStubDetailUI | OrphanDraftDetail
        ) => {
            // Skip deleted details (server-side deletion)
            if ('delete' in detail && detail.delete) return totals;

            // Get flat draft changes for this detail (scoped to timesheet and detail)
            const flatDetailKey = `${timesheetId}:${detail.id || ''}`;
            const flatDraft = state.detailDrafts.get(flatDetailKey) || {};

            // Skip if marked for deletion in flat draft
            if (flatDraft._uiDelete === true) return totals;

            // Skip if marked as temporary
            if (flatDraft._uiIsTemporary === true) return totals;

            // For orphan drafts, use the draft values directly
            // For server details, merge with draft changes using consistent null handling
            const mergedDetail =
                '_isOrphanDraft' in detail && detail._isOrphanDraft
                    ? {
                          stHours: safeNumericValue(detail.stHours),
                          otHours: safeNumericValue(detail.otHours),
                          dtHours: safeNumericValue(detail.dtHours),
                          bonus: safeNumericValue(detail.bonus),
                          expenses: safeNumericValue(detail.expenses)
                      }
                    : {
                          stHours: safeNumericValue(getEffectiveValueForTotals(detail.stHours, flatDraft.stHours)),
                          otHours: safeNumericValue(getEffectiveValueForTotals(detail.otHours, flatDraft.otHours)),
                          dtHours: safeNumericValue(getEffectiveValueForTotals(detail.dtHours, flatDraft.dtHours)),
                          bonus: safeNumericValue(getEffectiveValueForTotals(detail.bonus, flatDraft.bonus)),
                          expenses: safeNumericValue(getEffectiveValueForTotals(detail.expenses, flatDraft.expenses))
                      };

            // Values are already safely converted to numbers
            const stHours = mergedDetail.stHours;
            const otHours = mergedDetail.otHours;
            const dtHours = mergedDetail.dtHours;
            const bonus = mergedDetail.bonus;
            const expenses = mergedDetail.expenses;

            return {
                stHours: totals.stHours + stHours,
                otHours: totals.otHours + otHours,
                dtHours: totals.dtHours + dtHours,
                totalHours: totals.totalHours + stHours + otHours + dtHours,
                bonus: totals.bonus + bonus,
                expenses: totals.expenses + expenses
            };
        },
        { stHours: 0, otHours: 0, dtHours: 0, totalHours: 0, bonus: 0, expenses: 0 }
    );

    return totals;
}

// =============================================================================
// STORE IMPLEMENTATION
// =============================================================================

/**
 * Create the Zustand store (in-memory only)
 * Follows patterns from existing stores while adding timesheet-specific functionality
 */
export const useTimesheetUIStore = create<TimesheetUIStore>()((set, get) => ({
    // Initial state
    activeTimesheetId: null,
    draftsVersion: 0,

    // Phase 1: New dual flat draft maps architecture
    detailDrafts: new Map<string, FlatPayStubDetailDraft>(),
    payStubDrafts: new Map<string, FlatPayStubDraft>(),
    expandedPayStubs: new Set(),
    editingPayStubs: new Set(),
    errorsByPayStubId: new Map(),
    validationErrorsByPayStubId: new Map<string, ValidationError[]>(),
    isSaving: false,
    savingPayStubIds: new Set(),
    lastSaved: null,
    globalError: null,
    showingEmployeeSelector: false,
    selectedEmployeeForAdd: null,

    // Phase 5: Singleton debouncer cache
    validationDebouncers: new Map<string, DebouncedFunction<[payload: any], void>>(),

    // Phase 2: Additional state for complete Context migration
    selectedEmployees: new Map(),
    dateRanges: new Map(),
    viewModes: new Map(),
    columnVisibility: new Map(),
    employeeSelectorVisible: new Map(),
    selectedPayStubs: new Map(),
    markedForDeletion: new Set(),

    // Helper function for scoped keys
    createScopedKey: (timesheetId: string, payStubId: string) => `${timesheetId}:${payStubId}`,

    /**
     * Data-gathering selector that builds complete ModifiablePayStub array
     *
     * This selector merges server PayStub data with draft changes from the store,
     * providing a unified view of all PayStub modifications. It handles:
     * - Merging PayStub-level draft changes with server data
     * - Merging detail-level draft changes with server details
     * - Including orphan draft details (new details without server counterparts)
     * - Excluding PayStubs marked for deletion
     * - Including draft-only PayStubs (newly created via addEmptyPayStub)
     *
     * @param timesheetId - The ID of the timesheet to gather data for
     * @param serverPayStubs - PayStubs from the server (typically from Relay connection nodes)
     * @returns Array of ModifiablePayStub objects with all draft changes merged
     *
     * @example
     * ```typescript
     * const selectModifiablePayStubs = useTimesheetUIStore.getState().selectModifiablePayStubs;
     * const serverPayStubs = timesheetData?.payStubs?.edges?.map(edge => edge.node) || [];
     * const modifiablePayStubs = selectModifiablePayStubs(timesheetId, serverPayStubs);
     * ```
     */
    selectModifiablePayStubs: (timesheetId: string, serverPayStubs: readonly ServerPayStub[]): readonly PayStubUI[] => {
        try {
            const state = get();
            const prefix = `${timesheetId}:`;

            // Get all PayStub-level drafts for this timesheet
            const payStubDrafts = new Map<string, FlatPayStubDraft>();
            for (const [key, draft] of Array.from(state.payStubDrafts.entries())) {
                if (key.startsWith(prefix)) {
                    const payStubId = key.substring(prefix.length);
                    payStubDrafts.set(payStubId, draft);
                }
            }

            // Get all detail-level drafts for this timesheet, grouped by payStubId
            const detailDraftsByPayStub = new Map<string, PayStubDetailUI[]>();
            for (const [key, draft] of Array.from(state.detailDrafts.entries())) {
                if (key.startsWith(prefix) && draft.payStubId) {
                    const existing = detailDraftsByPayStub.get(draft.payStubId) || [];
                    existing.push({
                        ...draft,
                        id: key.substring(prefix.length) // Extract detailId from scoped key
                    } as PayStubDetailUI);
                    detailDraftsByPayStub.set(draft.payStubId, existing);
                }
            }

            // Process server PayStubs and merge with drafts
            const modifiablePayStubs: PayStubUI[] = serverPayStubs
                .filter((payStub) => {
                    // Exclude PayStubs marked for deletion
                    const scopedKey = state.createScopedKey(timesheetId, payStub.id);
                    return !state.markedForDeletion.has(scopedKey);
                })
                .map((payStub) => {
                    // Get PayStub-level draft changes
                    const payStubDraft = payStubDrafts.get(payStub.id) || {};

                    // Merge server details with draft details
                    const serverDetails = payStub.details || [];
                    const draftDetails = detailDraftsByPayStub.get(payStub.id) || [];

                    // Merge server details with their corresponding drafts
                    const mergedDetails: PayStubDetailUI[] = serverDetails.map((detail) => {
                        const detailDraft = state.detailDrafts.get(`${timesheetId}:${detail.id}`) || {};
                        return {
                            ...detail,
                            ...detailDraft,
                            // Ensure critical fields are preserved
                            id: detail.id,
                            payStubId: payStub.id
                        } as PayStubDetailUI;
                    });

                    // Add draft-only details (orphan drafts)
                    const serverDetailIds = new Set(serverDetails.map((d) => d.id));
                    const orphanDraftDetails = draftDetails.filter((draft) => !serverDetailIds.has(draft.id) && !draft.delete);

                    // Combine all details
                    const allDetails = [...mergedDetails, ...orphanDraftDetails];

                    // Return merged PayStub (use unknown for complex type conversion)
                    return {
                        ...payStub,
                        ...payStubDraft,
                        // Preserve critical fields
                        id: payStub.id,
                        details: allDetails
                    } as unknown as PayStubUI;
                });

            // Add draft-only PayStubs (created via addEmptyPayStub)
            const serverPayStubIds = new Set(serverPayStubs.map((ps) => ps.id));
            const draftOnlyPayStubs: PayStubUI[] = [];

            for (const [key, draft] of Array.from(state.payStubDrafts.entries())) {
                if (key.startsWith(prefix)) {
                    const payStubId = key.substring(prefix.length);
                    if (!serverPayStubIds.has(payStubId)) {
                        // This is a draft-only PayStub (newly created)
                        const draftDetails = detailDraftsByPayStub.get(payStubId) || [];
                        draftOnlyPayStubs.push({
                            ...draft,
                            id: payStubId,
                            details: draftDetails
                        } as unknown as PayStubUI);
                    }
                }
            }

            // Combine all PayStubs and return (draft overlays + draft-only)
            const merged = [...modifiablePayStubs, ...draftOnlyPayStubs] as const;

            return merged;
        } catch (error) {
            console.error('[TimesheetUIStore] selectModifiablePayStubs error:', error, {
                timesheetId,
                serverPayStubsCount: serverPayStubs?.length ?? 0,
                component: 'selectModifiablePayStubs'
            });
            // Fallback to server data to prevent complete failure
            return serverPayStubs.map((payStub) => ({
                ...payStub,
                details: payStub.details || []
            })) as unknown as readonly PayStubUI[];
        }
    },

    // Actions with timesheet scoping
    setActiveTimesheet: (timesheetId: string) => {
        set({ activeTimesheetId: timesheetId });
    },

    // Phase 1: Updated to use flat types instead of domain model
    updatePayStubDraft: (timesheetId: string, payStubId: string, changes: Partial<FlatPayStubDraft>) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newDrafts = new Map(state.payStubDrafts);
            const existingDraft = newDrafts.get(key) || ({} as FlatPayStubDraft);

            // Phase 1: Direct flat merge - no complex nested object handling needed
            const mergedChanges: FlatPayStubDraft = {
                ...existingDraft,
                ...changes,
                _uiLastModified: Date.now()
            };

            newDrafts.set(key, mergedChanges);
            return {
                payStubDrafts: newDrafts,
                draftsVersion: state.draftsVersion + 1
            };
        });
    },

    toggleExpansion: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newExpanded = new Set(state.expandedPayStubs);
            if (newExpanded.has(key)) {
                newExpanded.delete(key);
            } else {
                newExpanded.add(key);
            }
            return { expandedPayStubs: newExpanded };
        });
    },

    setEditingState: (timesheetId: string, payStubId: string, isEditing: boolean) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newEditing = new Set(state.editingPayStubs);
            if (isEditing) {
                newEditing.add(key);
            } else {
                newEditing.delete(key);
            }
            return { editingPayStubs: newEditing };
        });
    },

    setError: (timesheetId: string, payStubId: string, error: TimesheetError | null) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newErrors = new Map(state.errorsByPayStubId);
            if (error) {
                newErrors.set(key, {
                    ...error,
                    timestamp: Date.now()
                });
            } else {
                newErrors.delete(key);
            }
            return { errorsByPayStubId: newErrors };
        });
    },

    setValidationErrors: (timesheetId: string, payStubId: string, errors: ValidationError[]) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newErrors = new Map(state.validationErrorsByPayStubId);
            if (errors.length > 0) {
                newErrors.set(key, errors);
            } else {
                newErrors.delete(key);
            }
            return { validationErrorsByPayStubId: newErrors };
        });
    },

    clearValidationErrors: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newErrors = new Map(state.validationErrorsByPayStubId);
            newErrors.delete(key);
            return { validationErrorsByPayStubId: newErrors };
        });
    },

    clearValidationErrorsForField: (timesheetId: string, payStubId: string, detailId?: string, columnUid?: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const currentErrors = state.validationErrorsByPayStubId.get(key) || [];

            // Filter out errors that match the specified field
            const filteredErrors = currentErrors.filter((error) => {
                if (detailId && error.detailId !== detailId) return true;
                if (columnUid && error.columnUid !== columnUid) return true;
                return false; // Remove errors that match both criteria
            });

            const newErrors = new Map(state.validationErrorsByPayStubId);
            if (filteredErrors.length > 0) {
                newErrors.set(key, filteredErrors);
            } else {
                newErrors.delete(key);
            }

            return { validationErrorsByPayStubId: newErrors };
        });
    },

    setGlobalError: (error: string | null) => {
        set({ globalError: error });
    },

    clearError: (timesheetId: string, payStubId: string) => {
        get().setError(timesheetId, payStubId, null);
    },

    // Phase 5: Singleton debouncer management
    getValidationDebouncer: (timesheetId: string, handler: (payload: any) => void) => {
        const state = get();
        const existing = state.validationDebouncers.get(timesheetId);

        if (existing) {
            return existing;
        }

        // Create new debouncer with validation-specific settings
        const newDebouncer = debounce(handler, 300, {
            leading: false,
            trailing: true,
            maxWait: 1000
        });

        // Cache debouncer without triggering another React render.
        // Directly mutating the Map is safe here because we only use it as an
        // internal cache and no component subscribes specifically to it.
        state.validationDebouncers.set(timesheetId, newDebouncer);

        return newDebouncer;
    },

    clearValidationDebouncer: (timesheetId: string) => {
        const state = get();
        const debouncer = state.validationDebouncers.get(timesheetId);

        if (debouncer) {
            // Cancel any pending calls
            debouncer.cancel();

            // Remove from cache
            set((state) => {
                const newDebouncers = new Map(state.validationDebouncers);
                newDebouncers.delete(timesheetId);
                return { validationDebouncers: newDebouncers };
            });
        }
    },

    stopEditingPayStub: (timesheetId: string) => {
        set((state) => {
            const newEditing = new Set(state.editingPayStubs);
            const prefix = `${timesheetId}:`;
            for (const key of Array.from(newEditing)) {
                if (key.startsWith(prefix)) {
                    newEditing.delete(key);
                }
            }
            return { editingPayStubs: newEditing };
        });
    },

    startEditingPayStub: (timesheetId: string, payStubId: string) => {
        get().setEditingState(timesheetId, payStubId, true);
    },

    showEmployeeSelector: (timesheetId: string) => {
        set({
            showingEmployeeSelector: true,
            selectedEmployeeForAdd: timesheetId
        });
    },

    hideEmployeeSelector: () => {
        set({
            showingEmployeeSelector: false,
            selectedEmployeeForAdd: null
        });
    },

    selectEmployeeForNewPayStub: (timesheetId: string, employeeId: string) => {
        get().hideEmployeeSelector();
        get().addEmptyPayStub(timesheetId, employeeId);
    },

    /**
     * Adds an employee to a timesheet using the safe addEmptyPayStub mutation.
     *
     * This implementation replaces the deprecated addSinglePayStub function which used
     * modifyTimeSheet and caused unintentional deletion of existing pay-stubs. The new
     * approach uses a dedicated addEmptyPayStub mutation that only creates new PayStub
     * records without affecting existing ones.
     *
     * **Key Changes from Legacy Implementation:**
     * - Uses `addEmptyPayStubMutation` instead of `modifyTimeSheet`
     * - Prevents data loss by avoiding deletion of existing pay-stubs
     * - Provides immediate feedback through Relay optimistic updates
     * - Includes fallback to local draft for error recovery
     *
     * **Error Handling Strategy:**
     * - Primary: Execute server mutation for immediate persistence
     * - Fallback: Create local draft if mutation fails (for user experience)
     * - Re-throws errors to allow component-level error handling
     *
     * @param timesheetId - The ID of the timesheet to add the employee to
     * @param employeeId - The Global ID of the employee to add
     * @param environment - Relay environment for executing the mutation
     * @param numericId - Numeric timesheet ID (legacy parameter, may be removed)
     * @param employerGuid - Employer GUID for authorization (legacy parameter)
     * @param employeeName - Optional employee name for display purposes
     *
     * @throws {Error} When the mutation fails and fallback cannot recover
     *
     * @example
     * ```typescript
     * try {
     *   await timesheetStore.addEmployeeWithMutation(
     *     timesheetId,
     *     'RW1wbG95ZWU6MQ==', // Base64 encoded Employee:1
     *     relayEnvironment,
     *     12345,
     *     'employer-guid-here',
     *     'John Doe'
     *   );
     * } catch (error) {
     *   // Handle error in component
     *   console.error('Failed to add employee:', error);
     * }
     * ```
     *
     * @since Phase 4 - Replaces legacy addSinglePayStub implementation
     */
    addEmployeeWithMutation: async (
        timesheetId: string,
        employeeId: string,
        environment: Environment,
        numericId: number,
        employerGuid: string,
        employeeName?: string
    ) => {
        get().hideEmployeeSelector();

        // Use the new addEmptyPayStub mutation instead of modifyTimeSheet
        // This prevents deletion of existing pay-stubs when adding a single employee
        try {
            const payStubId = await addEmptyPayStubMutation(environment, timesheetId, employeeId);

            // 🔄 Remap any drafts that still reference the optimistic/temp PayStub id to the real id
            const storeState = get();
            const prefix = `${timesheetId}:`;
            let tempStubId: string | null = null;

            // Find the first draft whose employeeId matches and whose key uses a non-global (temp) id
            for (const [key, draft] of Array.from(storeState.payStubDrafts.entries())) {
                if (key.startsWith(prefix) && draft.employeeId === employeeId) {
                    const candidateId = key.substring(prefix.length);
                    if (!RelayIdService.isGlobalId(candidateId)) {
                        tempStubId = candidateId;
                        break;
                    }
                }
            }

            if (tempStubId) {
                storeState.remapPayStubId(timesheetId, tempStubId, payStubId);
            }
        } catch (error) {
            console.error('Failed to add employee via addEmptyPayStub mutation:', error);
            // Fallback to local draft for now (can be removed later)
            get().addEmptyPayStub(timesheetId, employeeId);
            throw error; // Re-throw so component can handle
        }
    },

    // Deprecated: This function is being phased out in favor of useSaveTimesheet hook
    // Temporarily renamed to prevent new usage during migration
    _deprecatedSaveAllChanges: async (environment: Environment, timesheetId: string, employerGuid: string) => {
        // Phase 2: Use new detailDrafts and flat payStubDrafts with proper scoping
        const { detailDrafts, payStubDrafts } = get();

        // Filter drafts for the specific timesheet (keep original scoped keys)
        const prefix = `${timesheetId}:`;
        const scopedDetailDrafts = new Map<string, FlatPayStubDetailDraft>();
        const scopedPayStubDrafts = new Map<string, FlatPayStubDraft>();

        for (const [key, value] of Array.from(detailDrafts.entries())) {
            if (key.startsWith(prefix)) {
                scopedDetailDrafts.set(key, value); // Keep original scoped key
            }
        }

        for (const [key, value] of Array.from(payStubDrafts.entries())) {
            if (key.startsWith(prefix)) {
                scopedPayStubDrafts.set(key, value); // Keep original scoped key
            }
        }

        if (scopedDetailDrafts.size === 0 && scopedPayStubDrafts.size === 0) {
            console.warn('No draft changes found for timesheet:', timesheetId);
            return;
        }

        set({ isSaving: true });

        // Phase 2: Direct flat to GraphQL conversion - eliminates domain model intermediary

        // Mark all affected PayStubs as saving
        const affectedPayStubIds = new Set<string>();
        scopedDetailDrafts.forEach((draft) => {
            if (draft.payStubId) affectedPayStubIds.add(draft.payStubId);
        });
        scopedPayStubDrafts.forEach((_, scopedKey) => {
            // Extract payStubId from scoped key to avoid double prefixing
            const payStubId = scopedKey.substring(prefix.length);
            affectedPayStubIds.add(payStubId);
        });

        const scopedKeys = Array.from(affectedPayStubIds).map((payStubId) => get().createScopedKey(timesheetId, payStubId));
        set((state) => {
            const newSaving = new Set(state.savingPayStubIds);
            scopedKeys.forEach((key) => newSaving.add(key));
            return { savingPayStubIds: newSaving };
        });

        try {
            // ✅ PHASE 2: Direct flat-to-GraphQL conversion (no domain model intermediary)
            // This eliminates the performance overhead of domain model conversion
            // and provides a direct path from flat drafts to GraphQL mutations

            const prefix = `${timesheetId}:`;

            // Direct flat-to-GraphQL conversion using new flatToModifyInput
            const payStubInputs = flatToModifyInput(scopedPayStubDrafts, scopedDetailDrafts, prefix);

            const modifyInput = {
                id: (() => {
                    const numeric = RelayIdService.parseNumericId(timesheetId);
                    if (numeric === null) {
                        throw new Error(`Invalid timesheetId '${timesheetId}' – cannot derive numeric ID`);
                    }
                    return String(numeric);
                })(),
                employerGuid: employerGuid,
                payStubs: payStubInputs // Direct flat-to-GraphQL conversion!
            };

            const { commitModifyTimeSheetMutation } = await import('@/src/relay/commitModifyTimeSheetMutation');

            return new Promise<void>((resolve, reject) => {
                commitMutation<commitModifyTimeSheetMutation>(environment, {
                    mutation: commitModifyTimeSheetMutation,
                    variables: { input: modifyInput },
                    onCompleted: (response, errors) => {
                        if (errors?.length) {
                            set({ isSaving: false });
                            reject(new Error(`GraphQL errors: ${errors.map((e) => e.message).join(', ')}`));
                            return;
                        }

                        // Clear saved drafts and update state - Phase 1: use detailDrafts
                        const newDetailDrafts = new Map(get().detailDrafts);
                        const newPayStubDrafts = new Map(get().payStubDrafts);
                        for (const key of Array.from(newDetailDrafts.keys())) {
                            if (key.startsWith(prefix)) {
                                newDetailDrafts.delete(key);
                            }
                        }
                        for (const key of Array.from(newPayStubDrafts.keys())) {
                            if (key.startsWith(prefix)) {
                                newPayStubDrafts.delete(key);
                            }
                        }

                        // Clear saving state for completed PayStubs
                        set((state) => {
                            const newSaving = new Set(state.savingPayStubIds);
                            scopedKeys.forEach((key) => newSaving.delete(key));
                            return {
                                detailDrafts: newDetailDrafts,
                                payStubDrafts: newPayStubDrafts,
                                isSaving: false,
                                savingPayStubIds: newSaving,
                                lastSaved: Date.now()
                            };
                        });
                        resolve();
                    },
                    onError: (error) => {
                        // Clear saving state on error
                        set((state) => {
                            const newSaving = new Set(state.savingPayStubIds);
                            scopedKeys.forEach((key) => newSaving.delete(key));
                            return {
                                isSaving: false,
                                savingPayStubIds: newSaving
                            };
                        });
                        reject(error);
                    }
                });
            });
        } catch (error) {
            // Clear saving state on catch error
            set((state) => {
                const newSaving = new Set(state.savingPayStubIds);
                scopedKeys.forEach((key) => newSaving.delete(key));
                return {
                    isSaving: false,
                    savingPayStubIds: newSaving
                };
            });
            throw error;
        }
    },

    // Phase 1: CI Guard implementation - typed as 'never' but must exist to satisfy interface
    // This prevents new usage while maintaining type compatibility
    // TODO: Remove completely in Phase 2 after all references are migrated to useSaveTimesheet
    saveAllChanges: (() => {
        throw new Error(
            'saveAllChanges has been deprecated in Phase 1. Use the useSaveTimesheet hook instead. ' +
                'This method will be removed in Phase 2.'
        );
    }) as never,

    clearAllDrafts: (timesheetId?: string) => {
        if (timesheetId) {
            // Clear validation debouncer for this timesheet
            get().clearValidationDebouncer(timesheetId);

            // Clear only drafts for specific timesheet
            set((state) => {
                // Phase 1: Use detailDrafts instead of draftChanges
                const newDetailDrafts = new Map(state.detailDrafts);
                const newPayStubDrafts = new Map(state.payStubDrafts);
                const newExpanded = new Set(state.expandedPayStubs);
                const newEditing = new Set(state.editingPayStubs);
                const newErrors = new Map(state.errorsByPayStubId);

                // Remove entries that start with timesheetId
                const prefix = `${timesheetId}:`;
                for (const key of Array.from(newDetailDrafts.keys())) {
                    if (key.startsWith(prefix)) newDetailDrafts.delete(key);
                }
                for (const key of Array.from(newPayStubDrafts.keys())) {
                    if (key.startsWith(prefix)) newPayStubDrafts.delete(key);
                }
                for (const key of Array.from(newExpanded)) {
                    if (key.startsWith(prefix)) newExpanded.delete(key);
                }
                for (const key of Array.from(newEditing)) {
                    if (key.startsWith(prefix)) newEditing.delete(key);
                }
                for (const key of Array.from(newErrors.keys())) {
                    if (key.startsWith(prefix)) newErrors.delete(key);
                }

                // Clear validation errors for this timesheet as well
                const newValidationErrors = new Map(state.validationErrorsByPayStubId);
                for (const key of Array.from(newValidationErrors.keys())) {
                    if (key.startsWith(prefix)) newValidationErrors.delete(key);
                }

                return {
                    detailDrafts: newDetailDrafts,
                    payStubDrafts: newPayStubDrafts,
                    expandedPayStubs: newExpanded,
                    editingPayStubs: newEditing,
                    errorsByPayStubId: newErrors,
                    validationErrorsByPayStubId: newValidationErrors,
                    draftsVersion: state.draftsVersion + 1
                };
            });
        } else {
            // Clear all validation debouncers
            const state = get();
            state.validationDebouncers.forEach((debouncer, timesheetId) => {
                get().clearValidationDebouncer(timesheetId);
            });

            // Clear all drafts - Phase 1: use detailDrafts
            set((state) => ({
                detailDrafts: new Map<string, FlatPayStubDetailDraft>(),
                payStubDrafts: new Map<string, FlatPayStubDraft>(),
                expandedPayStubs: new Set(),
                editingPayStubs: new Set(),
                errorsByPayStubId: new Map(),
                validationErrorsByPayStubId: new Map<string, ValidationError[]>(), // Clear validation errors too
                markedForDeletion: new Set(), // Clear deletion markers too
                draftsVersion: state.draftsVersion + 1
            }));
        }
    },

    clearDraftForPayStub: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newPayStubDrafts = new Map(state.payStubDrafts);
            // Phase 1: Use detailDrafts instead of draftChanges
            const newDetailDrafts = new Map(state.detailDrafts);

            // Clear PayStub-level draft
            newPayStubDrafts.delete(key);

            // Clear all detail-level drafts for this PayStub
            const detailPrefix = `${timesheetId}:`;
            for (const detailKey of Array.from(newDetailDrafts.keys())) {
                if (detailKey.startsWith(detailPrefix)) {
                    const draft = newDetailDrafts.get(detailKey);
                    if (draft?.payStubId === payStubId) {
                        newDetailDrafts.delete(detailKey);
                    }
                }
            }

            // Clear validation errors for this PayStub
            const newValidationErrors = new Map(state.validationErrorsByPayStubId);
            newValidationErrors.delete(key);

            // Clear general errors for this PayStub
            const newErrors = new Map(state.errorsByPayStubId);
            newErrors.delete(key);

            return {
                payStubDrafts: newPayStubDrafts,
                detailDrafts: newDetailDrafts,
                validationErrorsByPayStubId: newValidationErrors,
                errorsByPayStubId: newErrors,
                draftsVersion: state.draftsVersion + 1
            };
        });
    },

    // Phase 1: Updated flat draft actions to use detailDrafts
    setDetailDraft: (timesheetId: string, detailId: string, draft: FlatPayStubDetailDraft) => {
        set((state) => {
            const key = `${timesheetId}:${detailId}`;
            const newDetailDrafts = new Map(state.detailDrafts);
            newDetailDrafts.set(key, {
                ...draft,
                id: detailId, // Ensure id is always set for consistency
                // payStubId should be provided in the draft parameter
                _uiLastModified: Date.now()
            });
            return {
                detailDrafts: newDetailDrafts,
                draftsVersion: state.draftsVersion + 1
            };
        });
    },

    updateDetailField: (timesheetId: string, detailId: string, field: PayStubDetailDraftKeys, value: unknown, payStubId: string, originalDetail?: { workDate?: string }) => {
        set((state) => {
            const key = `${timesheetId}:${detailId}`;
            // Phase 1: Use detailDrafts instead of draftChanges
            const existingDraft = state.detailDrafts.get(key) || {};

            // Field validation using strict reference
            const validationResult = validateFlatDraftField(field, value);
            if (!validationResult.isValid) {
                console.error(`[FLAT-DRAFT] Field validation failed for ${field}:`, validationResult.errorMessage);
                return {}; // No state change on validation failure
            }

            // Ensure payStubId is always set in the draft for proper filtering
            // For new drafts (when existingDraft is empty), preserve important fields from original detail
            const isNewDraft = Object.keys(existingDraft).length === 0;
            const updatedDraft: FlatPayStubDetailDraft = {
                ...existingDraft,
                [field]: value,
                payStubId: payStubId, // Always set payStubId for filtering
                id: detailId, // Ensure id is also set for consistency
                // Preserve workDate from original detail if this is a new draft and workDate isn't already set
                ...(isNewDraft && originalDetail?.workDate && !existingDraft.workDate ? { workDate: originalDetail.workDate } : {}),
                _uiLastModified: Date.now()
            };

            // Validate that critical fields are set for orphan drafts
            if (!updatedDraft.payStubId) {
                console.error(`[CRITICAL] updateDetailField: payStubId is required for detail draft ${detailId}`);
                return {}; // No state change if payStubId is missing
            }

            const newDetailDrafts = new Map(state.detailDrafts);
            newDetailDrafts.set(key, updatedDraft);

            // Clear validation errors for this specific field since it has been updated
            // This prevents stale validation errors from persisting after user fixes them
            const validationKey = state.createScopedKey(timesheetId, payStubId);
            const currentErrors = state.validationErrorsByPayStubId.get(validationKey) || [];
            const filteredErrors = currentErrors.filter((error) => !(error.detailId === detailId && error.columnUid === field));

            const newValidationErrors = new Map(state.validationErrorsByPayStubId);
            if (filteredErrors.length > 0) {
                newValidationErrors.set(validationKey, filteredErrors);
            } else {
                newValidationErrors.delete(validationKey);
            }

            return {
                detailDrafts: newDetailDrafts,
                validationErrorsByPayStubId: newValidationErrors,
                draftsVersion: state.draftsVersion + 1
            };
        });
    },

    clearDetailDraft: (timesheetId: string, detailId: string) => {
        set((state) => {
            const key = `${timesheetId}:${detailId}`;
            // Phase 1: Use detailDrafts instead of draftChanges
            const newDetailDrafts = new Map(state.detailDrafts);
            const draft = newDetailDrafts.get(key);
            newDetailDrafts.delete(key);

            // Clear validation errors for this detail when draft is cleared
            const newValidationErrors = new Map(state.validationErrorsByPayStubId);
            if (draft?.payStubId) {
                const validationKey = state.createScopedKey(timesheetId, draft.payStubId);
                const currentErrors = newValidationErrors.get(validationKey) || [];
                const filteredErrors = currentErrors.filter((error) => error.detailId !== detailId);

                if (filteredErrors.length > 0) {
                    newValidationErrors.set(validationKey, filteredErrors);
                } else {
                    newValidationErrors.delete(validationKey);
                }
            }

            return {
                detailDrafts: newDetailDrafts,
                validationErrorsByPayStubId: newValidationErrors,
                draftsVersion: state.draftsVersion + 1
            };
        });
    },

    // Computed selectors with timesheet scoping
    hasDraftChanges: (timesheetId: string) => {
        const state = get();
        const prefix = `${timesheetId}:`;

        // Phase 1: Check for detail draft changes using detailDrafts
        for (const key of Array.from(state.detailDrafts.keys())) {
            if (key.startsWith(prefix)) return true;
        }

        // Check for PayStub-level draft changes
        for (const key of Array.from(state.payStubDrafts.keys())) {
            if (key.startsWith(prefix)) return true;
        }

        // Check for PayStubs marked for deletion
        for (const key of Array.from(state.markedForDeletion)) {
            if (key.startsWith(prefix)) return true;
        }

        return false;
    },

    getDraftForPayStub: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().payStubDrafts.get(key);
    },

    // Phase 1: Updated flat draft selectors to use detailDrafts
    getDetailDraft: (timesheetId: string, detailId: string) => {
        const key = `${timesheetId}:${detailId}`;
        return get().detailDrafts.get(key);
    },

    hasDetailDraft: (timesheetId: string, detailId: string) => {
        const key = `${timesheetId}:${detailId}`;
        return get().detailDrafts.has(key);
    },

    getAllDetailDrafts: (timesheetId: string) => {
        const state = get();
        const prefix = `${timesheetId}:`;
        const filteredDrafts = new Map<string, FlatPayStubDetailDraft>();

        // Phase 1: Use detailDrafts instead of draftChanges
        const entries = Array.from(state.detailDrafts.entries());
        for (const [key, draft] of entries) {
            if (key.startsWith(prefix)) {
                // Extract just the detailId part (remove timesheetId prefix)
                const detailId = key.substring(prefix.length);
                filteredDrafts.set(detailId, draft);
            }
        }

        return filteredDrafts;
    },

    // Stable selector that returns a memoized array to prevent re-renders
    // Uses version-based memoization to ensure useSyncExternalStore compatibility
    getDetailDraftsArrayForPayStub: (timesheetId: string, payStubId: string) => {
        const state = get();
        return buildDetailArray(state, timesheetId, payStubId);
    },

    getExpandedState: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().expandedPayStubs.has(key);
    },

    getEditingState: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().editingPayStubs.has(key);
    },

    getErrorForPayStub: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().errorsByPayStubId.get(key) || null;
    },

    // Phase 2: Complete Context API migration implementations
    collapseAllPayStubs: (timesheetId: string) => {
        set((state) => {
            const newExpanded = new Set(state.expandedPayStubs);
            const prefix = `${timesheetId}:`;
            for (const key of Array.from(newExpanded)) {
                if (key.startsWith(prefix)) {
                    newExpanded.delete(key);
                }
            }
            return { expandedPayStubs: newExpanded };
        });
    },

    setExpansion: (timesheetId: string, payStubId: string, expanded: boolean) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newExpanded = new Set(state.expandedPayStubs);
            if (expanded) {
                newExpanded.add(key);
            } else {
                newExpanded.delete(key);
            }
            return { expandedPayStubs: newExpanded };
        });
    },

    setSelectedEmployees: (timesheetId: string, employeeIds: string[]) => {
        set((state) => {
            const newSelected = new Map(state.selectedEmployees);
            newSelected.set(timesheetId, new Set(employeeIds));
            return { selectedEmployees: newSelected };
        });
    },

    toggleEmployeeFilter: (timesheetId: string, employeeId: string) => {
        set((state) => {
            const newSelected = new Map(state.selectedEmployees);
            const currentSet = newSelected.get(timesheetId) || new Set<string>();
            const newSet = new Set(currentSet);
            if (newSet.has(employeeId)) {
                newSet.delete(employeeId);
            } else {
                newSet.add(employeeId);
            }
            newSelected.set(timesheetId, newSet);
            return { selectedEmployees: newSelected };
        });
    },

    setDateRange: (timesheetId: string, startDate: string, endDate: string) => {
        set((state) => {
            const newRanges = new Map(state.dateRanges);
            newRanges.set(timesheetId, { startDate, endDate });
            return { dateRanges: newRanges };
        });
    },

    setViewMode: (timesheetId: string, mode: string) => {
        set((state) => {
            const newModes = new Map(state.viewModes);
            newModes.set(timesheetId, mode);
            return { viewModes: newModes };
        });
    },

    setColumnVisibility: (timesheetId: string, columnName: string, isVisible: boolean) => {
        set((state) => {
            const newVisibility = new Map(state.columnVisibility);
            const currentColumns = newVisibility.get(timesheetId) || new Map<string, boolean>();
            const newColumns = new Map(currentColumns);
            newColumns.set(columnName, isVisible);
            newVisibility.set(timesheetId, newColumns);
            return { columnVisibility: newVisibility };
        });
    },

    setEmployeeSelectorVisible: (timesheetId: string, visible: boolean) => {
        set((state) => {
            const newVisible = new Map(state.employeeSelectorVisible);
            newVisible.set(timesheetId, visible);
            return { employeeSelectorVisible: newVisible };
        });
    },

    markForDeletion: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newMarked = new Set(state.markedForDeletion);
            newMarked.add(key);

            // Clear validation errors for PayStub marked for deletion
            const newValidationErrors = new Map(state.validationErrorsByPayStubId);
            newValidationErrors.delete(key);

            // Clear general errors for this PayStub as well
            const newErrors = new Map(state.errorsByPayStubId);
            newErrors.delete(key);

            return {
                markedForDeletion: newMarked,
                validationErrorsByPayStubId: newValidationErrors,
                errorsByPayStubId: newErrors
            };
        });
    },

    unmarkForDeletion: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newMarked = new Set(state.markedForDeletion);
            newMarked.delete(key);

            return { markedForDeletion: newMarked };
        });
    },

    clearAllMarkForDeletion: (timesheetId: string) => {
        set((state) => {
            const prefix = `${timesheetId}:`;
            const newMarked = new Set(state.markedForDeletion);
            for (const key of Array.from(newMarked)) {
                if (key.startsWith(prefix)) {
                    newMarked.delete(key);
                }
            }
            return { markedForDeletion: newMarked };
        });
    },

    // Phase 1: Updated to use flat types instead of domain model
    addNewPayStub: (timesheetId: string, employee: { id: string; fullName?: string }, data: Partial<FlatPayStubDraft>) => {
        // Create a new draft PayStub with the provided data (PayStub-level fields only)
        const payStubId = `temp-${Date.now()}-${Math.random()}`;

        get().updatePayStubDraft(timesheetId, payStubId, {
            id: payStubId,
            employeeId: employee.id, // Keep as Global ID string
            name: data.name || '',
            totalHours: data.totalHours || 0,
            _uiLastModified: Date.now(),
            _uiIsTemporary: true
        });
    },

    addEmptyPayStub: (timesheetId: string, employeeId: string) => {
        // Create an empty draft PayStub - Phase 1: use flat types (PayStub-level fields only)
        const payStubId = `temp-${Date.now()}-${Math.random()}`;

        get().updatePayStubDraft(timesheetId, payStubId, {
            id: payStubId,
            employeeId: employeeId, // Keep as Global ID string
            name: '',
            totalHours: 0,
            _uiLastModified: Date.now(),
            _uiIsTemporary: true
        });
    },

    deletePayStub: (timesheetId: string, payStubId: string) => {
        // Mark for deletion and clear draft
        get().markForDeletion(timesheetId, payStubId);
        get().clearDraftForPayStub(timesheetId, payStubId);
    },

    // Additional getters for complete Context API migration
    getSelectedEmployees: (timesheetId: string) => {
        const selectedSet = get().selectedEmployees.get(timesheetId);
        return selectedSet ? Array.from(selectedSet) : [];
    },

    getDateRange: (timesheetId: string) => {
        return get().dateRanges.get(timesheetId);
    },

    getViewMode: (timesheetId: string) => {
        return get().viewModes.get(timesheetId);
    },

    getColumnVisibility: (timesheetId: string, columnName: string) => {
        const columns = get().columnVisibility.get(timesheetId);
        return columns?.get(columnName) ?? true; // Default to visible
    },

    isEmployeeSelectorVisible: (timesheetId: string) => {
        return get().employeeSelectorVisible.get(timesheetId) ?? false;
    },

    isMarkedForDeletion: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        const isMarked = get().markedForDeletion.has(key);

        return isMarked;
    },

    getValidationErrors: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().validationErrorsByPayStubId.get(key) || [];
    },

    getAllValidationErrors: (timesheetId: string) => {
        const state = get();
        const prefix = `${timesheetId}:`;
        const result = new Map<string, ValidationError[]>();

        for (const [key, errors] of Array.from(state.validationErrorsByPayStubId.entries())) {
            if (key.startsWith(prefix)) {
                // Extract payStubId from scoped key
                const payStubId = key.substring(prefix.length);
                result.set(payStubId, errors);
            }
        }

        return result;
    },

    getBlockingValidationErrorCount: (timesheetId: string) => {
        const state = get();
        const prefix = `${timesheetId}:`;
        let count = 0;

        for (const [key, errors] of Array.from(state.validationErrorsByPayStubId.entries())) {
            if (key.startsWith(prefix)) {
                // Count errors with severity 'error' (blocking errors)
                count += errors.filter((error) => error.severity === 'error').length;
            }
        }

        return count;
    },

    isSavingPayStub: (timesheetId: string, payStubId: string) => {
        const key = get().createScopedKey(timesheetId, payStubId);
        return get().savingPayStubIds.has(key);
    },

    /**
     * Calculates footer totals from timesheet data with draft changes.
     *
     * This selector aggregates totals across all PayStubs in the timesheet,
     * including any draft modifications and excluding PayStubs marked for deletion.
     *
     * @since Phase 2 - Simplified to only handle arrays after standardization
     *
     * @param {string} timesheetId - The ID of the timesheet to calculate totals for
     * @param {Object} [timesheetData] - Timesheet data containing PayStubs array
     * @param {Array} [timesheetData.payStubs] - Array of PayStub objects
     *
     * @returns {Object} Aggregated totals
     * @returns {number} returns.stHours - Total standard hours
     * @returns {number} returns.otHours - Total overtime hours
     * @returns {number} returns.dtHours - Total double-time hours
     * @returns {number} returns.totalHours - Sum of all hours
     * @returns {number} returns.bonus - Total bonuses
     * @returns {number} returns.expenses - Total expenses
     *
     * @example
     * const totals = selectFooterTotals('timesheet-1', { payStubs: materializedPayStubs });
     * console.log(`Total hours: ${totals.totalHours}`);
     */
    selectFooterTotals: (timesheetId: string, timesheetData?: { payStubs?: readonly PayStubUI[] }) => {
        // TODO: Simplify this function to only handle Relay connection objects after all call-sites are standardized (Task C-1)
        const state = get();

        // All call-sites have been standardized to pass arrays directly
        const payStubs: readonly PayStubUI[] = timesheetData?.payStubs?.filter(Boolean) ?? [];

        if (payStubs.length === 0) {
            return { stHours: 0, otHours: 0, dtHours: 0, totalHours: 0, bonus: 0, expenses: 0 };
        }

        interface FooterTotals {
            stHours: number;
            otHours: number;
            dtHours: number;
            totalHours: number;
            bonus: number;
            expenses: number;
        }

        const totals = payStubs.reduce<FooterTotals>(
            (acc, payStub) => {
                // Skip PayStubs marked for deletion
                if (payStub.id && state.isMarkedForDeletion(timesheetId, payStub.id)) {
                    return acc;
                }

                // Skip deleted payStubs (server-side deletion)
                if ('isDeleted' in payStub && payStub.isDeleted) {
                    return acc;
                }

                const payStubTotals = calculatePayStubTotalsWithDrafts(payStub, timesheetId, state);

                return {
                    stHours: acc.stHours + payStubTotals.stHours,
                    otHours: acc.otHours + payStubTotals.otHours,
                    dtHours: acc.dtHours + payStubTotals.dtHours,
                    totalHours: acc.totalHours + payStubTotals.totalHours,
                    bonus: acc.bonus + payStubTotals.bonus,
                    expenses: acc.expenses + payStubTotals.expenses
                };
            },
            { stHours: 0, otHours: 0, dtHours: 0, totalHours: 0, bonus: 0, expenses: 0 }
        );

        return totals;
    },

    // Performance and debugging
    getStoreStats: () => {
        const state = get();
        const timesheetScopes = new Set<string>();

        // Phase 1: Extract unique timesheet IDs from detailDrafts keys
        for (const key of Array.from(state.detailDrafts.keys())) {
            const timesheetId = key.split(':')[0];
            if (timesheetId) timesheetScopes.add(timesheetId);
        }

        return {
            totalDrafts: state.detailDrafts.size + state.payStubDrafts.size,
            totalExpanded: state.expandedPayStubs.size,
            totalEditing: state.editingPayStubs.size,
            totalErrors: state.errorsByPayStubId.size,
            timesheetScopes: Array.from(timesheetScopes)
        };
    },

    /**
     * Remap all internal draft / UI keys that still point at a temporary PayStub id
     * to the real server-assigned id.
     */
    remapPayStubId: (timesheetId: string, oldPayStubId: string, newPayStubId: string) => {
        set((state) => {
            const scopedOld = state.createScopedKey(timesheetId, oldPayStubId);
            const scopedNew = state.createScopedKey(timesheetId, newPayStubId);

            // ── PayStub drafts ────────────────────────────────────────────
            const newPayStubDrafts = new Map(state.payStubDrafts);
            const oldPayStubDraft = newPayStubDrafts.get(scopedOld);
            if (oldPayStubDraft) {
                newPayStubDrafts.delete(scopedOld);
                newPayStubDrafts.set(scopedNew, { ...oldPayStubDraft, id: newPayStubId });
            }

            // ── Detail drafts ─────────────────────────────────────────────
            const newDetailDrafts = new Map(state.detailDrafts);
            for (const [detailKey, draft] of Array.from(newDetailDrafts.entries())) {
                if (detailKey.startsWith(`${timesheetId}:`) && draft.payStubId === oldPayStubId) {
                    newDetailDrafts.set(detailKey, { ...draft, payStubId: newPayStubId });
                }
            }

            // ── Helper to shift scoped-key based sets ────────────────────
            const remapScopedSet = (src: Set<string>): Set<string> => {
                const updated = new Set(src);
                if (updated.delete(scopedOld)) {
                    updated.add(scopedNew);
                }
                return updated;
            };

            const expanded = remapScopedSet(state.expandedPayStubs);
            const editing = remapScopedSet(state.editingPayStubs);
            const saving = remapScopedSet(state.savingPayStubIds);
            const marked = remapScopedSet(state.markedForDeletion);

            // ── Maps keyed by scoped key ─────────────────────────────────
            const remapScopedMap = <T>(src: Map<string, T>): Map<string, T> => {
                if (!src.has(scopedOld)) return src;
                const updated = new Map(src);
                const value = updated.get(scopedOld)!;
                updated.delete(scopedOld);
                updated.set(scopedNew, value);
                return updated;
            };

            const errors = remapScopedMap(state.errorsByPayStubId);
            const validationErrors = remapScopedMap(state.validationErrorsByPayStubId);

            return {
                payStubDrafts: newPayStubDrafts,
                detailDrafts: newDetailDrafts,
                expandedPayStubs: expanded,
                editingPayStubs: editing,
                savingPayStubIds: saving,
                markedForDeletion: marked,
                errorsByPayStubId: errors,
                validationErrorsByPayStubId: validationErrors,
                draftsVersion: state.draftsVersion + 1
            };
        });
    }
}));

// =============================================================================
// CONVENIENCE HOOKS AND UTILITIES
// =============================================================================

/**
 * Convenience hook for getting scoped selectors for a specific timesheet
 * Reduces boilerplate when working with a single timesheet context
 */
export function useTimesheetUISelectors(timesheetId: string) {
    const store = useTimesheetUIStore();

    return {
        // Scoped selectors
        hasDraftChanges: () => store.hasDraftChanges(timesheetId),
        getDraftForPayStub: (payStubId: string) => store.getDraftForPayStub(timesheetId, payStubId),
        getExpandedState: (payStubId: string) => store.getExpandedState(timesheetId, payStubId),
        getEditingState: (payStubId: string) => store.getEditingState(timesheetId, payStubId),
        getErrorForPayStub: (payStubId: string) => store.getErrorForPayStub(timesheetId, payStubId),

        // Scoped actions - Phase 1: Updated to use flat types
        updatePayStubDraft: (payStubId: string, changes: Partial<FlatPayStubDraft>) =>
            store.updatePayStubDraft(timesheetId, payStubId, changes),
        toggleExpansion: (payStubId: string) => store.toggleExpansion(timesheetId, payStubId),
        setEditingState: (payStubId: string, isEditing: boolean) => store.setEditingState(timesheetId, payStubId, isEditing),
        setError: (payStubId: string, error: TimesheetError | null) => store.setError(timesheetId, payStubId, error),
        clearDraftForPayStub: (payStubId: string) => store.clearDraftForPayStub(timesheetId, payStubId),

        // Timesheet-level actions
        // Deprecated - use useSaveTimesheet hook instead
        saveAllChanges: null as never,
        clearAllDrafts: () => store.clearAllDrafts(timesheetId),

        // Global state
        isSaving: store.isSaving,
        lastSaved: store.lastSaved
    };
}

/**
 * Performance monitoring hook for debugging
 */
export function useTimesheetUIPerformance() {
    const getStoreStats = useTimesheetUIStore((state) => state.getStoreStats);
    return getStoreStats();
}

export default useTimesheetUIStore;
