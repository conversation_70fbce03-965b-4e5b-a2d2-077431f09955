import type { ValidationResult, ValidationError } from '@/src/types/timesheet-detail';
import type { TimesheetDetail_timeSheet$data } from '@/relay/__generated__/TimesheetDetail_timeSheet.graphql';
import type { ModifiablePayStub } from '@/src/types/timesheet-detail';

/**
 * TimesheetValidationService - Centralized validation logic for timesheet data
 * 
 * This service provides both real-time field validation and comprehensive timesheet validation.
 * It follows business rules defined in the PRD and provides consistent error messages.
 * 
 * Architecture note: This service is stateless and pure - it doesn't depend on React context
 * or UI state. This makes it highly testable and reusable.
 */
export class TimesheetValidationService {
    
    /**
     * Validates a single field in real-time
     * @param fieldName - The name of the field being validated
     * @param value - The value to validate
     * @param context - Additional context for validation (payStubId, detailId, etc.)
     * @returns ValidationError if validation fails, null if valid
     */
    static validateField(
        fieldName: string,
        value: any,
        context: { 
            payStubId?: string; 
            detailId?: string; 
            relatedData?: Record<string, any>;
            hasData?: boolean;
        }
    ): ValidationError | null {
        switch (fieldName) {
            case 'stHours':
            case 'otHours':
            case 'dtHours':
                return this.validateHours(fieldName, value, context);
                
            case 'bonus':
            case 'expenses':
            case 'hourlyRate':
                return this.validateMoneyField(fieldName, value, context);
                
            case 'employeeId':
                return this.validateEmployee(value, context);
                
            case 'agreementId':
                return this.validateAgreement(value, context);
                
            case 'classificationId':
                return this.validateClassification(value, context);
                
            case 'jobCode':
                return this.validateJobCode(value, context);
                
            case 'costCenter':
                return this.validateCostCenter(value, context);
                
            default:
                return null; // No validation rules for this field
        }
    }
    
    /**
     * Validates hours fields (stHours, otHours, dtHours)
     */
    private static validateHours(
        fieldName: string,
        value: number | string | null | undefined,
        context: { payStubId?: string; detailId?: string }
    ): ValidationError | null {
        // Null/undefined is valid - represents no hours entered
        if (value === null || value === undefined) {
            return null;
        }
        
        // Convert string numbers if possible
        let numericValue = value;
        if (typeof value === 'string') {
            const parsed = Number(value);
            if (!isNaN(parsed)) {
                numericValue = parsed;
            } else {
                return {
                    field: fieldName,
                    message: `${this.getFieldDisplayName(fieldName)} must be a valid number`,
                    severity: 'error',
                    columnUid: fieldName,
                    payStubId: context.payStubId || 'unknown',
                    detailId: context.detailId
                };
            }
        }
        
        // Must be a valid number
        if (typeof numericValue !== 'number' || isNaN(numericValue)) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} must be a valid number`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Use the converted numeric value for remaining validations
        value = numericValue;
        
        // Cannot be negative
        if (value < 0) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} cannot be negative`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Reasonable upper limit (24 hours per day is max)
        if (value > 24) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} cannot exceed 24 hours per day`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Precision check - no more than 2 decimal places
        if (Number(value.toFixed(2)) !== value) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} can have at most 2 decimal places`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates money fields (bonus, expenses, hourlyRate)
     */
    private static validateMoneyField(
        fieldName: string,
        value: number | string | null | undefined,
        context: { payStubId?: string; detailId?: string }
    ): ValidationError | null {
        // Null/undefined is valid for bonus and expenses
        if (value === null || value === undefined) {
            return null;
        }
        
        // Convert string numbers if possible
        let numericValue = value;
        if (typeof value === 'string') {
            const parsed = Number(value);
            if (!isNaN(parsed)) {
                numericValue = parsed;
            } else {
                return {
                    field: fieldName,
                    message: `${this.getFieldDisplayName(fieldName)} must be a valid number`,
                    severity: 'error',
                    columnUid: fieldName,
                    payStubId: context.payStubId || 'unknown',
                    detailId: context.detailId
                };
            }
        }
        
        // Must be a valid number
        if (typeof numericValue !== 'number' || isNaN(numericValue)) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} must be a valid number`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Use the converted numeric value for remaining validations
        value = numericValue;
        
        // Cannot be negative (except for certain cases like corrections)
        if (value < 0) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} cannot be negative`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Reasonable upper limit
        const maxValue = fieldName === 'hourlyRate' ? 1000 : 50000;
        const limitText = fieldName === 'bonus' ? `$${maxValue.toLocaleString()} per day` : `$${maxValue.toLocaleString()}`;
        if (value > maxValue) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} cannot exceed ${limitText}`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Precision check - money should have max 2 decimal places
        if (Number(value.toFixed(2)) !== value) {
            return {
                field: fieldName,
                message: `${this.getFieldDisplayName(fieldName)} can have at most 2 decimal places`,
                severity: 'error',
                columnUid: fieldName,
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates employee selection
     */
    private static validateEmployee(
        value: string | number | null | undefined,
        context: { payStubId?: string; detailId?: string; hasData?: boolean }
    ): ValidationError | null {
        if (!value) {
            // If hasData is explicitly set to false, allow missing employee
            if (context.hasData === false) {
                return null;
            }
            
            // If hasData is true or not specified, require employee when pay stub has data
            if (context.hasData === true) {
                return {
                    field: 'employeeId',
                    message: 'Employee must be selected when pay stub contains data',
                    severity: 'error',
                    payStubId: context.payStubId || 'unknown',
                    detailId: context.detailId,
                    columnUid: 'employeeId'
                };
            }
            
            // Default behavior when hasData is not specified
            return {
                field: 'employeeId',
                message: 'Employee must be selected',
                severity: 'error',
                columnUid: 'employeeId',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates agreement selection based on business rules
     */
    private static validateAgreement(
        value: number | null | undefined,
        context: { 
            payStubId?: string; 
            detailId?: string; 
            relatedData?: Record<string, any> 
        }
    ): ValidationError | null {
        // Check if agreement is required based on trigger fields
        const relatedData = context.relatedData || {};
        const hasTriggerData = this.hasTriggerFieldsWithData(relatedData);
        
        if (hasTriggerData && !this.isAgreementSelected(value)) {
            return {
                field: 'agreementId',
                message: 'Agreement is required when hours, job code, cost center, bonus, or expenses are entered',
                severity: 'error',
                columnUid: 'agreementId',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates classification selection based on business rules
     */
    private static validateClassification(
        value: number | null | undefined,
        context: { 
            payStubId?: string; 
            detailId?: string; 
            relatedData?: Record<string, any> 
        }
    ): ValidationError | null {
        const relatedData = context.relatedData || {};
        const hasTriggerData = this.hasTriggerFieldsWithData(relatedData);
        const agreementId = relatedData.agreementId;
        
        // Classification is required when there's trigger data, unless agreement is "No Agreement" (ID 0)
        if (hasTriggerData && agreementId !== 0 && !this.isLookupSelected(value)) {
            return {
                field: 'classificationId',
                message: 'Classification is required when hours, job code, cost center, bonus, or expenses are entered',
                severity: 'error',
                columnUid: 'classificationId',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates job code format
     */
    private static validateJobCode(
        value: string | null | undefined,
        context: { payStubId?: string; detailId?: string }
    ): ValidationError | null {
        if (!value) return null; // Job code is optional
        
        // Basic format validation - alphanumeric and some special characters
        const jobCodePattern = /^[A-Z0-9\-_]+$/i;
        if (!jobCodePattern.test(value)) {
            return {
                field: 'jobCode',
                message: 'Job code can only contain letters, numbers, hyphens, and underscores',
                severity: 'error',
                columnUid: 'jobCode',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        // Length validation
        if (value.length > 50) {
            return {
                field: 'jobCode',
                message: 'Job code cannot exceed 50 characters',
                severity: 'error',
                columnUid: 'jobCode',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Validates cost center format
     */
    private static validateCostCenter(
        value: string | null | undefined,
        context: { payStubId?: string; detailId?: string }
    ): ValidationError | null {
        if (!value) return null; // Cost center is optional
        
        // Length validation
        if (value.length > 50) {
            return {
                field: 'costCenter',
                message: 'Cost center cannot exceed 50 characters',
                severity: 'error',
                columnUid: 'costCenter',
                payStubId: context.payStubId || 'unknown',
                detailId: context.detailId
            };
        }
        
        return null;
    }
    
    /**
     * Comprehensive timesheet validation
     * @param timesheet - The timesheet data to validate
     * @param payStubs - Array of pay stubs to validate
     * @returns ValidationResult with isValid flag and array of errors
     */
    static validateTimesheet(
        timesheet: TimesheetDetail_timeSheet$data | null,
        payStubs: ModifiablePayStub[]
    ): ValidationResult {
        const errors: ValidationError[] = [];
        
        // Validate timesheet exists
        if (!timesheet) {
            errors.push({
                field: 'timesheet',
                message: 'Timesheet data is required',
                severity: 'error',
                payStubId: 'unknown',
                columnUid: 'timesheet'
            });
            return { isValid: false, errors };
        }
        
        // Validate each pay stub
        payStubs.forEach((payStub, payStubIndex) => {
            // Check if pay stub has data to determine if employee is required
            const hasPayStubData = payStub.details && payStub.details.some(detail => {
                if (detail.delete === true) return false;
                return this.hasTriggerFieldsWithData(detail);
            });
            
            // Validate employee assignment with proper context
            const employeeError = this.validateField(
                'employeeId',
                payStub.employeeId,
                {
                    payStubId: payStub.id,
                    hasData: hasPayStubData
                }
            );
            if (employeeError) {
                errors.push(employeeError);
            }
            
            // Validate pay stub details
            if (payStub.details) {
                payStub.details.forEach((detail, detailIndex) => {
                    // Skip deleted details
                    if (detail.delete === true) return;
                    
                    // Validate each field in the detail
                    const fieldNames = [
                        'stHours', 'otHours', 'dtHours', 
                        'bonus', 'expenses', 'hourlyRate',
                        'jobCode', 'costCenter', 'agreementId', 'classificationId'
                    ];
                    
                    fieldNames.forEach(fieldName => {
                        const fieldValue = (detail as any)[fieldName];
                        const error = this.validateField(
                            fieldName,
                            fieldValue,
                            {
                                payStubId: payStub.id,
                                detailId: detail.id,
                                relatedData: detail
                            }
                        );
                        if (error) {
                            errors.push(error);
                        }
                    });
                });
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Validates business rules for a detail row (agreement and classification requirements)
     * @param detail - The detail row to validate
     * @param context - Context containing payStubId and detailId
     * @returns Array of validation errors for business rule violations
     */
    static validateBusinessRules(
        detail: Record<string, any>,
        context: { payStubId?: string; detailId?: string }
    ): ValidationError[] {
        const errors: ValidationError[] = [];
        
        // Skip validation for deleted rows
        if (detail.delete === true) {
            return errors;
        }
        
        // Check if trigger fields have data
        const hasTriggerData = this.hasTriggerFieldsWithData(detail);
        
        if (hasTriggerData) {
            // Validate agreement requirement
            if (!this.isAgreementSelected(detail.agreementId)) {
                errors.push({
                    field: 'agreementId',
                    message: 'Agreement is required when hours or money fields have values',
                    severity: 'error',
                    columnUid: 'agreementId',
                    payStubId: context.payStubId || 'unknown',
                    detailId: context.detailId
                });
            }
            
            // Validate classification requirement (unless agreement is "No Agreement" = 0)
            if (detail.agreementId !== 0 && !this.isLookupSelected(detail.classificationId)) {
                errors.push({
                    field: 'classificationId',
                    message: 'Classification is required when hours or money fields have values',
                    severity: 'error',
                    columnUid: 'classificationId',
                    payStubId: context.payStubId || 'unknown',
                    detailId: context.detailId
                });
            }
        }
        
        return errors;
    }
    
    /**
     * Validates a complete detail row with business rule dependencies
     * @param detail - The detail row to validate
     * @param payStubId - ID of the containing pay stub
     * @returns Array of validation errors
     */
    static validateDetailRow(
        detail: Record<string, any>,
        payStubId: string
    ): ValidationError[] {
        const errors: ValidationError[] = [];
        
        // Skip validation for deleted rows
        if (detail.delete === true) {
            return errors;
        }
        
        // Validate all fields with their business rule dependencies
        const fieldNames = [
            'stHours', 'otHours', 'dtHours', 
            'bonus', 'expenses', 'hourlyRate',
            'jobCode', 'costCenter', 'agreementId', 'classificationId'
        ];
        
        fieldNames.forEach(fieldName => {
            const fieldValue = detail[fieldName];
            const error = this.validateField(
                fieldName,
                fieldValue,
                {
                    payStubId,
                    detailId: detail.id,
                    relatedData: detail
                }
            );
            if (error) {
                errors.push(error);
            }
        });
        
        return errors;
    }
    
    // Helper methods
    
    /**
     * Checks if a detail row has trigger fields that require agreement/classification
     */
    private static hasTriggerFieldsWithData(detail: Record<string, any>): boolean {
        return (
            this.isNumericValueEntered(detail.stHours) ||
            this.isNumericValueEntered(detail.otHours) ||
            this.isNumericValueEntered(detail.dtHours) ||
            this.isTextValueEntered(detail.jobCode) ||
            this.isTextValueEntered(detail.costCenter) ||
            this.isNumericValueEntered(detail.bonus) ||
            this.isNumericValueEntered(detail.expenses)
        );
    }
    
    /**
     * Checks if a numeric value is entered and meaningful
     */
    private static isNumericValueEntered(value: number | null | undefined): boolean {
        return value !== null && value !== undefined && value !== 0;
    }
    
    /**
     * Checks if a text value is entered and meaningful
     */
    private static isTextValueEntered(value: string | null | undefined): boolean {
        return value !== null && value !== undefined && value.trim() !== '';
    }
    
    /**
     * Checks if a lookup ID represents a valid selection
     */
    private static isLookupSelected(id: number | string | null | undefined): boolean {
        return id !== null && id !== undefined && id !== 0 && id !== '0' && id !== '';
    }
    
    /**
     * Checks if an agreement ID represents a valid selection (including "No Agreement" = 0)
     */
    private static isAgreementSelected(id: number | string | null | undefined): boolean {
        // Agreement ID 0 represents "No Agreement" and is considered valid
        if (id === 0 || id === '0') {
            return true;
        }
        return this.isLookupSelected(id);
    }
    
    /**
     * Gets display name for field names
     */
    private static getFieldDisplayName(fieldName: string): string {
        const displayNames: Record<string, string> = {
            'stHours': 'Standard hours',
            'otHours': 'Overtime hours',
            'dtHours': 'Double-time hours',
            'bonus': 'Bonus',
            'expenses': 'Expenses',
            'hourlyRate': 'Hourly rate',
            'jobCode': 'Job code',
            'costCenter': 'Cost center',
            'agreementId': 'Agreement',
            'classificationId': 'Classification',
            'employeeId': 'Employee'
        };
        
        return displayNames[fieldName] || fieldName;
    }
}