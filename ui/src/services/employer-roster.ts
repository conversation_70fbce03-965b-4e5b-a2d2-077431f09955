import { cloneDeep } from 'lodash';
import { downloadRosterView } from './rosters';
import { ColumnType } from '@/src/types/rosters';
import { EmployerRosterA<PERSON> } from '../constants/api-urls';
import { DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS } from '../constants/employer-roster';
import { DEFAULT_ROOT_HEADER_QUERY_PARAMS, DEFAULT_ROOT_QUERY_PARAMS } from '../constants/employer-roster';
import { EmployerRosterGridQuery$variables } from '@/relay/__generated__/EmployerRosterGridQuery.graphql';
import { EmployerFilterFEINFragment$data } from '@/relay/__generated__/EmployerFilterFEINFragment.graphql';
import { EmployerFilterNameFragment$data } from '@/relay/__generated__/EmployerFilterNameFragment.graphql';
import { EmployerRosterViewSortInput } from '@/relay/__generated__/EmployerRosterGridRefetchQuery.graphql';
import { EmployerRosterTableFragment$data } from '@/relay/__generated__/EmployerRosterTableFragment.graphql';
import { EmployerRosterViewFilterInput } from '@/relay/__generated__/EmployerRosterGridRefetchQuery.graphql';
import { BenefitElectionsRosterDtoSortInput } from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { BenefitElectionsTableFragment$data } from '@/relay/__generated__/BenefitElectionsTableFragment.graphql';
import { BenefitElectionsRosterDtoFilterInput } from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { BenefitElectionsRefetchQuery$variables } from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { EmployerRosterViews } from '@/src/constants/views';

// Define and export the constant for the active relationship status ID
export const ACTIVE_RELATIONSHIP_STATUS_ID = 1;

type EmployerFragmentData = EmployerRosterTableFragment$data;
type EmployerFilterFragmentData = EmployerFilterNameFragment$data | EmployerFilterFEINFragment$data;
type EmployerNode<T extends EmployerFragmentData> = NonNullable<NonNullable<T['employerRosterByChapterId']>['edges']>[number]['node'];
type EmployerFilterNode<T extends EmployerFilterFragmentData> = NonNullable<
    NonNullable<T['employersByChapterId']>['edges']
>[number]['node'];
type BenefitElectionsFragmentData = BenefitElectionsTableFragment$data;
type BenefitElectionNode = NonNullable<
    NonNullable<BenefitElectionsFragmentData['benefitElectionRosterByChapterId']>['edges']
>[number]['node'];

/**
 * Extracts the nodes from the employer roster fragment data.
 * @param fragmentData - The employer roster fragment data.
 * @returns An array of employer nodes extracted from the fragment data.
 */
export const getNodesFromEmployerRosterFragment = <T extends EmployerFragmentData>(fragmentData: T): EmployerNode<T>[] => {
    return fragmentData?.employerRosterByChapterId?.edges?.map((edge) => edge.node) || [];
};

export const getNodesFromEmployerFilterFragment = <T extends EmployerFilterFragmentData>(fragmentData: T): EmployerFilterNode<T>[] => {
    return fragmentData?.employersByChapterId?.edges?.map((edge) => edge.node) || [];
};

/**
 * Gets the employer options from the employer roster fragment data.
 * @param fragmentData - The employer roster fragment data.
 * @returns An array of employer options with value and label.
 */
export const getEmployerOptionsFromEmployerRoster = (
    fragmentData: EmployerFilterNameFragment$data
): { value: string; label: string | null | undefined }[] => {
    const nodes = getNodesFromEmployerFilterFragment(fragmentData);
    return nodes.map((node) => ({
        value: node.organization?.name || '',
        label: node.organization?.name
    }));
};

export const getFormattedFEIN = (fein: string | object | null | undefined) => {
    if (!fein) return fein;

    const feinStr = fein.toString();

    if (feinStr.length === 9) {
        const formattedFEIN = feinStr.slice(0, 2) + '-' + feinStr.slice(2);
        return formattedFEIN;
    }
};

/**
 * Gets the FEIN options from the employer roster fragment data.
 * @param fragmentData - The employer roster fragment data.
 * @returns An array of FEIN options with value and label.
 */
export const getFEINOptionsFromEmployerRoster = (
    fragmentData: EmployerFilterFEINFragment$data
): { value: string; label: string | null | undefined }[] => {
    const nodes = getNodesFromEmployerFilterFragment(fragmentData);
    return nodes.map((node) => ({
        value: node.fein || '',
        label: node.fein
    }));
};

/**
 * Gets the total count of employers from the employer roster fragment data.
 * @param fragmentData - The employer roster fragment data.
 * @returns The total count of employers.
 */
export const getTotalCountFromEmployerRosterFragment = (fragmentData: EmployerRosterTableFragment$data): number => {
    // TODO: Total Count value not coming from backend
    return fragmentData?.employerRosterByChapterId?.totalCount || 0;
};

/**
 * Combines the first name and last name to get the full name.
 * @param payrollContactFirstName - The first name of the payroll contact.
 * @param payrollContactLastName - The last name of the payroll contact.
 * @returns The full name of the payroll contact.
 */
export const getFullNameFromFirstAndLastName = (
    payrollContactFirstName: string | null | undefined,
    payrollContactLastName: string | null | undefined
): string => {
    payrollContactFirstName = payrollContactFirstName || '';
    payrollContactLastName = payrollContactLastName || '';

    return `${payrollContactFirstName} ${payrollContactLastName}`.trim();
};

/**
 * Extracts the nodes from the benefit elections fragment data.
 * @param fragmentData - The benefit elections fragment data.
 * @returns An array of benefit election nodes extracted from the fragment data.
 */
export const getNodesFromBenefitElectionsFragment = (fragmentData: BenefitElectionsFragmentData): BenefitElectionNode[] => {
    return fragmentData?.benefitElectionRosterByChapterId?.edges?.map((edge) => edge.node) || [];
};

/**
 * Gets the total count of benefit elections from the benefit elections fragment data.
 * @param fragmentData - The benefit elections fragment data.
 * @returns The total count of benefit elections.
 */
export const getTotalCountFromBenefitElectionsFragment = (fragmentData: BenefitElectionsFragmentData): number => {
    return fragmentData?.benefitElectionRosterByChapterId?.totalCount || 0;
};

const getFilterByView = <T extends EmployerRosterGridQuery$variables | BenefitElectionsRefetchQuery$variables>(
    filter: T,
    updatedAndQuery: Array<BenefitElectionsRosterDtoFilterInput | EmployerRosterViewFilterInput>,
    columndData: Array<ColumnType>
) => {
    const updatedFilter = cloneDeep(filter);
    delete updatedFilter.where?.and;

    const andQuery = filter?.where?.and ?? [];

    updatedFilter.where = updatedFilter.where ?? ({} as BenefitElectionsRosterDtoFilterInput | EmployerRosterViewFilterInput);
    updatedFilter.where.and = getANDQueryForFilterByColumnData(columndData, [...andQuery], updatedAndQuery);

    if (updatedFilter.where.and.length === 0) {
        delete updatedFilter.where.and;
    }

    return updatedFilter;
};

const getANDQueryForFilterByColumnData = (
    columndData: Array<ColumnType>,
    andQuery: Array<EmployerRosterViewFilterInput>,
    updatedAndQuery: Array<BenefitElectionsRosterDtoFilterInput | EmployerRosterViewFilterInput>
) => {
    for (let i = 0; i < andQuery.length; i++) {
        const orQuery = andQuery[i]?.or ?? [];

        if (orQuery.length > 0) {
            const filterFieldObject = orQuery[0];
            const key = Object.keys(filterFieldObject)[0] as keyof typeof filterFieldObject;

            if (columndData.findIndex((column: ColumnType) => column.key === key) > -1) {
                updatedAndQuery.push(andQuery[i]);
            }
        }
    }

    return updatedAndQuery;
};

export const getFilterByColumnData = <T extends EmployerRosterGridQuery$variables | BenefitElectionsRefetchQuery$variables>(
    filter: T,
    columndData: Array<ColumnType>
) => {
    const updatedAndQuery = [] as BenefitElectionsRosterDtoFilterInput[] | EmployerRosterViewFilterInput[];
    return getFilterByView(filter, updatedAndQuery, columndData);
};

export const getFilterWhereByColumnData = ({ and }: EmployerRosterViewFilterInput, columndData: Array<ColumnType>) => {
    const updatedAndQuery = [] as BenefitElectionsRosterDtoFilterInput[] | EmployerRosterViewFilterInput[];

    if (!and) return and;

    return getANDQueryForFilterByColumnData(columndData, [...and], updatedAndQuery);
};

export const getSortOrderByColumnData = (
    sortOrder:
        | EmployerRosterViewSortInput[]
        | BenefitElectionsRosterDtoSortInput[]
        | readonly EmployerRosterViewSortInput[]
        | null
        | undefined,
    columndData: Array<ColumnType>,
    defaultSortOrder: EmployerRosterViewSortInput[] | BenefitElectionsRosterDtoSortInput[] | null | undefined
) => {
    const updatedSortOrder = sortOrder ? [...cloneDeep(sortOrder)] : [];

    for (let i = 0; i < updatedSortOrder.length; i++) {
        const sortFieldObject = updatedSortOrder[i];
        const key = Object.keys(sortFieldObject)[0] as keyof typeof sortFieldObject;

        if (columndData.findIndex((column: ColumnType) => column.key === key || column.sortableUIKey === key) === -1) {
            updatedSortOrder.splice(i, 1);
        }
    }

    const updatedDefaultSortOrder: EmployerRosterViewSortInput[] | BenefitElectionsRosterDtoSortInput[] = cloneDeep([
        ...(defaultSortOrder || [])
    ]);

    if (updatedSortOrder.length === 0 && updatedDefaultSortOrder.length > 0) {
        updatedSortOrder.push(updatedDefaultSortOrder[0]);
    }

    return updatedSortOrder;
};

export const getDefaultBenefitElectionsQueryVariables = (chapterId: string) => {
    // Directly use encodeChapterId for GraphQL operations
    const encodedChapterId = ClientUtils.encodeChapterId(chapterId);

    // If we couldn't get a valid encoded ID, log an error
    if (!encodedChapterId) {
        console.error('Failed to create valid encoded chapter ID for GraphQL query', { originalChapterId: chapterId });
        // Return an empty string to prevent API errors or use a fallback
        // Fallback to encoded string "0" if we have no valid ID
        return {
            ...cloneDeep(DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS),
            chapterId: ClientUtils.encodeChapterId('0')
        };
    }

    return {
        ...cloneDeep(DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS),
        chapterId: encodedChapterId
    };
};

/**
 * Gets the employer roster query variables with encoded chapter ID for GraphQL.
 *
 * @param chapterId - The chapter ID in any format (GUID, numeric, or encoded)
 * @returns The employer roster query variables with properly encoded chapter ID
 */
export const getDefaultEmployerRosterQueryVariables = (chapterId: string) => {
    // Directly use encodeChapterId for GraphQL operations
    const encodedChapterId = ClientUtils.encodeChapterId(chapterId);

    // If we couldn't get a valid encoded ID, log an error
    if (!encodedChapterId) {
        console.error('Failed to create valid encoded chapter ID for GraphQL query', { originalChapterId: chapterId });
        // Return an empty string to prevent API errors or use a fallback
        // Fallback to encoded string "0" if we have no valid ID
        return {
            ...cloneDeep(DEFAULT_ROOT_QUERY_PARAMS),
            where: {
                and: [{ relationshipStatusId: { eq: ACTIVE_RELATIONSHIP_STATUS_ID } } as any]
            },
            chapterId: ClientUtils.encodeChapterId('0')
        };
    }

    return {
        ...cloneDeep(DEFAULT_ROOT_QUERY_PARAMS),
        where: {
            and: [{ relationshipStatusId: { eq: ACTIVE_RELATIONSHIP_STATUS_ID } } as any]
        },
        chapterId: encodedChapterId
    };
};

/**
 * Gets the employer roster header query variables with encoded chapter ID for GraphQL.
 *
 * @param chapterId - The chapter ID in any format (GUID, numeric, or encoded)
 * @returns The employer roster header query variables with properly encoded chapter ID
 */
export const getDefaultEmployerRosterHeaderQueryVariables = (chapterId: string) => {
    // Directly use encodeChapterId for GraphQL operations
    const encodedChapterId = ClientUtils.encodeChapterId(chapterId);

    // If we couldn't get a valid encoded ID, log an error
    if (!encodedChapterId) {
        console.error('Failed to create valid encoded chapter ID for GraphQL query', { originalChapterId: chapterId });
        // Return an empty string to prevent API errors or use a fallback
        // Fallback to encoded string "0" if we have no valid ID
        return {
            ...cloneDeep(DEFAULT_ROOT_HEADER_QUERY_PARAMS),
            chapterId: ClientUtils.encodeChapterId('0')
        };
    }

    return {
        ...cloneDeep(DEFAULT_ROOT_HEADER_QUERY_PARAMS),
        chapterId: encodedChapterId
    };
};

export const exportRosterView = (chapterIdEncoded: string, columns: Array<ColumnType>, format: string, viewType: string) => {
    const queryColumns = columns
        .filter((column) => column.show && column.key !== 'actions')
        .map((column) => `Columns=${column.key}`)
        .join('&');
    const decodedChapterId = ClientUtils.decodeChapterId(chapterIdEncoded);
    let apiPath: string;
    let fileNamePrefix: string;

    if (viewType === EmployerRosterViews.BENEFIT_ELECTIONS) {
        apiPath = EmployerRosterApi.EXPORT_BENEFIT_ELECTIONS_VIEW(decodedChapterId, queryColumns, format);
        fileNamePrefix = 'Benefit Elections - ';
    } else {
        apiPath = EmployerRosterApi.EXPORT_ROSTER_VIEW(decodedChapterId, queryColumns, format);
        fileNamePrefix = 'Employer Roster - ';
    }

    downloadRosterView(apiPath, format, fileNamePrefix);
};
