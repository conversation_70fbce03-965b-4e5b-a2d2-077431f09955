import { cloneDeep } from 'lodash';
import { downloadRosterView } from './rosters';
import { ColumnType } from '@/src/types/rosters';
import { getWeekAndEndDateOfTheYear } from './date';
import { TimesheetRosterApi } from '../constants/api-urls';
import { DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS } from '../constants/timesheet-roster';
import { TimeSheetFilterInput } from '@/relay/__generated__/EmlpoyerNameSelectRefetchQuery.graphql';
import { TimesheetRosterTableFragment$data } from '@/relay/__generated__/TimesheetRosterTableFragment.graphql';
import { TimeSheetSortInput, TimesheetRosterQuery$variables } from '@/relay/__generated__/TimesheetRosterQuery.graphql';
import { TimesheetFilterModifiedByFragment$data } from '@/relay/__generated__/TimesheetFilterModifiedByFragment.graphql';

type TimesheetRosterTableFragmentData = TimesheetRosterTableFragment$data | TimesheetFilterModifiedByFragment$data;

type EmployerNode<T extends TimesheetRosterTableFragmentData> = NonNullable<
    NonNullable<T['timesheetsByEmployerGuid']>['edges']
>[number]['node'];

export const getNodesFromTimesheetRosterFragment = <T extends TimesheetRosterTableFragmentData>(fragmentData: T): EmployerNode<T>[] => {
    return fragmentData.timesheetsByEmployerGuid?.edges?.map((edge) => getUITimesheetNodeFromNode(edge.node)) || [];
};

export const getTotalCountFromTimesheetRosterFragment = (fragmentData: TimesheetRosterTableFragment$data): number => {
    return fragmentData.timesheetsByEmployerGuid?.totalCount || 0;
};

const getUITimesheetNodeFromNode = <T extends TimesheetRosterTableFragmentData>(
    node: EmployerNode<T> & { oldId?: any; payPeriodEndDate?: string }
) => {
    const timesheetUID = node?.id; // Use node.id as UID since convertToUID is missing
    const period = node?.payPeriodEndDate ? getWeekAndEndDateOfTheYear(node.payPeriodEndDate) : node?.payPeriodEndDate;

    return {
        ...node,
        uid: timesheetUID,
        period
    };
};

const getFilterByView = (
    filter: TimesheetRosterQuery$variables,
    updatedAndQuery: Array<TimeSheetFilterInput>,
    columndData: Array<ColumnType>
) => {
    const updatedFilter = cloneDeep(filter);
    delete updatedFilter.where.and;

    const andQuery = filter?.where?.and ?? [];

    updatedFilter.where.and = getANDQueryForFilterByColumnData(columndData, [...andQuery], updatedAndQuery);

    return updatedFilter;
};

export const getModifiedByOptionsFromTimesheetRoster = (
    fragmentData: TimesheetRosterTableFragmentData
): { value: string; label: string | null | undefined }[] => {
    const nodes = getNodesFromTimesheetRosterFragment(fragmentData);
    
    // Create a Set to track unique user IDs and avoid duplicates
    const uniqueUserIds = new Set<string>();
    const options: { value: string; label: string | null | undefined }[] = [];
    
    nodes.forEach((node) => {
        const userId = node.modifiedByUserId;
        
        // Skip if userId is null, undefined, empty string, or already processed
        if (!userId || userId.trim() === '' || uniqueUserIds.has(userId)) {
            return;
        }
        
        uniqueUserIds.add(userId);
        
        // For now, format the user ID in a more readable way
        // In the future, this could be enhanced to fetch actual user names
        const label = userId; // Use the userId directly as the label
            
        options.push({
            value: userId,
            label: label
        });
    });
    
    // Sort options alphabetically by label for better UX
    return options.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
};

const getANDQueryForFilterByColumnData = (
    columndData: Array<ColumnType>,
    andQuery: Array<TimeSheetFilterInput>,
    updatedAndQuery: Array<TimeSheetFilterInput>
) => {
    for (let i = 0; i < andQuery.length; i++) {
        const orQuery = andQuery[i]?.or ?? [];

        if (orQuery.length > 0) {
            const filterFieldObject = orQuery[0];
            const key = Object.keys(filterFieldObject)[0] as keyof typeof filterFieldObject;

            if (columndData.findIndex((column: ColumnType) => column.key === key) > -1) {
                updatedAndQuery.push(andQuery[i]);
            }
        }
    }

    return updatedAndQuery;
};

export const getFilterByColumnData = (filter: TimesheetRosterQuery$variables, columndData: Array<ColumnType>) => {
    const updatedAndQuery = [] as TimeSheetFilterInput[];
    return getFilterByView(filter, updatedAndQuery, columndData);
};

export const getFilterWhereByColumnData = ({ and }: TimeSheetFilterInput, columndData: Array<ColumnType>) => {
    const updatedAndQuery = [] as TimeSheetFilterInput[];

    if (!and) return and;

    return getANDQueryForFilterByColumnData(columndData, [...and], updatedAndQuery);
};

export const getSortOrderByColumnData = (sortOrder: TimesheetRosterQuery$variables['order'], columndData: Array<ColumnType>) => {
    const updatedSortOrder = sortOrder ? [...cloneDeep(sortOrder)] : [];

    for (let i = 0; i < updatedSortOrder.length; i++) {
        const sortFieldObject = updatedSortOrder[i];
        const key = Object.keys(sortFieldObject)[0] as keyof typeof sortFieldObject;

        if (columndData.findIndex((column: ColumnType) => column.key === key || column.sortableUIKey === key) === -1) {
            updatedSortOrder.splice(i, 1);
        }
    }

    const defaultSortOrder: TimeSheetSortInput[] = cloneDeep([...(DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS.order || [])]);

    if (updatedSortOrder.length === 0 && defaultSortOrder.length > 0) {
        updatedSortOrder.push(defaultSortOrder[0]);
    }

    return updatedSortOrder;
};

export const getDefaultTimesheetRosterQueryVariables = (employerGuid: string) => {
    return {
        ...cloneDeep(DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS),
        employerGuid
    };
};

export const exportRosterView = (employerGuid: string, columns: Array<ColumnType>, format: string) => {
    const queryColumns = columns
        .filter((column) => column.show && column.key !== 'actions')
        .map((column) => `Columns=${column.key}`)
        .join('&');
    const DOWNLOAD_API = TimesheetRosterApi.EXPORT_ROSTER_VIEW(employerGuid, queryColumns, format);
    const FILE_NAME_PREFIX = 'Timesheet Roster - ';

    downloadRosterView(DOWNLOAD_API, format, FILE_NAME_PREFIX);
};
