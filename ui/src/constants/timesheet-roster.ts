import { TimesheetRosterQuery$variables } from '@/relay/__generated__/TimesheetRosterQuery.graphql';

export const TIMESHEET_ROSTER_DATABASE_CUSTOM_VIEW_NAME = 'TimesheetRosterCustomViews';

export const DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS: TimesheetRosterQuery$variables = {
    employerGuid: 'b0f5472a-5167-49ed-a36c-357c8593ea30',
    order: [{ modificationDate: 'DESC' }],
    where: { and: [] },
    customViewsName: TIMESHEET_ROSTER_DATABASE_CUSTOM_VIEW_NAME
};

export const TIMESHEET_TYPES = ['Primary', 'Additional'];
