import { EmployerRosterGridQuery$variables } from '@/relay/__generated__/EmployerRosterGridQuery.graphql';
import { EmployerRosterHeaderQuery$variables } from '@/relay/__generated__/EmployerRosterHeaderQuery.graphql';
import { BenefitElectionsRefetchQuery$variables } from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';

export const EMPLOYER_ROSTER_DATABASE_CUSTOM_VIEW_NAME = 'EmployerRosterCustomViews';

export const DEFAULT_ROOT_QUERY_PARAMS: EmployerRosterGridQuery$variables = {
    chapterId: '',
    order: [{ name: 'ASC' }],
    where: { and: [] }
};

export const DEFAULT_ROOT_HEADER_QUERY_PARAMS: EmployerRosterHeaderQuery$variables = {
    chapterId: '',
    order: [{ organization: { name: 'ASC' } }],
    where: {}
};

export const DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS: BenefitElectionsRefetchQuery$variables = {
    chapterId: '',
    order: [{ employerName: 'ASC' }],
    where: {}
};
