import { ColumnType } from '@/src/types/rosters';
import { LABEL_TEXT } from '@/src/constants/text';
import { EmployerRosterTableFragment$data } from '@/relay/__generated__/EmployerRosterTableFragment.graphql';
import { useFieldMetadata } from '@/src/context/FieldMetadataContext';
import { useStableMemo } from '@/src/lib/hooks/useStableMemo';

type NodeType = NonNullable<NonNullable<EmployerRosterTableFragment$data['employerRosterByChapterId']>['edges']>[number]['node'];

export type TableData = NodeType & {
    payrollContact: string;
};

export const employerRosterColumnData = (): ColumnType[] => {
    const metadata = useFieldMetadata();

    return useStableMemo(() => [
        {
            key: 'name',
            label: metadata?.EmployerName?.DisplayName || LABEL_TEXT.EMPLOYER_NAME_LABEL,
            columnLabel: metadata?.EmployerName?.DisplayName || LABEL_TEXT.EMPLOYER_NAME_LABEL,
            show: true,
            canHide: false,
            position: 0,
            allowsFiltering: true,
            metaData: {
                defaultWidth: 250,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'dba',
            label: metadata?.dba?.DisplayName || LABEL_TEXT.DBA_LABEL,
            columnLabel: metadata?.dba?.DisplayName || LABEL_TEXT.DBA_LABEL,
            show: false,
            canHide: true,
            position: 1,
            metaData: {
                defaultWidth: 225,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'fein',
            label: metadata?.Fein?.DisplayName || LABEL_TEXT.FEIN,
            columnLabel: metadata?.Fein?.DisplayName || LABEL_TEXT.FEIN,
            show: true,
            canHide: true,
            position: 2,
            allowsFiltering: true,
            metaData: {
                defaultWidth: 120,
                allowsResizing: true,
                allowsSorting: false,
                children: undefined
            }
        },
        {
            key: 'associationId',
            label: metadata?.NecaId?.DisplayName || LABEL_TEXT.NECA_ID_LABEL,
            columnLabel: metadata?.NecaId?.DisplayName || LABEL_TEXT.NECA_ID_LABEL,
            show: false,
            canHide: true,
            position: 3,
            allowsFiltering: false,
            metaData: {
                defaultWidth: 120,
                allowsResizing: true,
                allowsSorting: false,
                children: undefined
            }
        },
        {
            key: 'payrollContact',
            sortableUIKey: 'payrollContactFirstName',
            label: metadata?.PayrollContact?.DisplayName || LABEL_TEXT.PAYROLL_CONTACT_LABEL,
            columnLabel: metadata?.PayrollContact?.DisplayName || LABEL_TEXT.PAYROLL_CONTACT_LABEL,
            show: true,
            canHide: true,
            position: 4,
            metaData: {
                defaultWidth: 200,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'lastReportedDate',
            label: metadata?.LatestReport?.DisplayName || LABEL_TEXT.LATEST_REPORT_LABEL,
            columnLabel: metadata?.LatestReport?.DisplayName || LABEL_TEXT.LATEST_REPORT_LABEL,
            show: true,
            canHide: true,
            position: 5,
            allowsFiltering: true,
            metaData: {
                defaultWidth: 160,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'actions',
            show: true,
            canHide: false,
            label: LABEL_TEXT.ACTIONS_LABEL,
            columnLabel: LABEL_TEXT.ACTIONS_LABEL,
            position: 6,
            metaData: {
                defaultWidth: 30,
                allowsResizing: false,
                allowsSorting: false,
                children: undefined
            }
        }
    ], [metadata]);
};

// Map UI key to the key in the Dto
export const keyMapForSortingFields: { [key: string]: string } = {
    name: 'name',
    fein: 'fein',
    payrollContact: 'payrollContactFirstName',
    lastReportedDate: 'lastReportedDate'
};
