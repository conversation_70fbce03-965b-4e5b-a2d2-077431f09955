import { ColumnType } from '@/src/types/rosters';
import { BenefitElectionsTableFragment$data } from '@/relay/__generated__/BenefitElectionsTableFragment.graphql';
import { LABEL_TEXT } from '@/src/constants/text';
import { useFieldMetadata } from '@/src/context/FieldMetadataContext';
import { useStableMemo } from '@/src/lib/hooks/useStableMemo';

type NodeType = NonNullable<NonNullable<BenefitElectionsTableFragment$data['benefitElectionRosterByChapterId']>['edges']>[number]['node'];

export type TableData = NodeType & {
    override: string;
    guid: string;
};

// Hook to get column data with metadata
export const useBenefitElectionsColumnData = (): ColumnType[] => {
    const metadata = useFieldMetadata();

    return useStableMemo(() => [
        {
            key: 'employerName',
            label: metadata?.EmployerName?.DisplayName || LABEL_TEXT.EMPLOYER_NAME_LABEL,
            columnLabel: metadata?.EmployerName?.DisplayName || LABEL_TEXT.EMPLOYER_NAME_LABEL,
            show: true,
            canHide: false,
            position: 0,
            allowsFiltering: true,
            metaData: {
                defaultWidth: 250,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'fein',
            label: metadata?.Fein?.DisplayName || LABEL_TEXT.FEIN,
            columnLabel: metadata?.Fein?.DisplayName || LABEL_TEXT.FEIN,
            show: true,
            canHide: true,
            position: 1,
            metaData: {
                defaultWidth: 120,
                allowsResizing: true,
                allowsSorting: false,
                children: undefined
            }
        },
        {
            key: 'associationID',
            label: metadata?.NecaId?.DisplayName || LABEL_TEXT.NECA_ID_LABEL,
            columnLabel: metadata?.NecaId?.DisplayName || LABEL_TEXT.NECA_ID_LABEL,
            show: true,
            canHide: true,
            position: 2,
            metaData: {
                defaultWidth: 120,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'benefitName',
            label: metadata?.Benefit?.DisplayName || LABEL_TEXT.BENEFIT_LABEL, // Assumes BenefitName metadata field
            columnLabel: metadata?.Benefit?.DisplayName || LABEL_TEXT.BENEFIT_LABEL,
            show: true,
            canHide: true,
            position: 3,
            metaData: {
                defaultWidth: 180,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'effectiveStartDate',
            label: metadata?.EffectiveStartDate?.DisplayName || LABEL_TEXT.DATES_LABEL, // Uses Date metadata
            columnLabel: metadata?.EffectiveStartDate?.DisplayName || LABEL_TEXT.DATES_LABEL,
            show: true,
            canHide: true,
            position: 5,
            metaData: {
                defaultWidth: 120,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'override',
            label: metadata?.OverrideRate?.DisplayName || LABEL_TEXT.OVERRIDE_RATE_LABEL, // Uses Rate metadata
            columnLabel: metadata?.OverrideRate?.DisplayName || LABEL_TEXT.OVERRIDE_RATE_LABEL,
            show: true,
            canHide: true,
            position: 6,
            metaData: {
                defaultWidth: 140,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
        {
            key: 'effectiveEndDate',
            label: metadata?.EffectiveEndDate?.DisplayName || LABEL_TEXT.LATEST_REPORT_LABEL, // Uses SubmissionDate metadata
            columnLabel: metadata?.EffectiveEndDate?.DisplayName || LABEL_TEXT.LATEST_REPORT_LABEL,
            show: true,
            canHide: true,
            position: 7,
            metaData: {
                defaultWidth: 140,
                allowsResizing: true,
                allowsSorting: true,
                children: undefined
            }
        },
    ], [metadata]);
};

export const keyMapForSortingFields: { [key: string]: string } = {
    employerName: 'employerName',
    benefitName: 'benefitName',
    fein: 'fein',
    dba: 'dba',
    effectiveStartDate: 'effectiveStartDate',
    effectiveEndDate: 'effectiveEndDate',
    elected: 'elected',
    employerGUID: 'employerGUID',
    associationID: 'associationID'
};
