import { Suspense, Component, ErrorInfo, ReactNode, useEffect, useMemo, useState } from 'react';
import { RelayEnvironmentProvider, useLazyLoadQuery, graphql, Environment } from 'react-relay';
import { getRelayEnvironment } from '@/src/relay/withPersistence';
import { FieldMetadataProvider } from '@/src/context/FieldMetadataContext';
import { UserInfoProvider, UserInfo } from '@/src/context/UserInfoContext';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { Constants } from '@/src/constants/global';
import { RelayEnvironmentLayoutQuery as FieldMetadataContextQueryType } from '@/relay/__generated__/RelayEnvironmentLayoutQuery.graphql';

// Define types needed for processing (could be imported if shared)
type Metadata = {
    DisplayName: string;
    DisplayFormat: string | null;
};
type FieldsType = {
    [key: string]: Metadata;
};

// Internal component to fetch data and render provider
const MetadataLoader: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    // Get store setter to populate Zustand store with user data
    const setUser = useStore((state: StoreState) => state.setUser);

    // With reference tokens, we don't need to pass username - backend extracts it from token via introspection
    const data = useLazyLoadQuery<FieldMetadataContextQueryType>(
        graphql`
            query RelayEnvironmentLayoutQuery {
                fieldDefinitions {
                    key
                    value {
                        fieldName
                        displayName
                        displayFormat
                    }
                }
                userInfo {
                    username
                    roleGroups
                    showMyReportsMenu
                    showTimesheetsMenu
                    firstName
                    lastName
                    email
                    orgGUID
                    orgName
                    chapterId
                    phoneNumber
                    phoneTypeId
                    preferredMfamethod
                    phoneNumberExtension
                }
            }
        `,
        {} // No variables needed - backend gets username from reference token
    );

    // Process field definitions
    const fields: FieldsType = {};
    data?.fieldDefinitions?.forEach((entry) => {
        if (entry && entry.value) {
            const field = entry.value;
            fields[field.fieldName] = {
                DisplayName: field.displayName,
                DisplayFormat: field.displayFormat ?? null
            };
        }
    });

    // Process user info data - wrap in useMemo to prevent dependency changes on every render
    const userInfo: UserInfo = useMemo(() => ({
        username: data.userInfo.username,
        roleGroups: data.userInfo.roleGroups,
        showMyReportsMenu: data.userInfo.showMyReportsMenu,
        showTimesheetsMenu: data.userInfo.showTimesheetsMenu,
        firstName: data.userInfo.firstName,
        lastName: data.userInfo.lastName,
        email: data.userInfo.email,
        orgGUID: data.userInfo.orgGUID,
        orgName: data.userInfo.orgName,
        chapterId: data.userInfo.chapterId,
        phoneNumber: data.userInfo.phoneNumber,
        phoneTypeId: data.userInfo.phoneTypeId,
        preferredMfamethod: data.userInfo.preferredMfamethod,
        phoneNumberExtension: data.userInfo.phoneNumberExtension
    }), [data.userInfo]);

    // Populate Zustand store with user data for components that depend on it (like AppLayout)
    useEffect(() => {
        setUser({
            username: userInfo.username,
            roles: userInfo.roleGroups.map((role) => role as Constants.Role),
            permissions: [], // Not available in GraphQL response
            showMyReportsMenu: userInfo.showMyReportsMenu,
            showTimesheetsMenu: userInfo.showTimesheetsMenu,
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
            email: userInfo.email,
            orgGUID: userInfo.orgGUID,
            orgName: userInfo.orgName || '',
            chapterId: userInfo.chapterId,
            phoneNumber: userInfo.phoneNumber || '',
            phoneTypeId: userInfo.phoneTypeId || 0,
            preferredMfamethod: userInfo.preferredMfamethod || '',
            phoneNumberExtension: userInfo.phoneNumberExtension || null
        });
    }, [setUser, userInfo]);

    return (
        <FieldMetadataProvider value={fields}>
            <UserInfoProvider value={userInfo}>{children}</UserInfoProvider>
        </FieldMetadataProvider>
    );
};

// Error boundary to catch any errors in the metadata loading
class RelayEnvironmentErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean; error?: Error }> {
    constructor(props: { children: ReactNode }) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
        console.error('[RelayEnvironmentErrorBoundary] Error caught:', error);
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('[RelayEnvironmentErrorBoundary] Component error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{ padding: '20px', border: '1px solid red', margin: '20px' }}>
                    <h2>Error Loading Application Data</h2>
                    <p>Error: {this.state.error?.message}</p>
                    <p>Check browser console for details</p>
                </div>
            );
        }

        return this.props.children;
    }
}

// Main layout component now uses Suspense and the internal loader
export default function RelayEnvironmentLayout({ children }: { children: React.ReactNode }) {
    const [env, setEnv] = useState<Environment | null>(null);
    
    useEffect(() => {
        getRelayEnvironment().then(setEnv);
    }, []);
    
    if (!env) {
        return (
            <div style={{ padding: '20px', textAlign: 'center' }}>
                <div>Loading application...</div>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '10px' }}>Initializing cache and environment</div>
            </div>
        );
    }

    return (
        <RelayEnvironmentProvider environment={env}>
            <RelayEnvironmentErrorBoundary>
                <Suspense
                    fallback={
                        <div style={{ padding: '20px', textAlign: 'center' }}>
                            <div>Loading application data...</div>
                            <div style={{ fontSize: '12px', color: '#666', marginTop: '10px' }}>Check browser console for debug logs</div>
                        </div>
                    }>
                    <MetadataLoader>{children}</MetadataLoader>
                </Suspense>
            </RelayEnvironmentErrorBoundary>
        </RelayEnvironmentProvider>
    );
}
