import { ColumnType } from '@/src/types/rosters';
import { LABEL_TEXT } from '@/src/constants/text';
import { TimesheetRosterTableFragment$data } from '@/relay/__generated__/TimesheetRosterTableFragment.graphql';
import { useFieldMetadata } from '@/src/context/FieldMetadataContext';
import { useStableMemo } from '@/src/lib/hooks/useStableMemo';

type NodeType = NonNullable<NonNullable<TimesheetRosterTableFragment$data['timesheetsByEmployerGuid']>['edges']>[number]['node'];

type UITableData = {
    period: {
        year: number;
        weekNumber: number;
        endDate: string;
    };
    uid: string;
};

export type TableData = NodeType & UITableData;

const defaultColumnData: Omit<ColumnType, 'key' | 'label' | 'columnLabel' | 'position'> = {
    show: true,
    canHide: true,
    allowsFiltering: true,
    metaData: {
        allowsResizing: true,
        allowsSorting: true,
        children: undefined
    }
};

// Hook to get column data with metadata
export const useTimesheetRosterColumnData = (): ColumnType[] => {
    const metadata = useFieldMetadata();

    return useStableMemo(() => [
        {
            ...defaultColumnData,
            key: 'payPeriodEndDate',
            label: metadata?.Period?.DisplayName || LABEL_TEXT.PERIOD_LABEL,
            columnLabel: metadata?.Period?.DisplayName || LABEL_TEXT.PERIOD_LABEL,
            canHide: false,
            position: 1,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 200,
                allowsSorting: true
            }
        },
        {
            ...defaultColumnData,
            key: 'numericId',
            label: metadata?.NumericId?.DisplayName || LABEL_TEXT.ID_LABEL,
            columnLabel: metadata?.NumericId?.DisplayName || LABEL_TEXT.TIMESHEET_ID_LABEL,
            position: 2,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 90,
                allowsSorting: false
            }
        },
        {
            ...defaultColumnData,
            key: 'type',
            label: metadata?.Type?.DisplayName || LABEL_TEXT.TYPE_LABEL,
            columnLabel: metadata?.Type?.DisplayName || LABEL_TEXT.TYPE_LABEL,
            position: 3,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 110,
                allowsSorting: false
            }
        },
        {
            ...defaultColumnData,
            key: 'hoursWorked',
            label: metadata?.HoursWorked?.DisplayName || LABEL_TEXT.HOURS_WKD_LABEL,
            columnLabel: metadata?.HoursWorked?.DisplayName || LABEL_TEXT.HOURS_WORKED_LABEL,
            position: 4,
            allowsFiltering: false,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 150,
                allowsSorting: false
            }
        },
        {
            ...defaultColumnData,
            key: 'payStubCount',
            label: metadata?.Stubs?.DisplayName || LABEL_TEXT.STUBS_LABEL,
            columnLabel: metadata?.Stubs?.DisplayName || LABEL_TEXT.STUBS_LABEL,
            position: 5,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 110,
                allowsSorting: false
            }
        },
        {
            ...defaultColumnData,
            key: 'modificationDate',
            label: metadata?.LastModified?.DisplayName || LABEL_TEXT.LAST_MODIFIED_LABEL,
            columnLabel: metadata?.LastModified?.DisplayName || LABEL_TEXT.LAST_MODIFIED_LABEL,
            position: 6,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 200,
                allowsResizing: false
            }
        },
        {
            ...defaultColumnData,
            key: 'modifiedByUserId',
            label: LABEL_TEXT.ACTIONS_LABEL,
            columnLabel: metadata?.LastModifiedBy?.DisplayName || LABEL_TEXT.LAST_MODIFIED_BY_LABEL,
            position: 7,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 120,
                allowsSorting: false
            }
        },
        {
            ...defaultColumnData,
            key: 'status',
            label: metadata?.Status?.DisplayName || LABEL_TEXT.STATUS_LABEL,
            columnLabel: metadata?.Status?.DisplayName || LABEL_TEXT.STATUS_LABEL,
            position: 8,
            metaData: {
                ...defaultColumnData.metaData,
                defaultWidth: 105,
                allowsSorting: false
            }
        },
        {
            key: 'actions',
            show: true,
            canHide: false,
            label: LABEL_TEXT.ACTIONS_LABEL,
            columnLabel: LABEL_TEXT.ACTIONS_LABEL,
            position: 9,
            allowsFiltering: false,
            metaData: {
                defaultWidth: 30,
                allowsResizing: false,
                allowsSorting: false,
                children: undefined
            }
        }
    ], [metadata]);
};

export const TimesheetTypeOptions = [
    {
        label: 'Primary',
        value: 'Primary'
    },
    {
        label: 'Additional',
        value: 'Additional'
    }
];

export const TimesheetStatusOptions = [
    {
        label: 'Saved',
        value: 'Saved'
    },
    {
        label: 'Submitted',
        value: 'Submitted'
    }
];

// Map UI key to the key in the Dto
export const keyMapForSortingFields: { [key: string]: string } = {
    name: 'name',
    payPeriodEndDate: 'payPeriodEndDate',
    modificationDate: 'modificationDate',
    modifiedByUserId: 'modifiedByUserId',
    payStubCount: 'payStubCount'
};
