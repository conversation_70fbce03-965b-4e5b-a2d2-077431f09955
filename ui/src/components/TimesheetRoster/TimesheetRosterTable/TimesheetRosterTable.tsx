import { graphql } from 'relay-runtime';
import { SortOrder } from '@/src/types/table';
import { ColumnType } from '@/src/types/rosters';
import { ToastQueue } from '@react-spectrum/toast';
import { usePaginationFragment } from 'react-relay';
import { DynamicCell } from './DynamicCell/DynamicCell';
import type { SortDescriptor, LoadingState } from '@react-types/shared';
import { LoadingStates, isLoading } from '@/src/services/spectrum/table';
import { TableData, keyMapForSortingFields } from './TimesheetRoster.data';
import ContainerLoader from '../../UI/Loader/ContainerLoader/ContainerLoader';
import React, { Suspense, useEffect, useMemo, useState, useTransition } from 'react';
import EmptyTableState from '../../EmployerRoster/EmployerRosterTable/EmptyTableState';
import EPRDynamicColumns from '../../EmployerRoster/EmployerRosterTable/EPRDynamicColumns';
import { Cell, Flex, TableHeader, TableView, TableBody, Row } from '@adobe/react-spectrum';
import { TimesheetRosterQuery$variables } from '@/relay/__generated__/TimesheetRosterQuery.graphql';
import InfiniteScrollTableFooter from '@/src/components/UI/InfiniteScrollTableFooter/InfiniteScrollTableFooter';
import { TimesheetRosterTableFragment$key } from '@/relay/__generated__/TimesheetRosterTableFragment.graphql';
import { TimesheetRosterQuery as TimesheetRosterQueryType } from '@/relay/__generated__/TimesheetRosterQuery.graphql';
import { getNodesFromTimesheetRosterFragment, getTotalCountFromTimesheetRosterFragment } from '@/src/services/timesheet-roster';
import { useMutation } from 'react-relay';
import { ConnectionHandler } from 'relay-runtime';
import { TimesheetRosterTableDeleteTimesheetMutation } from '@/relay/__generated__/TimesheetRosterTableDeleteTimesheetMutation.graphql';

type Props = {
    columnData: ColumnType[];
    filter: TimesheetRosterQuery$variables;
    queryRef: TimesheetRosterTableFragment$key;
    onRootSortChange: (sortData: SortOrder) => void;
};

const DeleteTimesheetMutation = graphql`
    mutation TimesheetRosterTableDeleteTimesheetMutation($input: DeleteTimesheetInput!) {
        deleteTimesheet(input: $input) {
            int
        }
    }
`;

const TimesheetRosterTableFragment = graphql`
    fragment TimesheetRosterTableFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        employerGuid: { type: "UUID!" }
        # last: { type: "Int!", defaultValue: 40 }
        order: { type: "[TimeSheetSortInput!]", defaultValue: null }
        where: { type: "TimeSheetFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "TimesheetRosterRefetchQuery") {
        timesheetsByEmployerGuid(first: $first, after: $after, employerGuid: $employerGuid, order: $order, where: $where)
            @connection(key: "TimesheetRosterTableFragment_timesheetsByEmployerGuid") {
            edges {
                node {
                    id
                    name
                    numericId
                    payPeriodEndDate
                    status
                    type
                    hoursWorked
                    oldId
                    creationDate
                    modificationDate
                    showBonusColumn
                    showDTHoursColumn
                    showEarningsCodesColumn
                    showExpensesColumn
                    payStubCount
                    modifiedByUserId
                }
            }
            pageInfo {
                hasPreviousPage
                startCursor
            }
            totalCount
        }
    }
`;

const DEFAULT_SORT_DESCRIPTOR: SortDescriptor = {
    column: 'payPeriodEndDate',
    direction: 'descending'
};

const TimesheetRosterTable = ({ queryRef, filter, columnData, onRootSortChange }: Props) => {
    const { data, hasNext, loadNext, isLoadingNext, refetch } = usePaginationFragment<
        TimesheetRosterQueryType,
        TimesheetRosterTableFragment$key
    >(TimesheetRosterTableFragment, queryRef);

    const [commitDeleteMutation, isDeleting] = useMutation<TimesheetRosterTableDeleteTimesheetMutation>(DeleteTimesheetMutation);

    const handleDeleteTimesheet = (timesheetId: string) => {
        commitDeleteMutation({
            variables: { input: { id: timesheetId } },
            onCompleted: (response, errors) => {
                if (errors) {
                    ToastQueue.negative(`Error deleting timesheet: ${errors.map((e) => e.message).join(', ')}`, { timeout: 7000 });
                    console.error('Delete Timesheet Error:', errors);
                } else {
                    ToastQueue.positive(`Timesheet deleted successfully.`, { timeout: 7000 });
                }
            },
            onError: (error) => {
                ToastQueue.negative(`Failed to delete timesheet: ${error.message}`, { timeout: 7000 });
                console.error('Delete Timesheet Network/Server Error:', error);
            },
            optimisticUpdater: (store) => {
                const connectionRecord = ConnectionHandler.getConnection(
                    store.getRoot(),
                    'TimesheetRosterTableFragment_timesheetsByEmployerGuid',
                    {
                        employerGuid: filter.employerGuid,
                        order: filter.order ?? null,
                        where: filter.where ?? null
                    }
                );
                if (connectionRecord) {
                    ConnectionHandler.deleteNode(connectionRecord, timesheetId);
                }
            },
            updater: (store) => {
                const connectionRecord = ConnectionHandler.getConnection(
                    store.getRoot(),
                    'TimesheetRosterTableFragment_timesheetsByEmployerGuid',
                    {
                        employerGuid: filter.employerGuid,
                        order: filter.order ?? null,
                        where: filter.where ?? null
                    }
                );
                if (connectionRecord) {
                    ConnectionHandler.deleteNode(connectionRecord, timesheetId);
                }
            }
        });
    };

    const pageSize = 20;
    const [isSortingPending, startSortingTransition] = useTransition();
    const [loadingState, setLoadingState] = useState<LoadingState>(LoadingStates.IDLE);
    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>(DEFAULT_SORT_DESCRIPTOR);
    const [hasInitializedFirstPageData, setHasInitializedFirstPageData] = useState(false);
    const isPendingListLoading = useMemo(() => isLoading(loadingState) || isLoadingNext, [loadingState, isLoadingNext]);

    const rows = useMemo<TableData[]>(() => {
        if (isSortingPending) return [];
        return getNodesFromTimesheetRosterFragment(data) as TableData[];
    }, [data, isSortingPending]);

    const filterCount = useMemo(() => filter.where?.and?.length ?? 0, [filter]);
    const totalCount = useMemo(() => getTotalCountFromTimesheetRosterFragment(data), [data]);

    useEffect(() => {
        setHasInitializedFirstPageData(true);
    }, []);

    useEffect(() => {
        setLoadingState(isSortingPending ? LoadingStates.LOADING : LoadingStates.IDLE);
    }, [isSortingPending]);

    const getNextPaginatedQuery = (pageSize: number) => {
        loadNext(pageSize, {
            UNSTABLE_extraVariables: { ...filter },
            onComplete: (args: Error | null) => {
                if (args) {
                    ToastQueue.negative(`Error fetching Timesheet Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                }
                setLoadingState(LoadingStates.IDLE);
            }
        });
    };

    const onLoadMoreHandler = () => {
        if (!hasInitializedFirstPageData || isPendingListLoading || !hasNext || isSortingPending) {
            return;
        }

        setLoadingState(LoadingStates.LOADING_MORE);
        getNextPaginatedQuery(pageSize);
    };

    const onSortChangeHandler = (sortDescriptor: SortDescriptor) => {
        if (isPendingListLoading) {
            return;
        }

        setLoadingState(LoadingStates.SORTING);
        setSortDescriptor(sortDescriptor);

        startSortingTransition(() => {
            sortListOnServerSide(sortDescriptor);
        });
    };

    const sortListOnServerSide = async (sortDescriptor: SortDescriptor) => {
        let field = (sortDescriptor.column as string) || keyMapForSortingFields[0];
        field = keyMapForSortingFields[field];

        const direction = sortDescriptor.direction === 'ascending' ? 'ASC' : 'DESC';
        const newOrder: SortOrder = [{ [field]: direction }];

        onRootSortChange(newOrder);

        refetch(
            { order: newOrder, where: filter.where, employerGuid: filter.employerGuid },
            {
                onComplete: (args: Error | null) => {
                    if (args) {
                        ToastQueue.negative(`Error fetching Timesheet Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                    }
                }
            }
        );
    };

    const columnsUI = EPRDynamicColumns(columnData, isSortingPending || isPendingListLoading || rows.length === 0);

    return (
        <Flex width="100%" direction="column" flex={1} marginBottom={'size-600'} marginTop={'size-300'}>
            <Suspense fallback={<ContainerLoader />}>
                <TableView
                    maxWidth={'1200px'}
                    isQuiet
                    flex
                    flexGrow={1}
                    flexBasis={0}
                    flexShrink={0}
                    aria-label="Timesheet Roster Table"
                    onSortChange={onSortChangeHandler}
                    sortDescriptor={sortDescriptor}
                    renderEmptyState={() => <EmptyTableState isFilterActive={filterCount > 0} loadingState={loadingState} />}>
                    <TableHeader>{columnsUI}</TableHeader>

                    <TableBody items={rows} loadingState={loadingState} onLoadMore={onLoadMoreHandler}>
                        {(item: TableData) => (
                            <Row key={item.id}>{(key) => <Cell>{DynamicCell(key, item, handleDeleteTimesheet, filter.employerGuid)}</Cell>}</Row>
                        )}
                    </TableBody>
                </TableView>

                <InfiniteScrollTableFooter totalCount={totalCount} />
            </Suspense>
        </Flex>
    );
};

export default TimesheetRosterTable;
