import { DATE_FILTER_OPTIONS } from '@/src/constants/date-filter-options';
import { TimeSheetFilterInput } from '@/relay/__generated__/TimesheetRosterQuery.graphql';
import { parseISO, isValid, subDays, isSameDay, format } from 'date-fns';

// Mock function to test the date filter handling logic
function testDateFilterHandling(filterValueObject: any, fieldName: string): string {
    // We no longer use the _key property, so we rely on date range calculation
    if (filterValueObject.gte && filterValueObject.lte) {
        // For LocalDate format (YYYY-MM-DD), we need to check the string format
        const startDateStr = filterValueObject.gte;

        // Parse and validate the date using date-fns
        const parsedStartDate = parseISO(startDateStr);
        if (!isValid(parsedStartDate)) {
            console.error(`Invalid date string encountered in ${fieldName} filter: '${startDateStr}'. Defaulting to All Time.`);
            return ''; // Default to All Time
        }

        const now = new Date();
        const currentYear = now.getFullYear();

        // Check if the start date is January 1st of the current year (Year to date)
        const yearStartDateStr = `${currentYear}-01-01`;
        const isYearToDate = startDateStr === yearStartDateStr;

        // Calculate the date 30 days ago using date-fns
        const thirtyDaysAgoDate = subDays(now, 30);

        // Format to string for comparison if needed
        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

        // Use isSameDay for precise date comparison, ignoring time components
        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

        // Check if it's 1970-01-01 (All time)
        const isAllTime = startDateStr === '1970-01-01';

        if (isYearToDate) {
            // Year to date - start date is January 1st of current year
            return 'year_to_date';
        } else if (isLast30Days) {
            // Last 30 days
            return 'last_30_days';
        } else if (isAllTime) {
            // All time
            return '';
        } else {
            // If we can't determine the predefined range, default to empty (All Time)
            // This is a safer default than showing an incorrect selection
            return '';
        }
    } else if (filterValueObject.gte && !filterValueObject.lte) {
        // For LocalDate format (YYYY-MM-DD), we need to check the string format
        const startDateStr = filterValueObject.gte;

        // Parse and validate the date using date-fns
        const parsedStartDate = parseISO(startDateStr);
        if (!isValid(parsedStartDate)) {
            console.error(`Invalid date string encountered in ${fieldName} filter: '${startDateStr}'. Defaulting to All Time.`);
            return ''; // Default to All Time
        }

        const now = new Date();
        const currentYear = now.getFullYear();

        // Check if the start date is January 1st of the current year (Year to date)
        const yearStartDateStr = `${currentYear}-01-01`;
        const isYearToDate = startDateStr === yearStartDateStr;

        // Calculate the date 30 days ago using date-fns
        const thirtyDaysAgoDate = subDays(now, 30);

        // Format to string for comparison if needed
        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

        // Use isSameDay for precise date comparison, ignoring time components
        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

        // Check if it's 1970-01-01 (All time)
        const isAllTime = startDateStr === '1970-01-01';

        if (isYearToDate) {
            // Year to date - start date is January 1st of current year
            return 'year_to_date';
        } else if (isLast30Days) {
            // Last 30 days
            return 'last_30_days';
        } else if (isAllTime) {
            // All time
            return '';
        } else {
            return '';
        }
    } else if (!filterValueObject.gte && filterValueObject.lte) {
        // Handle case with only end date (everything before X)
        // This doesn't match our predefined options well, so default to All Time
        return '';
    } else if (!filterValueObject.gte && !filterValueObject.lte) {
        // No date range specified, use 'All Time'
        return '';
    }

    // Default fallback
    return '';
}

describe('Date Filter Handling', () => {
    // We no longer use the _key property, so we rely on date range calculation

    // Test with date ranges
    test('should identify last_30_days from date range', () => {
        const now = new Date();
        const thirtyDaysAgoDate = subDays(now, 30);

        const filterObject = {
            gte: format(thirtyDaysAgoDate, 'yyyy-MM-dd'),
            lte: format(now, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('last_30_days');
    });

    test('should identify year_to_date from date range (January 1st of current year)', () => {
        const now = new Date();
        const currentYear = now.getFullYear();
        const yearStartDate = new Date(currentYear, 0, 1); // January 1st of current year

        const filterObject = {
            gte: format(yearStartDate, 'yyyy-MM-dd'),
            lte: format(now, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('year_to_date');
    });

    test('should identify All Time from 1970 date', () => {
        const now = new Date();
        const allTimeStartDate = new Date('1970-01-01');

        const filterObject = {
            gte: format(allTimeStartDate, 'yyyy-MM-dd'),
            lte: format(now, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
    });

    // Test with only start date
    test('should handle only start date (last_30_days)', () => {
        const now = new Date();
        const thirtyDaysAgoDate = subDays(now, 30);

        const filterObject = {
            gte: format(thirtyDaysAgoDate, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('last_30_days');
    });

    test('should handle only start date (year_to_date)', () => {
        const currentYear = new Date().getFullYear();
        const yearStartDate = new Date(currentYear, 0, 1); // January 1st of current year

        const filterObject = {
            gte: format(yearStartDate, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('year_to_date');
    });

    // Test with only end date
    test('should handle only end date', () => {
        const now = new Date();

        const filterObject = {
            lte: format(now, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
    });

    // Test with no date constraints
    test('should handle empty date constraints', () => {
        const filterObject = {};

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
    });

    // Test with date range that doesn't match predefined options
    test('should default to All Time for custom date ranges', () => {
        const now = new Date();
        const hundredDaysAgoDate = subDays(now, 100);

        const filterObject = {
            gte: format(hundredDaysAgoDate, 'yyyy-MM-dd'),
            lte: format(now, 'yyyy-MM-dd')
        };

        expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
    });

    // Test with invalid date strings
    describe('Invalid Date Handling', () => {
        // Spy on console.error to verify it's called
        let consoleErrorSpy: jest.SpyInstance;

        beforeEach(() => {
            consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        });

        afterEach(() => {
            consoleErrorSpy.mockRestore();
        });

        test('should handle completely invalid date string', () => {
            const filterObject = {
                gte: 'invalid-date',
                lte: '2023-12-31'
            };

            expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Invalid date string encountered in payPeriodEndDate filter')
            );
        });

        test('should handle malformed but parsable date string', () => {
            const filterObject = {
                gte: '2023-13-01', // Invalid month (13)
                lte: '2023-12-31'
            };

            expect(testDateFilterHandling(filterObject, 'payPeriodEndDate')).toBe('');
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Invalid date string encountered in payPeriodEndDate filter')
            );
        });

        test('should handle invalid date string with only start date', () => {
            const filterObject = {
                gte: 'not-a-date'
            };

            expect(testDateFilterHandling(filterObject, 'modificationDate')).toBe('');
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Invalid date string encountered in modificationDate filter')
            );
        });
    });
});
