import { parse, format } from 'date-fns';
import type { TimeSheetDetailRow_payStubDetail$key } from '@/relay/__generated__/TimeSheetDetailRow_payStubDetail.graphql';

/**
 * Type guard to check if a detail is a Relay ref (fragment pointer)
 */
export function isRelayDetailRef(detail: any): detail is TimeSheetDetailRow_payStubDetail$key {
    return detail && typeof detail === 'object' && '__fragments' in detail;
}

/**
 * Parse a date string into a Date object for timezone-neutral handling
 * Handles both ISO timestamps and yyyy-MM-dd formats
 * @param dateString The date string to parse
 * @returns Date object or null if invalid
 */
export const parseWorkDate = (dateString: string | null | undefined): Date | null => {
    if (!dateString) return null;

    try {
        if (dateString.includes('T')) {
            const isoDate = new Date(dateString);
            if (!isNaN(isoDate.getTime())) return isoDate;
        }

        const parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());
        if (isNaN(parsedDate.getTime())) {
            throw new Error(`Invalid date format or value: ${dateString}`);
        }
        return parsedDate;
    } catch (error) {
        console.error('Error parsing workDate:', dateString, error);
        return null;
    }
};

/**
 * Format a date for display in the day column (e.g., 'Mon')
 */
export const formatDayName = (dateObj: Date | null): string => {
    if (!dateObj || isNaN(dateObj.getTime())) return '';
    return format(dateObj, 'EEE');
};

/**
 * Format a date for display in the work date column (e.g., '01/15/2023')
 */
export const formatWorkDate = (dateObj: Date | null): string => {
    if (!dateObj || isNaN(dateObj.getTime())) return '';
    return format(dateObj, 'MM/dd/yyyy');
};
