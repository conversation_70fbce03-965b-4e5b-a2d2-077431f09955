import React, { useMemo } from 'react';
import { useLazyLoadQuery, useFragment, graphql } from 'react-relay';
import { EmployeeDataFragment } from '@/src/fragments/EmployeeDataFragment';
import type { EmployeeDataFragment$key } from '@/relay/__generated__/EmployeeDataFragment.graphql';
import type { EmployeeSelectorWithDataQuery as EmployeeSelectorWithDataQueryType } from '@/relay/__generated__/EmployeeSelectorWithDataQuery.graphql';
import EmployeeSelector from './EmployeeSelector';

/**
 * Query for loading employee data for the selector
 */
const EmployeeSelectorWithDataQuery = graphql`
    query EmployeeSelectorWithDataQuery($employerGuid: UUID!) {
        ...EmployeeDataFragment @arguments(employerGuid: $employerGuid)
    }
`;

/**
 * Interface for processed employee data for UI consumption
 */
interface EmployeeUIData {
    id: string;
    value: string;
    text: string;
    firstName: string | null | undefined;
    lastName: string | null | undefined;
    middleName: string | null | undefined;
    active: boolean | null | undefined;
}

interface EmployeeSelectorWithDataProps {
    employerGuid: string;
    payStubId: string;
    timesheetId: string;
    selectedKey: string | null;
    isDisabled?: boolean;
    'aria-label': string;
    autoFocus?: boolean;
    onBlur?: () => void;
    onEmployeeSelect?: (id: string) => Promise<void>;
}

/**
 * EmployeeSelectorWithData - Fetches employee data and renders EmployeeSelector
 * 
 * This component follows Relay best practices by:
 * - Using useLazyLoadQuery for component-specific data
 * - Colocating data requirements with the component that uses them
 * - Converting GraphQL data to UI format without using deprecated context providers
 */
const EmployeeSelectorWithData: React.FC<EmployeeSelectorWithDataProps> = ({
    employerGuid,
    timesheetId,
    payStubId,
    selectedKey,
    isDisabled,
    'aria-label': ariaLabel,
    autoFocus,
    onBlur,
    onEmployeeSelect
}) => {
    // Fetch employee data using Relay
    const data = useLazyLoadQuery<EmployeeSelectorWithDataQueryType>(
        EmployeeSelectorWithDataQuery,
        { employerGuid }
    );
    
    // Resolve the fragment data
    const employeeFragmentData = useFragment(EmployeeDataFragment, data as EmployeeDataFragment$key);

    // Convert GraphQL employee data to UI format
    const employeeUIData: ReadonlyArray<EmployeeUIData> = useMemo(() => {
        const employees = employeeFragmentData.employeesByEmployerGuidAsync?.edges
            ?.map((edge: any) => edge?.node)
            .filter(Boolean)
            .map((emp: any) => {
                const firstName = emp.firstName || '';
                const lastName = emp.lastName || '';
                const displayText = firstName ? `${lastName}, ${firstName}` : lastName;

                return {
                    id: emp.id,
                    value: emp.id,
                    text: displayText.trim(),
                    firstName: emp.firstName,
                    lastName: emp.lastName,
                    middleName: emp.middleName,
                    active: emp.active
                };
            })
            .sort((a: EmployeeUIData, b: EmployeeUIData) => {
                const textA = a.text || '';
                const textB = b.text || '';
                return textA.localeCompare(textB);
            }) || [];

        return employees;
    }, [employeeFragmentData]);

    return (
        <EmployeeSelector
            payStubId={payStubId}
            timesheetId={timesheetId}
            selectedKey={selectedKey}
            isDisabled={isDisabled}
            aria-label={ariaLabel}
            autoFocus={autoFocus}
            onBlur={onBlur}
            employees={employeeUIData}
            onEmployeeSelect={onEmployeeSelect}
        />
    );
};

export default EmployeeSelectorWithData;
