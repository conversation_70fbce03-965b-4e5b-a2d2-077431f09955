import React, { useMemo, Suspense } from 'react';
import { graphql, useFragment, useRelayEnvironment } from 'react-relay';
import { Heading, Button, Flex, View, Text, ProgressCircle, DialogContainer, Dialog, Content, ButtonGroup } from '@adobe/react-spectrum';
import TimeSheetGrid from './TimeSheetGrid';
import PayPeriodEnd from './PayPeriodEnd';
import EmployeeSelectorWithData from './EmployeeSelectorWithData';

import type { TimesheetDetailView_timeSheet$key } from '@/relay/__generated__/TimesheetDetailView_timeSheet.graphql';
import type { TimeSheetSettingsState } from './TimeSheetSettings';


import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { useTimesheetContext } from './TimesheetContext';

// Styles for screen reader accessibility
const SCREEN_READER_ONLY_STYLE = {
    position: 'absolute' as const,
    left: '-10000px',
    width: '1px',
    height: '1px',
    overflow: 'hidden' as const,
    visibility: 'hidden' as const
};

// Define the GraphQL Fragment for TimesheetDetailView
// eslint-disable-next-line custom/relay-fragment-dependencies -- Fragment correctly spread for TimeSheetGrid child component
const TimesheetDetailViewFragment = graphql`
    fragment TimesheetDetailView_timeSheet on TimeSheet {
        numericId
        name
        status
        payPeriodEndDate
        # Fragment spread for child TimeSheetGrid component (used in timeSheetRef prop)
        ...TimeSheetGrid_timeSheet
    }
`;

/**
 * Props for TimesheetDetailView component
 *
 * @param timeSheetRef - Relay fragment reference for TimeSheet data
 * @param onSave - Save callback function
 * @param onCancel - Cancel callback function
 * @param onSaveForLater - Save for later callback function
 * @param isSaving - Loading state for save operations
 * @param isDirty - Whether the timesheet has unsaved changes
 * @param settings - UI settings state
 * @param onSettingsChange - Settings update callback
 */
interface TimesheetDetailViewProps {
    timeSheetRef: TimesheetDetailView_timeSheet$key; // Use the child fragment key type
    queryRef: any; // Query reference to pass down to child components
    onSave: () => void;
    onCancel: () => void;
    onSaveForLater: () => Promise<void>;
    isSaving: boolean;
    isDirty: boolean;
    settings: TimeSheetSettingsState;
    onSettingsChange: (newSettings: TimeSheetSettingsState) => void;
}

/**
 * TimesheetDetailView - Main view component for timesheet editing
 *
 * Data Flow:
 * - Receives TimeSheet fragment reference from parent container
 * - Uses useFragment to access TimeSheet data from Relay store
 * - Passes fragment references down to child components
 * - Never stores or duplicates TimeSheet data locally
 *
 * Architecture:
 * - Fragment-based data access (single source of truth)
 * - Child components receive their own fragment references
 * - UI state managed separately from server data
 */
const TimesheetDetailView: React.FC<TimesheetDetailViewProps> = ({
    timeSheetRef,
    queryRef,
    onSave,
    onCancel,
    onSaveForLater,
    isSaving,
    isDirty,
    settings,
    onSettingsChange
}) => {
    const timeSheet = useFragment(TimesheetDetailViewFragment, timeSheetRef);
    const environment = useRelayEnvironment();

    // Get context values instead of props (eliminates prop drilling)
    const { timesheetId, numericId, employerGuid } = useTimesheetContext();

    // Performance-critical: Use stable selectors + useMemo pattern
    // This prevents infinite loops by ensuring object identity stability
    // See: https://github.com/pmndrs/zustand#selecting-multiple-state-slices

    // Raw selectors - return primitive references from store
    const rawErrorsByPayStubId = useTimesheetUIStore((state) => state.errorsByPayStubId);
    const rawSavingPayStubIds = useTimesheetUIStore((state) => state.savingPayStubIds);

    // Memoized transformations - stable object references
    const errorsByPayStubId = useMemo(() => {
        const filteredMap = new Map();
        Array.from(rawErrorsByPayStubId.entries()).forEach(([key, error]) => {
            if (key.startsWith(`${timesheetId}:`)) {
                const shortKey = key.substring(`${timesheetId}:`.length);
                filteredMap.set(shortKey, error);
            }
        });
        return filteredMap;
    }, [rawErrorsByPayStubId, timesheetId]);

    const savingPayStubIds = useMemo(() => {
        const filteredSet = new Set();
        Array.from(rawSavingPayStubIds).forEach((key) => {
            if (key.startsWith(`${timesheetId}:`)) {
                const shortKey = key.substring(`${timesheetId}:`.length);
                filteredSet.add(shortKey);
            }
        });
        return filteredSet;
    }, [rawSavingPayStubIds, timesheetId]);

    // Action selectors (these are stable function references)
    const showingEmployeeSelector = useTimesheetUIStore((state) => state.showingEmployeeSelector);
    const selectedEmployeeForAdd = useTimesheetUIStore((state) => state.selectedEmployeeForAdd);
    const hideEmployeeSelector = useTimesheetUIStore((state) => state.hideEmployeeSelector);
    const addEmployeeWithMutation = useTimesheetUIStore((state) => state.addEmployeeWithMutation);



    // No longer need useState for derived messages

    const readOnly = timeSheet?.status !== 'New' && timeSheet?.status !== 'Saved';

    // Simple derived state - no effects needed!
    const errorMessage = useMemo(() => {
        const errorCount = errorsByPayStubId.size;
        if (errorCount > 0) {
            return `${errorCount} pay stub${errorCount > 1 ? 's' : ''} failed to save`;
        }
        return '';
    }, [errorsByPayStubId]);

    const statusMessage = useMemo(() => {
        const savingCount = savingPayStubIds.size;
        if (savingCount > 0) {
            return `Saving ${savingCount} pay stub${savingCount > 1 ? 's' : ''}...`;
        }
        return '';
    }, [savingPayStubIds]);

    // For now, keep it simple - no auto-clearing success message
    // Success feedback can be handled by the parent component or context
    const displayStatusMessage = statusMessage;

    if (!timeSheet) {
        return (
            <View padding="size-200" borderColor="notice" borderWidth="thin" borderRadius="medium">
                <Heading level={3}>Timesheet Data Unavailable</Heading>
                <Text>Could not load timesheet details.</Text>
            </View>
        );
    }

    // Button state logic
    const isSaveDisabled = readOnly || isSaving || !isDirty;

    // Submit button logic:
    // Disabled if saving, or readOnly (status not New/Saved).
    // If status is 'New', also disabled if !isDirty.
    // If status is 'Saved', !isDirty does not disable it (unless readOnly or isSaving).
    const isSubmitDisabled = isSaving || readOnly || (timeSheet.status === 'New' && !isDirty);

    const headerNumericId = timeSheet.numericId;
    const headerName = timeSheet.name ?? 'Timesheet';

    return (
        <View padding="size-300">
            {/* Live regions for screen reader announcements */}
            <div role="status" aria-live="polite" aria-atomic="true" style={SCREEN_READER_ONLY_STYLE}>
                {displayStatusMessage}
            </div>
            <div role="alert" aria-live="assertive" style={SCREEN_READER_ONLY_STYLE}>
                {errorMessage}
            </div>

            <Flex direction="column" gap="size-300">
                <Flex direction="row" justifyContent="space-between" alignItems="center">
                    <Heading level={1} marginTop="size-0" marginBottom="size-100" id="timesheet-title">
                        {headerName} (#{headerNumericId})
                    </Heading>
                    <Flex direction="row" gap="size-200" alignItems="center">
                        <PayPeriodEnd payPeriodEndDate={timeSheet.payPeriodEndDate} />
                        <View
                            borderWidth="thin"
                            borderColor="gray-300"
                            borderRadius="regular"
                            paddingX="size-125"
                            paddingY="size-65"
                            alignSelf="center">
                            <Text>Status: </Text>
                            <Text>
                                <strong>{timeSheet.status}</strong>
                            </Text>
                        </View>
                    </Flex>
                </Flex>



                <TimeSheetGrid
                    timeSheetRef={timeSheet}
                    queryRef={queryRef}
                    status={timeSheet.status}
                    readOnly={readOnly}
                    settings={settings}
                    onSettingsChange={onSettingsChange}
                />

                <View borderTopWidth="thin" borderColor="gray-300" padding="size-200" marginTop="size-400">
                    <Flex direction="row" justifyContent="end" gap="size-150">
                        <Button variant="secondary" onPress={onCancel} aria-label="Cancel timesheet editing and return to previous page">
                            Cancel
                        </Button>

                        <Button
                            variant="secondary"
                            onPress={onSaveForLater}
                            isDisabled={isSaveDisabled}
                            aria-label={
                                isSaveDisabled ? 'Save timesheet (disabled - no changes to save)' : 'Save timesheet for later editing'
                            }>
                            {isSaving ? <ProgressCircle size="S" aria-label="Saving..." isIndeterminate /> : 'Save'}
                        </Button>

                        <Button
                            variant="accent"
                            autoFocus
                            onPress={onSave}
                            isDisabled={isSubmitDisabled}
                            aria-label={isSubmitDisabled ? 'Submit timesheet (disabled)' : 'Submit timesheet for processing'}>
                            {isSaving ? <ProgressCircle size="S" aria-label="Saving..." isIndeterminate /> : 'Submit'}
                        </Button>
                    </Flex>
                </View>
            </Flex>

            {/* Employee Selector Dialog */}
            <DialogContainer onDismiss={hideEmployeeSelector} isDismissable type="modal">
                {showingEmployeeSelector && selectedEmployeeForAdd && (
                    <Dialog>
                        <Heading>Add Employee to Timesheet</Heading>
                        <Content>
                            <Text>Select an employee to add to this timesheet:</Text>
                            <View marginTop="size-200">
                                <Suspense
                                    fallback={
                                        <Flex justifyContent="center" alignItems="center" marginTop="size-200">
                                            <ProgressCircle aria-label="Loading employees" isIndeterminate size="S" />
                                        </Flex>
                                    }>
                                    <EmployeeSelectorWithData
                                        employerGuid={employerGuid}
                                        payStubId="temp-employee-selector"
                                        timesheetId={timesheetId}
                                        selectedKey={null}
                                        aria-label="Select employee to add to timesheet"
                                        autoFocus
                                        onEmployeeSelect={async (employeeId: string) => {
                                            try {
                                                // Additional validation before calling mutation
                                                if (!selectedEmployeeForAdd) {
                                                    console.error(
                                                        'Employee selection failed: selectedEmployeeForAdd is missing',
                                                        selectedEmployeeForAdd
                                                    );
                                                    throw new Error('Unable to add employee: Timesheet information is not available');
                                                }

                                                // employeeId is already a Relay Global ID, pass it directly
                                                await addEmployeeWithMutation(
                                                    selectedEmployeeForAdd,
                                                    employeeId,
                                                    environment,
                                                    numericId,
                                                    employerGuid
                                                );
                                            } catch (error) {
                                                console.error('Failed to add employee:', error);
                                                // Error handling is managed by the context
                                            }
                                        }}
                                    />
                                </Suspense>
                            </View>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={hideEmployeeSelector}>
                                Cancel
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>
        </View>
    );
};

export default TimesheetDetailView;
