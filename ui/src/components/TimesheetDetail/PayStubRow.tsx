/**
 * PayStubRow - Phase 5: Flat Types Migration Complete
 *
 * This component demonstrates the Phase 5 pattern:
 * - Uses Relay fragments for data loading (GraphQL boundary)
 * - Uses flat types directly without domain model conversion
 * - Passes flat data with draft overlays to UI components
 * - Maintains type safety with schema-aware flat types
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ PayStubRow (Fragment + Flat Types)      │ ← THIS COMPONENT
 * ├─────────────────────────────────────────┤
 * │ PayStubUI (Flat Data Consumer)          │ ← Flat types consumer
 * └─────────────────────────────────────────┘
 */

import React, { useMemo } from 'react';
import { useFragment } from 'react-relay';
import type { PayStubTable_payStub$data, PayStubTable_payStub$key } from '@/relay/__generated__/PayStubTable_payStub.graphql';
import type { EmployeeDisplayFragment_employee$key, TimeSheetDetailRow_employee$key } from '@/src/types/graphql-employee';
import type { FlatPayStubWithDrafts } from '@/src/hooks/useFlatPayStub';

// Enhanced flat data interface for UI consumption
interface EnhancedFlatPayStub extends FlatPayStubWithDrafts {
  // Employee-specific fields for UI
  employeeName: string;
  employeeFirstName: string;
  employeeLastName: string;
  employeeExternalId: string;
  employeeActive: boolean;
  
  // Pre-computed aggregates for performance
  totalStandardHours: number;
  totalOvertimeHours: number;
  totalDoubleTimeHours: number;
}
import { formatEmployeeName } from '@/src/utils/employeeUtils';
import { useFlatPayStub } from '@/src/hooks/useFlatPayStub';
import { indexDraftsByDetailId, getEffectiveValue, calculateHourTotals } from '@/src/utils/flat-type-utilities';
import { PayStubUI } from './PayStubUI';
import { EmployeeDisplayFragment } from '@/src/fragments/EmployeeDisplayFragment';
import { TimeSheetDetailRow_employeeFragment } from './TimeSheetDetailRow';

interface PayStubRowProps {
    payStub: PayStubTable_payStub$data; // Use resolved data instead of fragment key
    payStubFragmentRef: PayStubTable_payStub$key; // Fragment reference for nested components
    index: number;
    readOnly: boolean;
    employees?: ReadonlyArray<any>; // For employee selection
    // Column visibility flags
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    // Helper functions
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    payPeriodEndDate: string | null | undefined;
    // Timesheet context
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    // Event handlers that work with enhanced flat types
    onEdit?: (payStub: EnhancedFlatPayStub) => void;
    onDelete?: (payStub: EnhancedFlatPayStub) => void;
    onToggleExpanded?: (payStub: EnhancedFlatPayStub) => void;
}

/**
 * Fragment-based data loading component
 * Uses useFlatPayStub hook for direct flat types access with draft overlays
 */
export const PayStubRow: React.FC<PayStubRowProps> = ({
    payStub,
    payStubFragmentRef,
    onEdit,
    onDelete,
    onToggleExpanded,
    timeSheetId,
    ...restProps
}) => {

    // Resolve employee fragments to get actual employee data
    const employeeDisplayData = useFragment(EmployeeDisplayFragment, payStub.employee as EmployeeDisplayFragment_employee$key);
    const employeeDetailData = useFragment(TimeSheetDetailRow_employeeFragment, payStub.employee as TimeSheetDetailRow_employee$key);

    // Use flat types hook for direct data access with draft overlays
    const flatPayStubWithDrafts = useFlatPayStub(payStub, timeSheetId);

    // Pre-index detail drafts for efficient lookups (Phase 5 optimization)
    const draftsByDetailId = useMemo(() => {
        if (!flatPayStubWithDrafts?.drafts.details) return {};
        return indexDraftsByDetailId(flatPayStubWithDrafts.drafts.details);
    }, [flatPayStubWithDrafts?.drafts.details]);

    // Create enhanced flat data structure for UI consumption
    const enhancedFlatData = useMemo((): EnhancedFlatPayStub | null => {
        if (!flatPayStubWithDrafts) {
            // Return null for invalid data - will be handled below
            return null;
        }

        // Calculate individual totals with draft overlays
        const totalStandardHours = calculateHourTotals(flatPayStubWithDrafts.details, draftsByDetailId, 'stHours');
        const totalOvertimeHours = calculateHourTotals(flatPayStubWithDrafts.details, draftsByDetailId, 'otHours');
        const totalDoubleTimeHours = calculateHourTotals(flatPayStubWithDrafts.details, draftsByDetailId, 'dtHours');
        

        // Create enhanced flat data with employee information
        const enhancedData: EnhancedFlatPayStub = {
            ...flatPayStubWithDrafts,
            // Add employee-specific fields for UI consumption
            employeeName: formatEmployeeName(employeeDisplayData) || 'Loading...',
            employeeFirstName: employeeDisplayData?.firstName || '',
            employeeLastName: employeeDisplayData?.lastName || '',
            employeeExternalId: employeeDisplayData?.externalEmployeeId || String(flatPayStubWithDrafts.employeeId || ''),
            employeeActive: employeeDisplayData?.active ?? true,
            
            // Pre-computed aggregates from details with draft overlays
            totalStandardHours,
            totalOvertimeHours,
            totalDoubleTimeHours
        };

        return enhancedData;
    }, [flatPayStubWithDrafts, employeeDisplayData, draftsByDetailId]);

    // Handle null merged data gracefully (malformed fragment data)
    if (!enhancedFlatData) {
        return (
            <tr>
                <td colSpan={10}>Invalid PayStub data</td>
            </tr>
        );
    }

    // Create event handlers that work with flat types
    const handleEdit = () => {
        onEdit?.(enhancedFlatData);
    };

    const handleDelete = () => {
        onDelete?.(enhancedFlatData);
    };

    const handleToggleExpanded = () => {
        onToggleExpanded?.(enhancedFlatData);
    };

    // Convert flat types to domain model format for PayStubUI compatibility
    const domainModelPayStub = useMemo(() => {
        if (!enhancedFlatData) return null;
        
        // Convert flat details to domain model details
        const domainDetails = enhancedFlatData.details?.map(detail => {
            // Check detail-level deletion state from drafts, not PayStub-level deletion
            const detailDraft = draftsByDetailId[detail.id];
            const isDetailDeleted = detailDraft?._uiDelete === true;
            
            return {
                id: detail.id,
                payStubId: detail.payStubId,
                reportLineItemId: detail.reportLineItemId || undefined,
                workDate: detail.workDate,
                name: detail.name || undefined,
                dayName: detail.name || '',
                hours: {
                    standard: detail.stHours || 0,
                    overtime: detail.otHours || 0,
                    doubletime: detail.dtHours || 0,
                    total: (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0)
                },
                job: {
                    jobCode: detail.jobCode || '',
                    costCenter: detail.costCenter || '',
                    hourlyRate: detail.hourlyRate || 0
                },
                earnings: {
                    earningsCode: detail.earningsCode || ''
                },
                agreements: {
                    agreementId: detail.agreementId || 0,
                    classificationId: detail.classificationId || 0,
                    subClassificationId: detail.subClassificationId || undefined
                },
                amounts: {
                    bonus: detail.bonus || 0,
                    expenses: detail.expenses || 0
                },
                employeeId: enhancedFlatData.employeeId.toString(),
                ui: {
                    isEditing: false,
                    hasErrors: false,
                    isSelected: false,
                    // Use detail-specific deletion state, not PayStub-level deletion
                    isTemporary: isDetailDeleted,
                    validationErrors: []
                }
            };
        }) || [];

        return {
            id: enhancedFlatData.id,
            employeeId: enhancedFlatData.employeeId.toString(),
            employeeName: enhancedFlatData.employeeName,
            name: enhancedFlatData.name || '',
            hours: {
                standard: enhancedFlatData.totalStandardHours,
                overtime: enhancedFlatData.totalOvertimeHours,
                doubletime: enhancedFlatData.totalDoubleTimeHours,
                total: enhancedFlatData.totalStandardHours + enhancedFlatData.totalOvertimeHours + enhancedFlatData.totalDoubleTimeHours
            },
            amounts: {
                bonus: domainDetails.reduce((sum, detail) => sum + (detail.amounts?.bonus || 0), 0),
                expenses: domainDetails.reduce((sum, detail) => sum + (detail.amounts?.expenses || 0), 0)
            },
            details: domainDetails,
            employee: {
                id: enhancedFlatData.employeeId.toString(),
                firstName: enhancedFlatData.employeeFirstName,
                lastName: enhancedFlatData.employeeLastName,
                fullName: enhancedFlatData.employeeName,
                externalEmployeeId: enhancedFlatData.employeeExternalId,
                active: enhancedFlatData.employeeActive
            },
            ui: {
                isEditing: false,
                hasErrors: false,
                isSelected: false,
                isTemporary: enhancedFlatData.isMarkedForDeletion,
                expanded: false
            }
        };
    }, [enhancedFlatData, draftsByDetailId]);

    if (!domainModelPayStub) {
        return (
            <tr>
                <td colSpan={10}>Invalid PayStub data</td>
            </tr>
        );
    }

    // Use the full PayStubUI component with converted domain model
    return (
        <PayStubUI
            {...restProps}
            payStub={domainModelPayStub}
            payStubFragmentRef={payStubFragmentRef}
            timeSheetId={timeSheetId}
            onEdit={(payStub) => onEdit?.(enhancedFlatData)}
            onDelete={(payStub) => onDelete?.(enhancedFlatData)}
            onToggleExpanded={(payStub) => onToggleExpanded?.(enhancedFlatData)}
        />
    );
};

export default PayStubRow;
