import React, { useMemo, useState, useCallback } from 'react';
import { useFragment, graphql } from 'react-relay';
import { Flex, View, Text, ProgressCircle } from '@adobe/react-spectrum';
import TimesheetToolbar from './TimesheetToolbar';
import PayStubTable, { PayStubTable_payStubFragment, PayStubTable_connectionFragment } from './PayStubTable';
// We're now importing the fragment from PayStubTable.tsx instead of redefining it here
import type { TimeSheetGrid_timeSheet$key } from '@/relay/__generated__/TimeSheetGrid_timeSheet.graphql';
// Import settings types
import type { TimeSheetSettingsState } from './TimeSheetSettings';
import type { Employee } from '@/src/types/relay-ui-extensions';
import { useTimesheetContext } from './TimesheetContext';
import '@/src/fragments/TimeSheetPayStubsConnectionFragment';

// --- Relay Fragment ---

export const TimeSheetGrid_timeSheetFragment = graphql`
    fragment TimeSheetGrid_timeSheet on TimeSheet {
        payPeriodEndDate
        ...PayStubTable_connectionFragment
        ...TimesheetToolbar_timeSheet # Toolbar still needs its own fragment
    }
`;

// Updated to extract employee data from timesheet payStubs instead of separate query
// This follows Relay best practices by using data already available in the parent query

// --- Component ---

interface TimeSheetGridProps {
    timeSheetRef: TimeSheetGrid_timeSheet$key; // Use the child fragment key type
    queryRef: any; // Query reference to pass down to child components
    status: string | null | undefined;
    readOnly: boolean;
    settings: TimeSheetSettingsState; // Add settings prop
    onSettingsChange: (newSettings: TimeSheetSettingsState) => void; // Add handler prop
    // Employee data extracted from parent timesheet fragment (optional - PayStubs have their own employee data)
    employees?: ReadonlyArray<Employee>;
}

const TimeSheetGrid: React.FC<TimeSheetGridProps> = ({
    timeSheetRef,
    queryRef,
    status,
    readOnly,
    settings, // Destructure props
    onSettingsChange, // Destructure props
    employees = [] // Employee data from parent (optional - PayStubs have their own employee data)
}) => {
    const timeSheetGridData = useFragment(TimeSheetGrid_timeSheetFragment, timeSheetRef);
    
    // Get context values instead of props (eliminates prop drilling)
    const { timesheetId, numericId, employerGuid } = useTimesheetContext();

    // Employee data is now passed from parent component that has resolved the full timesheet fragment
    // This follows Relay best practices by not duplicating fragment resolution

    return (
        <>
            <TimesheetToolbar
                readOnly={readOnly}
                timeSheetHeaderRef={timeSheetGridData}
                queryRef={queryRef}
                status={status}
                settings={settings} // Pass settings down
                onSettingsChange={onSettingsChange} // Pass handler down
            />
            <PayStubTable
                timeSheetRef={timeSheetGridData}
                readOnly={readOnly}
                // Pass individual settings flags to PayStubTable based on settings state
                showBonusColumn={settings.showBonusColumn ?? false}
                showCostCenterColumn={settings.showCostCenterColumn ?? false}
                showDTHoursColumn={settings.showDTHoursColumn ?? false}
                showEarningsCodesColumn={settings.showEarningsCodesColumn ?? false}
                showExpensesColumn={settings.showExpensesColumn ?? false}
                employees={employees}
                payPeriodEndDate={timeSheetGridData.payPeriodEndDate}
                timeSheetId={timesheetId}
                numericId={numericId}
                employerGuid={employerGuid}
            />
        </>
    );
};

export default TimeSheetGrid;
