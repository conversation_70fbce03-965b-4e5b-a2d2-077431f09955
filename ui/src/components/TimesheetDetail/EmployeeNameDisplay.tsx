import React from 'react';
import { useFragment } from 'react-relay';
import { EmployeeDisplayFragment } from '@/src/fragments/EmployeeDisplayFragment';
import type { EmployeeDisplayFragment_employee$key } from '@/relay/__generated__/EmployeeDisplayFragment_employee.graphql';

/**
 * Component for displaying employee names with proper error handling
 * Follows Relay best practices for handling potentially missing data
 * 
 * This component implements Phase 4 of the employee cache optimization:
 * - Resolves employee data from link-only references
 * - Handles loading and error states gracefully
 * - Provides consistent employee name formatting
 */
interface EmployeeNameDisplayProps {
    employeeRef: EmployeeDisplayFragment_employee$key | null;
    className?: string;
    fallbackText?: string;
}

const EmployeeNameDisplay: React.FC<EmployeeNameDisplayProps> = ({
    employeeRef,
    className,
    fallbackText = 'Loading...'
}) => {
    // Call useFragment unconditionally (RELAY-RULES.md Rule 4)
    const employeeData = useFragment(EmployeeDisplayFragment, employeeRef);

    if (!employeeData) {
        return <span className={`employee-loading ${className || ''}`}>{fallbackText}</span>;
    }

    const displayName = `${employeeData.firstName || ''} ${employeeData.lastName || ''}`.trim();
    const statusClass = employeeData.active ? 'employee-active' : 'employee-inactive';

    return (
        <span className={`employee-name ${statusClass} ${className || ''}`}>
            {displayName || 'Unknown Employee'}
        </span>
    );
};

export default EmployeeNameDisplay;