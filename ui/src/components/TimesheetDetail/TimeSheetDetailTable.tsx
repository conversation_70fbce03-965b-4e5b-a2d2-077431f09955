import React, { useRef } from 'react';
import { View, Text } from '@adobe/react-spectrum';
import styles from './TimeSheetDetailTableView.module.scss';
import { useKeyboardNavigation } from '@/src/hooks/useKeyboardNavigation';
import type { DetailDisplayData } from '@/src/utils/timesheetDetailDataTransform';
import type { PayStubDetailDraftKeys } from '@/src/types';
import TimeSheetDetailRow from './TimeSheetDetailRow';
import type { TimeSheetDetailRow_employee$key } from '@/relay/__generated__/TimeSheetDetailRow_employee.graphql';

/**
 * Table column definition
 */
export interface TableColumn {
    name?: string;
    uid: string;
    width?: number;
}

/**
 * Column sets for different data display types and editing capabilities
 */
export const numericColumnUids = new Set(['hourlyRate', 'stHours', 'otHours', 'dtHours', 'totalHours', 'bonus', 'expenses']);
export const editableColumnUids = new Set([
    'jobCode',
    'costCenter',
    'stHours',
    'otHours',
    'dtHours',
    'hourlyRate',
    'bonus',
    'expenses',
    'agreementId',
    'classificationId',
    'subClassificationId',
    'earningsCode'
]);

interface TimeSheetDetailTableProps {
    displayDetails: DetailDisplayData[];
    columns: ReadonlyArray<TableColumn>;
    payStubEmployeeRef: TimeSheetDetailRow_employee$key | null;
    payStubIndex?: number;
    payStubId?: string;
    timesheetId?: string;
    readOnly: boolean;
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    handleRowDeleteToggle: (detailId: string) => void;
    employerGuid: string; // Add employerGuid prop
    handleRowDuplicate: (detailIndex: number) => void;
    isCellEditable: (row: number, col: number) => boolean;
    temporaryPayStubDetailEdits: Map<string, Record<string, unknown>>;
    updateTemporaryPayStubDetailEdit: (detailId: string, field: PayStubDetailDraftKeys, value: unknown) => void;
    applyEmployeeDefaultsOnce: (detailId: string, employeeDefaults: any) => void;
}

export const TimeSheetDetailTable: React.FC<TimeSheetDetailTableProps> = ({
    displayDetails,
    columns,
    payStubEmployeeRef,
    payStubIndex,
    payStubId,
    timesheetId,
    readOnly,
    showBonusColumn,
    showCostCenterColumn,
    showDTHoursColumn,
    showEarningsCodesColumn,
    showExpensesColumn,
    formatNumber,
    handleRowDeleteToggle,
    handleRowDuplicate,
    isCellEditable,
    temporaryPayStubDetailEdits,
    employerGuid,
    updateTemporaryPayStubDetailEdit,
    applyEmployeeDefaultsOnce
}) => {
    const tableRef = useRef<HTMLTableElement>(null);
    
    const { activeCell, editingCell, setActiveCell, setEditingCell, handleKeyDown } = useKeyboardNavigation({
        totalRows: displayDetails.length,
        totalColumns: columns.length,
        isEditable: isCellEditable
    });

    return (
        <table 
            ref={tableRef} 
            tabIndex={0} 
            onKeyDown={handleKeyDown} 
            className={styles.detailTable} 
            aria-label="Timesheet Detail Grid"
            role="grid"
            aria-rowcount={displayDetails.length + 1} // +1 for header
            aria-colcount={columns.length}
        >
            <thead className={styles.tableHead}>
                <tr role="row">
                    {columns.map((column, index) => (
                        <th
                            key={column.uid}
                            role="columnheader"
                            aria-colindex={index + 1}
                            aria-sort="none"
                            style={{
                                width: column.width ? `${column.width}px` : undefined,
                                textAlign: numericColumnUids.has(column.uid) ? 'right' : column.uid === 'name' ? 'center' : 'left'
                            }}
                        >
                            <Text>{column.name}</Text>
                        </th>
                    ))}
                </tr>
            </thead>
            <tbody className={styles.tableBody}>
                {displayDetails && displayDetails.length > 0 ? (
                    displayDetails.map((detail, index) => {
                        const isActive = activeCell?.[0] === index;
                        const rowEditingCell = editingCell?.[0] === index ? [editingCell[0], editingCell[1]] as [number, number] : null;
                        
                        return (
                            <TimeSheetDetailRow
                                key={detail?.id ?? index}
                                detail={detail}
                                employeeRef={payStubEmployeeRef}
                                index={index}
                                payStubIndex={payStubIndex}
                                payStubId={payStubId || detail?.payStubId}
                                timesheetId={timesheetId}
                                columns={columns}
                                temporaryPayStubDetailEdits={temporaryPayStubDetailEdits}
                                updateTemporaryPayStubDetailEdit={updateTemporaryPayStubDetailEdit}
                                readOnly={readOnly}
                                handleRowDeleteToggle={handleRowDeleteToggle}
                                handleRowDuplicate={handleRowDuplicate}
                                activeCell={activeCell}
                                editingCell={editingCell}
                                setActiveCell={setActiveCell}
                                setEditingCell={setEditingCell}
                                tableRef={tableRef}
                                showBonusColumn={showBonusColumn}
                                showCostCenterColumn={showCostCenterColumn}
                                showDTHoursColumn={showDTHoursColumn}
                                showEarningsCodesColumn={showEarningsCodesColumn}
                                showExpensesColumn={showExpensesColumn}
                                formatNumber={formatNumber}
                                employerGuid={employerGuid}
                                applyEmployeeDefaultsOnce={applyEmployeeDefaultsOnce}
                            />
                        );
                    })
                ) : (
                    <tr className={styles.emptyRow} role="row">
                        <td colSpan={columns.length} role="gridcell">
                            <Text>No details available for this pay stub.</Text>
                        </td>
                    </tr>
                )}
            </tbody>
        </table>
    );
};