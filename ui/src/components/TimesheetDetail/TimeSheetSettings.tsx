import { graphql, useFragment } from 'react-relay';
import {
    <PERSON>ton,
    DialogTrigger,
    Dialog,
    Heading,
    Divider,
    Content,
    CheckboxGroup,
    Checkbox,
    ButtonGroup,
    Text
} from '@adobe/react-spectrum';
import Settings from '@spectrum-icons/workflow/Settings';
import type { TimeSheetSettings_timeSheet$key } from '@/relay/__generated__/TimeSheetSettings_timeSheet.graphql';

// Define the GraphQL Fragment
export const TimeSheetSettingsFragment = graphql`
    fragment TimeSheetSettings_timeSheet on TimeSheet {
        id
        # No longer need numericId or employerGuid here if mutation is removed
        # numericId
        # employerGuid
        showBonusColumn
        showCostCenterColumn
        showDTHoursColumn
        showEarningsCodesColumn
        showExpensesColumn
        # Add other fields needed for display if necessary
    }
`;

// Define a type for the settings object
export interface TimeSheetSettingsState {
    showBonusColumn: boolean | null;
    showCostCenterColumn: boolean | null;
    showDTHoursColumn: boolean | null;
    showEarningsCodesColumn: boolean | null;
    showExpensesColumn: boolean | null;
}

// Define props interface using the generated fragment type and add new props
interface TimeSheetSettingsProps {
    timeSheetRef: TimeSheetSettings_timeSheet$key; // Use the generated fragment key type
    settings: TimeSheetSettingsState; // Current settings state from parent
    onSettingsChange: (newSettings: TimeSheetSettingsState) => void; // Handler to notify parent
}

const TimeSheetSettings: React.FC<TimeSheetSettingsProps> = ({ timeSheetRef, settings, onSettingsChange }) => {
    // Use the fragment just to potentially read initial values if needed, but primary source is props
    const timeSheetFragmentData = useFragment(TimeSheetSettingsFragment, timeSheetRef);

    // Handler for Spectrum Checkbox group (value is an array of selected string values)
    const handleSettingsChange = (selectedValues: string[]) => {
        const newSettings: TimeSheetSettingsState = {
            showDTHoursColumn: selectedValues.includes('showDTHoursColumn'),
            showEarningsCodesColumn: selectedValues.includes('showEarningsCodesColumn'),
            showCostCenterColumn: selectedValues.includes('showCostCenterColumn'),
            showBonusColumn: selectedValues.includes('showBonusColumn'), // Corrected value mapping if needed
            showExpensesColumn: selectedValues.includes('showExpensesColumn')
        };

        // Call the parent handler instead of committing mutation
        onSettingsChange(newSettings);
    };

    // Derive selected string values from the settings prop
    const selectedValues = Object.entries(settings)
        .filter(([key, value]) => key.startsWith('show') && value === true)
        .map(([key]) => {
            // Map internal state names back to checkbox values if they differ
            // Assuming they map directly for now, adjust if necessary
            if (key === 'showDTHoursColumn') return 'showDTHoursColumn';
            // Add other mappings if checkbox values differ from state keys
            return key;
        });

    // Determine if any mutation is in flight (this needs to come from parent now)
    // We'll assume no background mutation affecting this specific UI for now,
    // but a parent component might pass down an `isDisabled` prop if needed.
    const isMutationInFlight = false; // Placeholder - parent should control this if needed

    return (
        <DialogTrigger type="popover">
            {/* Consider passing isDisabled from parent if save actions disable interaction */}
            <Button variant="secondary" /* isDisabled={isMutationInFlight} */>
                <Settings />
                <Text>Settings</Text>
            </Button>
            {(close) => (
                <Dialog>
                    <Heading>Timesheet Settings</Heading>
                    <Divider />
                    <Content>
                        <CheckboxGroup
                            label="Optional Columns"
                            value={selectedValues}
                            onChange={handleSettingsChange}
                            /* isDisabled={isMutationInFlight} */
                        >
                            <Checkbox value="showDTHoursColumn">Show DT Hours Column</Checkbox>
                            <Checkbox value="showCostCenterColumn">Show Cost Center Column</Checkbox>
                            {/* Assuming 'showBonusColumn' corresponds to 'Direct Pay Column' */}
                            <Checkbox value="showBonusColumn">Show Direct Pay Column</Checkbox>
                            <Checkbox value="showExpensesColumn">Show Expenses Column</Checkbox>
                            <Checkbox value="showEarningsCodesColumn">Show Earning Codes Column</Checkbox>
                        </CheckboxGroup>
                    </Content>
                    <ButtonGroup>
                        <Button variant="primary" onPress={close}>
                            <Text>Done</Text>
                        </Button>
                    </ButtonGroup>
                </Dialog>
            )}
        </DialogTrigger>
    );
};

export default TimeSheetSettings;
