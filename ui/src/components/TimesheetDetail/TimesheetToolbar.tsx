import React, { useCallback, useMemo } from 'react';
import { Button, Flex, View, Text } from '@adobe/react-spectrum';
import { Toolbar } from 'react-aria-components';
import Add from '@spectrum-icons/workflow/Add';
import UserAdd from '@spectrum-icons/workflow/UserAdd';
import Contract from '@spectrum-icons/workflow/TreeCollapseAll';
import UploadTimeSheetWithEmployeeData from './UploadTimeSheet/UploadTimeSheetWithEmployeeData';
import TimeSheetSettings from './TimeSheetSettings';
import { graphql, useFragment } from 'react-relay';
import { TimesheetToolbar_timeSheetFragment } from './TimesheetToolbar.fragments';
import type { TimesheetToolbar_timeSheet$key } from '@/relay/__generated__/TimesheetToolbar_timeSheet.graphql';
import type { TimeSheetSettingsState } from './TimeSheetSettings';
import TimeSheetReset from './TimeSheetReset';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { useRelayEnvironment } from 'react-relay';
import { useTimesheetContext } from './TimesheetContext';

interface TimesheetToolbarProps {
    readOnly: boolean;
    timeSheetHeaderRef: TimesheetToolbar_timeSheet$key | null | undefined;
    queryRef: any; // Query reference to pass down to child components
    status: string | null | undefined;
    settings: TimeSheetSettingsState;
    onSettingsChange: (newSettings: TimeSheetSettingsState) => void;
}

const TimesheetToolbar: React.FC<TimesheetToolbarProps> = ({
    readOnly,
    timeSheetHeaderRef,
    queryRef,
    status,
    settings,
    onSettingsChange
}) => {
    // Get context values instead of props (eliminates prop drilling)
    const { timesheetId, numericId, employerGuid } = useTimesheetContext();
    
    // Use Zustand store actions with proper scoping
    const collapseAllPayStubs = useTimesheetUIStore(state => state.collapseAllPayStubs);
    const showEmployeeSelector = useTimesheetUIStore(state => state.showEmployeeSelector);
    const environment = useRelayEnvironment();

    // eslint-disable-next-line custom/relay-fragment-dependencies
    const toolbarData = useFragment(TimesheetToolbar_timeSheetFragment, timeSheetHeaderRef);

    const handleAddEmployee = useCallback(() => {
        // Use new employee selector approach with immediate PayStub creation
        if (!timesheetId) {
            console.error('TimesheetToolbar: Cannot add employee - timesheetId is missing');
            return;
        }

        // Additional validation for string format
        if (typeof timesheetId !== 'string' || timesheetId.trim() === '') {
            console.error('TimesheetToolbar: Invalid timesheetId format:', timesheetId);
            return;
        }

        // Check for common invalid values
        if (timesheetId === 'null' || timesheetId === 'undefined') {
            console.error('TimesheetToolbar: timesheetId has invalid string value:', timesheetId);
            return;
        }

        showEmployeeSelector(timesheetId);
    }, [showEmployeeSelector, timesheetId]);


    return (
        <Toolbar aria-label="Timesheet Actions">
            <Flex gap="size-200" alignItems="end" wrap="wrap">
                {!readOnly && (
                    <Button variant="primary" onPress={handleAddEmployee}>
                        <UserAdd />
                        <Text>Add Employee</Text>
                    </Button>
                )}
                <View flexGrow={1} />
                {toolbarData && employerGuid && (
                    <UploadTimeSheetWithEmployeeData
                        employerGuid={employerGuid}
                        timeSheetRef={toolbarData}
                        queryRef={queryRef}
                        readOnly={readOnly}
                        onClose={() => {}} // No-op since this is just the button, not the dialog
                    />
                )}
                {toolbarData && (
                    <TimeSheetReset
                        readOnly={readOnly}
                        timeSheetId={toolbarData.id}
                        payStubs={(toolbarData.payStubs?.edges?.map((edge) => edge.node).filter(Boolean) || []).map((ps) => ({
                            id: ps.id,
                            details: ps.details.map((d) => ({ id: d.id }))
                        }))}
                    />
                )}
                {toolbarData && settings && (
                    <TimeSheetSettings timeSheetRef={toolbarData} settings={settings} onSettingsChange={onSettingsChange} />
                )}
                <Button variant="secondary" onPress={() => timesheetId && collapseAllPayStubs(timesheetId)}>
                    <Contract />
                    <Text>Collapse All Rows</Text>
                </Button>
            </Flex>
        </Toolbar>
    );
};

export default TimesheetToolbar;
