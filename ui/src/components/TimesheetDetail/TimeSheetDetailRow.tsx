// src/components/TimesheetDetail/TimeSheetDetailRow.tsx
import React, { useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { graphql, useFragment } from 'react-relay';
import { View, Text, Flex, ActionButton, TooltipTrigger, Tooltip } from '@adobe/react-spectrum';
import Close from '@spectrum-icons/workflow/Close';
import CopyIcon from '@spectrum-icons/workflow/Copy';
import UndoIcon from '@spectrum-icons/workflow/Undo';
import NumericCellContent from '@ui/NumericCellContent';
import styles from './TimeSheetDetailTableView.module.scss';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
// Import validation utilities
import type { ValidationError } from '@/src/utils/validationUtils';
import { validateDetailField } from '@/src/utils/validationUtils';
import type { PayStubDetailDraftKeys } from '@/src/types';

// Import helper functions from utils
import { isRelayDetailRef, parseWorkDate, formatDayName, formatWorkDate } from './TimeSheetDetailRow.utils';
import { getFieldDisplayName } from '@/src/utils/fieldDisplayUtils';
import { safePayStubDetailId, safePayStubIdLenient } from '@/src/utils/safeIdConversion';

// Import Cell Components
import { ReadOnlyCell } from './Cells/ReadOnlyCell';
import { EditableNumberCell } from './Cells/EditableNumberCell';
import { EditableTextFieldCell } from './Cells/EditableTextFieldCell';
import { AgreementComboBoxCell } from './Cells/AgreementComboBoxCell';
import { ClassificationComboBoxCell } from './Cells/ClassificationComboBoxCell';
import { SubClassificationComboBoxCell } from './Cells/SubClassificationComboBoxCell';
import { EarningsCodeComboBoxCell } from './Cells/EarningsCodeComboBoxCell';

// Import types/constants shared with the parent table
import { TableColumn, numericColumnUids, editableColumnUids } from './TimeSheetDetailTable';
// Relay Types
import { TimeSheetDetailRow_payStubDetail$key } from '@/relay/__generated__/TimeSheetDetailRow_payStubDetail.graphql';
import type {
    TimeSheetDetailRow_employee$key,
    TimeSheetDetailRow_employee$data
} from '@/relay/__generated__/TimeSheetDetailRow_employee.graphql';
// Import Relay hooks for employee defaults
import { singleEmployeeDefaultSettingsQuery, type EmployeeDefaultSettingsData } from '@/src/hooks/useEmployeeDefaultSettingsQueries';
import type {
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery,
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data
} from '@/src/types/graphql-employee';
import { useLazyLoadQuery } from 'react-relay';

// Import hooks for agreement and classification data lookup
import { useAgreementsByEmployer } from '@/src/hooks/useAgreementsByEmployer';
import { useBatchComboBoxQueries } from '@/src/hooks/useBatchComboBoxQueries';

/**
 * GraphQL fragment defining the data needed for a timesheet detail row
 */
export const TimeSheetDetailRow_payStubDetailFragment = graphql`
    fragment TimeSheetDetailRow_payStubDetail on PayStubDetail {
        id
        payStubId
        workDate
        jobCode
        agreementId
        classificationId
        subClassificationId
        hourlyRate
        stHours
        otHours
        dtHours
        bonus
        expenses
        earningsCode
        costCenter
    }
`;

/**
 * GraphQL fragment for employee data needed in TimeSheetDetailRow
 * Following RELAY_RULES.md #2: Fragment naming convention <FileName>_<fragmentName>
 */
export const TimeSheetDetailRow_employeeFragment = graphql`
    fragment TimeSheetDetailRow_employee on Employee {
        id
        externalEmployeeId
        firstName
        lastName
        active
    }
`;

/**
 * Updated Props Interface with proper TypeScript typing
 * Following REACT_RULES.md #4: Props must be clearly typed
 * Following RELAY_PITFALLS.md #4: Use fragment reference types, not resolved data
 */
interface TimeSheetDetailRowProps {
    detail: TimeSheetDetailRow_payStubDetail$key | Record<string, unknown>;
    employeeRef: TimeSheetDetailRow_employee$key | null;
    index: number;
    payStubIndex?: number; // For test IDs
    payStubId?: string; // For validation context
    timesheetId?: string; // For validation context with Zustand store
    columns: ReadonlyArray<TableColumn>;
    temporaryPayStubDetailEdits: Map<string, Record<string, unknown>>;
    updateTemporaryPayStubDetailEdit: (detailId: string, field: PayStubDetailDraftKeys, value: unknown) => void;
    readOnly: boolean;
    handleRowDeleteToggle: (detailId: string) => void;
    handleRowDuplicate: (detailIndex: number) => void;
    // Active/Editing state + setters
    activeCell: [number, number] | null;
    editingCell: [number, number] | null;
    setActiveCell: React.Dispatch<React.SetStateAction<[number, number] | null>>;
    setEditingCell: React.Dispatch<React.SetStateAction<[number, number] | null>>;
    tableRef: React.RefObject<HTMLTableElement | null>;
    // Column visibility flags
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    employerGuid: string; // Add employerGuid prop
    applyEmployeeDefaultsOnce: (detailId: string, employeeDefaults: any) => void;
}

/**
 * Format a date for display in the day column (e.g., 'Mon')
 */

/**
 * Format a date for display in the work date column (e.g., '01/15/2023')
 */

const TimeSheetDetailRow: React.FC<TimeSheetDetailRowProps> = ({
    detail,
    employeeRef,
    index,
    payStubIndex,
    payStubId,
    timesheetId,
    columns,
    temporaryPayStubDetailEdits,
    updateTemporaryPayStubDetailEdit,
    readOnly,
    handleRowDeleteToggle,
    handleRowDuplicate,
    activeCell,
    editingCell,
    setActiveCell,
    setEditingCell,
    tableRef,
    showBonusColumn,
    showCostCenterColumn,
    showDTHoursColumn,
    showEarningsCodesColumn,
    showExpensesColumn,
    formatNumber,
    employerGuid,
    applyEmployeeDefaultsOnce
}) => {
    // Get validation errors from Zustand store if timesheetId is available
    const getValidationErrors = useTimesheetUIStore((state) => state.getValidationErrors);
    const setValidationErrors = useTimesheetUIStore((state) => state.setValidationErrors);
    const validationErrorsByPayStubId = useMemo(() => {
        if (!timesheetId || !payStubId) return new Map();
        const errors = getValidationErrors(timesheetId, payStubId);
        return new Map([[payStubId, errors]]);
    }, [getValidationErrors, timesheetId, payStubId]);

    // Fragment data resolution for validation context
    const fragmentData = useFragment(TimeSheetDetailRow_payStubDetailFragment, isRelayDetailRef(detail) ? detail : null);

    // Use proper typing instead of 'any' - ensure type safety
    const detailData = fragmentData || (detail as Record<string, unknown>);
    
    // ✅ PHASE 2.2: Replace silent casting with explicit error handling
    // OLD: const detailId = String(detailData.id || '');
    const detailId = safePayStubDetailId(detailData);

    // Use final payStubId for validation context
    // ✅ PHASE 2.2: Replace silent casting with explicit error handling  
    // OLD: const finalPayStubId = payStubId || String(detailData?.payStubId || '');
    const finalPayStubId = payStubId || safePayStubIdLenient(detailData);

    // Remove old service functions - employee defaults are handled through Relay queries

    // ✅ CORRECT: Unconditional useFragment call (RELAY_RULES.md #4)
    const employee = useFragment(TimeSheetDetailRow_employeeFragment, employeeRef);

    // ✅ CORRECT: Call all hooks unconditionally
    const employeeDefaultData = useLazyLoadQuery<useEmployeeDefaultSettingsQueriesSingleEmployeeQuery>(
        singleEmployeeDefaultSettingsQuery,
        { employeeId: employee?.id || '' },
        {
            fetchPolicy: employee?.id ? 'store-or-network' : 'store-only'
        }
    );

    const employeeDefaults: useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data['employeeDefaultSettings'] | undefined = employee?.id
        ? employeeDefaultData?.employeeDefaultSettings
        : undefined;

    // Use employee defaults directly from Relay query
    const finalEmployeeDefaults = employeeDefaults;

    // Employee defaults are now managed directly through Relay - no separate cache needed

    // Fetch agreement and classification data for tooltip name lookup
    const { agreements } = useAgreementsByEmployer({
        employerGuid,
        includeInactiveAgreements: false
    });

    // Get default agreement ID to fetch classifications for default tooltip
    const defaultAgreementId = finalEmployeeDefaults?.defaultAgreementId;
    const { classifications } = useBatchComboBoxQueries({
        shouldExecute: !!defaultAgreementId,
        agreementId: defaultAgreementId ? String(defaultAgreementId) : null,
        classificationId: null // We don't need sub-classifications for tooltip
    });

    // Helper functions to look up names by IDs for tooltips
    const getAgreementNameById = useCallback(
        (agreementId: string | number | null | undefined): string | null => {
            if (!agreementId || !agreements.length) return null;
            const agreement = agreements.find((a) => String(a.value) === String(agreementId));
            return agreement ? agreement.text : null;
        },
        [agreements]
    );

    const getClassificationNameById = useCallback(
        (classificationId: string | number | null | undefined): string | null => {
            if (!classificationId || !classifications.length) return null;
            const classification = classifications.find((c) => String(c.value) === String(classificationId));
            return classification ? classification.text : null;
        },
        [classifications]
    );

    const [hasUserInteractedWithAgreement, setHasUserInteractedWithAgreement] = useState(false);
    const [hasUserInteractedWithClassification, setHasUserInteractedWithClassification] = useState(false);
    const [hasUserInteractedWithHourlyRate, setHasUserInteractedWithHourlyRate] = useState(false);

    const mergedData = useMemo(() => {
        const edits = temporaryPayStubDetailEdits.get(detailId) || {};
        return {
            ...detailData,
            ...edits
        } as typeof detailData & Record<string, unknown>;
    }, [detailData, temporaryPayStubDetailEdits, detailId]);

    // CRITICAL FIX: Use _uiDelete field name to match the store's field name
    // The store uses _uiDelete for detail-level deletion state, not 'delete'
    const isDeleted = mergedData._uiDelete ?? mergedData.delete ?? false;

    // Explicitly reference bonus and expenses to satisfy Relay linter
    // These fields are used dynamically through mergedData[field] in the cell rendering logic
    const _bonusField = mergedData.bonus;
    const _expensesField = mergedData.expenses;

    // Phase 5: Use singleton debouncer from store instead of creating one per row
    const getValidationDebouncer = useTimesheetUIStore((state) => state.getValidationDebouncer);

    // Get singleton debounced validation function for this timesheet
    const debouncedValidation = useMemo(() => {
        if (!timesheetId) return null;

        // Create validation handler
        const validationHandler = (payload: any) => {
            const { field, newValue, updatedData, payStubId, timesheetId, detailId, employeeName, callback } = payload;

            // Perform validation using the imported validateDetailField
            const errors = validateDetailField(field, newValue, updatedData, payStubId, timesheetId, detailId, employeeName);

            // Call the callback with validation results
            callback(errors);
        };

        return getValidationDebouncer(timesheetId, validationHandler);
    }, [timesheetId, getValidationDebouncer]);

    // Helper function to perform real-time validation and update the store
    const performFieldValidation = useCallback(
        (field: string, newValue: unknown, updatedData: Record<string, unknown>) => {
            if (!timesheetId || !finalPayStubId || !debouncedValidation) return;

            const employeeName = employee ? `${employee.lastName || ''}, ${employee.firstName || ''}`.trim() : undefined;

            // Use singleton debounced validation with payload object
            debouncedValidation({
                field,
                newValue,
                updatedData,
                payStubId: finalPayStubId,
                timesheetId,
                detailId,
                employeeName,
                callback: (errors: ValidationError[]) => {
                    if (errors.length === 0) {
                        // Clear validation errors for this specific field when the field is valid
                        const store = useTimesheetUIStore.getState();
                        store.clearValidationErrorsForField(timesheetId, finalPayStubId, detailId, field);
                    } else {
                        // Get existing errors and merge with new ones, replacing errors for this field
                        const store = useTimesheetUIStore.getState();
                        const existingErrors = store.getValidationErrors(timesheetId, finalPayStubId);

                        // Filter out old errors for this field
                        const otherErrors = existingErrors.filter((err) => !(err.detailId === detailId && err.columnUid === field));

                        // Combine with new errors
                        const mergedErrors = [...otherErrors, ...errors];
                        setValidationErrors(timesheetId, finalPayStubId, mergedErrors);
                    }
                }
            });
        },
        [timesheetId, finalPayStubId, detailId, employee, setValidationErrors, debouncedValidation]
    );

    // Phase 5: Add recomputeRowValidation for cross-field error clearing
    const recomputeRowValidation = useCallback(() => {
        if (!timesheetId || !finalPayStubId) return;

        // List of fields that have cross-field dependencies
        const fieldsToRevalidate = ['stHours', 'otHours', 'dtHours', 'agreementId', 'classificationId'];

        const employeeName = employee ? `${employee.lastName || ''}, ${employee.firstName || ''}`.trim() : undefined;
        const store = useTimesheetUIStore.getState();
        const existingErrors = store.getValidationErrors(timesheetId, finalPayStubId);

        // Filter out errors for this row's cross-field validations
        const otherErrors = existingErrors.filter(
            (err) => !(err.detailId === detailId && fieldsToRevalidate.includes(err.columnUid || ''))
        );

        // Revalidate all cross-field dependent fields
        let newErrors: ValidationError[] = [];
        fieldsToRevalidate.forEach((field) => {
            const fieldErrors = validateDetailField(
                field,
                mergedData[field],
                mergedData,
                finalPayStubId,
                timesheetId,
                detailId,
                employeeName
            );
            newErrors = [...newErrors, ...fieldErrors];
        });

        // Update store with recomputed errors
        const mergedErrors = [...otherErrors, ...newErrors];
        setValidationErrors(timesheetId, finalPayStubId, mergedErrors);
    }, [timesheetId, finalPayStubId, detailId, employee, mergedData, setValidationErrors]);

    // Memoized onChange handlers to prevent inline function creation (moved after mergedData declaration)
    const handleNumberFieldChange = useCallback(
        (field: string, newValue: number | null | undefined) => {
            if (field === 'hourlyRate') {
                setHasUserInteractedWithHourlyRate(true);
            }
            // Safe cast: we know the calling code only passes valid PayStubDetailDraftKeys
            updateTemporaryPayStubDetailEdit(detailId, field as PayStubDetailDraftKeys, newValue);

            // Real-time validation
            const updatedData = { ...mergedData, [field]: newValue };
            performFieldValidation(field, newValue, updatedData);

            // Phase 5: Recompute cross-field validations when hours fields change
            if (['stHours', 'otHours', 'dtHours'].includes(field)) {
                // Use setTimeout to ensure field validation completes first
                setTimeout(() => recomputeRowValidation(), 0);
            }
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation, recomputeRowValidation]
    );

    const handleJobCodeChange = useCallback(
        (newValue: string | null) => {
            updateTemporaryPayStubDetailEdit(detailId, 'jobCode', newValue);

            // Real-time validation
            const updatedData = { ...mergedData, jobCode: newValue };
            performFieldValidation('jobCode', newValue, updatedData);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation]
    );

    const handleCostCenterChange = useCallback(
        (newValue: string | null) => {
            updateTemporaryPayStubDetailEdit(detailId, 'costCenter', newValue);

            // Real-time validation
            const updatedData = { ...mergedData, costCenter: newValue };
            performFieldValidation('costCenter', newValue, updatedData);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation]
    );

    const handleAgreementChange = useCallback(
        (newValue: string | null) => {
            const numericValue = newValue ? parseInt(newValue, 10) : null;
            setHasUserInteractedWithAgreement(true);
            updateTemporaryPayStubDetailEdit(detailId, 'agreementId', numericValue);
            updateTemporaryPayStubDetailEdit(detailId, 'classificationId', null);
            updateTemporaryPayStubDetailEdit(detailId, 'subClassificationId', null);
            // Classification data fetching now handled by Relay in ComboBox components

            // Real-time validation
            const updatedData = {
                ...mergedData,
                agreementId: numericValue,
                classificationId: null,
                subClassificationId: null
            };
            performFieldValidation('agreementId', numericValue, updatedData);

            // Phase 5: Recompute cross-field validations when agreement changes
            setTimeout(() => recomputeRowValidation(), 0);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation, recomputeRowValidation]
    );

    const handleClassificationChange = useCallback(
        (newValue: string | number | null) => {
            const numericValue = typeof newValue === 'string' ? parseInt(newValue, 10) : newValue;

            setHasUserInteractedWithClassification(true);
            updateTemporaryPayStubDetailEdit(detailId, 'classificationId', numericValue);
            updateTemporaryPayStubDetailEdit(detailId, 'subClassificationId', null);

            // Sub-classification data fetching now handled by Relay in ComboBox components

            // Real-time validation
            const updatedData = { ...mergedData, classificationId: numericValue, subClassificationId: null };
            performFieldValidation('classificationId', numericValue, updatedData);

            // Phase 5: Recompute cross-field validations when classification changes
            setTimeout(() => recomputeRowValidation(), 0);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation, recomputeRowValidation]
    );

    const handleSubClassificationChange = useCallback(
        (newValue: string | number | null) => {
            const numericValue = typeof newValue === 'string' ? parseInt(newValue, 10) : newValue;
            updateTemporaryPayStubDetailEdit(detailId, 'subClassificationId', numericValue);

            // Real-time validation
            const updatedData = { ...mergedData, subClassificationId: numericValue };
            performFieldValidation('subClassificationId', numericValue, updatedData);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation]
    );

    const handleEarningsCodeChange = useCallback(
        (newValue: string | number | null) => {
            const stringValue = typeof newValue === 'number' ? newValue.toString() : newValue;
            updateTemporaryPayStubDetailEdit(detailId, 'earningsCode', stringValue);

            // Real-time validation
            const updatedData = { ...mergedData, earningsCode: stringValue };
            performFieldValidation('earningsCode', stringValue, updatedData);
        },
        [detailId, mergedData, updateTemporaryPayStubDetailEdit, performFieldValidation]
    );

    const transformJobCodeValue = useCallback((value: string | null) => value?.toUpperCase() ?? '', []);

    // Helper function to determine if a field is required
    const isFieldRequired = useCallback((field: string, data: Record<string, unknown>): boolean => {
        // Agreement and classification are required when trigger fields have data
        const hasTriggerData =
            (data.stHours && Number(data.stHours) > 0) ||
            (data.otHours && Number(data.otHours) > 0) ||
            (data.dtHours && Number(data.dtHours) > 0) ||
            (data.jobCode && String(data.jobCode).trim() !== '') ||
            (data.costCenter && String(data.costCenter).trim() !== '') ||
            (data.bonus && Number(data.bonus) > 0) ||
            (data.expenses && Number(data.expenses) > 0);

        if (field === 'agreementId' && hasTriggerData) return true;
        if (field === 'classificationId' && hasTriggerData && data.agreementId !== 0) return true;

        return false;
    }, []);

    // Type-safe field access helpers
    const getStringField = useCallback(
        (fieldName: string): string | null => {
            const value = mergedData[fieldName];
            return typeof value === 'string' ? value : value ? String(value) : null;
        },
        [mergedData]
    );

    const getNumberField = useCallback(
        (fieldName: string): number | null => {
            const value = mergedData[fieldName];
            return typeof value === 'number' ? value : null;
        },
        [mergedData]
    );

    // Create a memoized function factory for number field changes to avoid inline functions
    const createNumberFieldChangeHandler = useCallback(
        (field: string) => {
            return (newValue: number | null | undefined) => handleNumberFieldChange(field, newValue);
        },
        [handleNumberFieldChange]
    );

    const isAgreementDefault =
        !hasUserInteractedWithAgreement &&
        finalEmployeeDefaults?.defaultAgreementId != null &&
        mergedData.agreementId != null &&
        mergedData.agreementId === finalEmployeeDefaults.defaultAgreementId;

    const isClassificationDefault =
        !hasUserInteractedWithClassification &&
        finalEmployeeDefaults?.defaultClassificationId != null &&
        mergedData.classificationId != null &&
        mergedData.classificationId === finalEmployeeDefaults.defaultClassificationId;

    const defaultHourlyRateFromGraphQL = finalEmployeeDefaults?.defaultHourlyRate;
    const currentRowHourlyRate = mergedData.hourlyRate;

    let isHourlyRateDefault = false;
    if (!hasUserInteractedWithHourlyRate && defaultHourlyRateFromGraphQL != null && currentRowHourlyRate != null) {
        const defaultRateAsNumber = parseFloat(defaultHourlyRateFromGraphQL);
        if (!isNaN(defaultRateAsNumber) && currentRowHourlyRate === defaultRateAsNumber) {
            isHourlyRateDefault = true;
        }
    }

    // Remove old REST API fetching useEffects - now handled by Relay in ComboBox components

    const tempEdit = temporaryPayStubDetailEdits.get(detailId);
    const effectiveAgreementId = tempEdit?.agreementId ?? getStringField('agreementId');
    const effectiveClassificationId = tempEdit?.classificationId ?? getStringField('classificationId');

    const handleCellActivate = (rIdx: number, cIdx: number) => {
        setActiveCell([index, cIdx]);
        setEditingCell([index, cIdx]);

        // Trigger auto-fill on first cell activation in this row
        if (finalEmployeeDefaults && detailId) {
            applyEmployeeDefaultsOnce(detailId, finalEmployeeDefaults);
        }
    };

    const handleCellDeactivate = useCallback(() => {
        setEditingCell(null);
        requestAnimationFrame(() => {
            tableRef.current?.focus();
        });
    }, [setEditingCell, tableRef]);

    // Memoize event handlers to prevent re-renders
    const handleDeleteToggle = useCallback(() => {
        handleRowDeleteToggle(detailId);
    }, [handleRowDeleteToggle, detailId]);

    const handleDuplicate = useCallback(() => {
        handleRowDuplicate(index);
    }, [handleRowDuplicate, index]);

    // Get validation errors for this PayStub
    const payStubErrors = validationErrorsByPayStubId.get(finalPayStubId) || [];

    // Get total validation errors for this row for accessibility announcement
    const rowErrorCount = payStubErrors.filter((error: ValidationError) => error.detailId === detailId).length;
    const rowHasErrors = rowErrorCount > 0;

    return (
        <tr
            key={detailId}
            className={`${isDeleted ? styles.deletedRow : ''} ${index % 2 === 1 ? styles.alternateRow : ''}`.trim()}
            role="row"
            aria-label={`Detail row ${index + 1} for ${employee?.firstName || ''} ${employee?.lastName || ''}${rowHasErrors ? ` (${rowErrorCount} validation error${rowErrorCount !== 1 ? 's' : ''})` : ''}`}
            aria-selected={false}
            tabIndex={-1}>
            {columns.map((column) => {
                const field = column.uid;
                const isDisabled: boolean = readOnly || Boolean(isDeleted);
                const cellKey = `${detailId}-${column.uid}`;
                const isNumeric = numericColumnUids.has(column.uid);
                const isDayColumn = column.uid === 'name';
                let cellAlign: 'left' | 'center' | 'right' = 'left';

                // Use previously declared payStubErrors for this specific field and detail row

                // Check for validation errors - handles both detail-level and header-level errors
                const hasValidationError = payStubErrors.some((error: ValidationError) => {
                    // Detail-level errors: must match both detailId and columnUid
                    if (error.detailId) {
                        return error.detailId === detailId && error.columnUid === column.uid;
                    }
                    // Header-level errors: restrict highlighting to agreement/classification columns
                    if (error.columnUid === 'header') {
                        return column.uid === 'agreementId' || column.uid === 'classificationId';
                    }
                    return error.columnUid === column.uid;
                });

                // Check for agreement/classification related errors
                const hasAgreementClassificationError = payStubErrors.some((err: ValidationError) => {
                    // Detail-level agreement/classification errors
                    if (err.detailId) {
                        return (
                            err.detailId === detailId &&
                            err.columnUid === 'agreementId' &&
                            (column.uid === 'agreementId' || column.uid === 'classificationId')
                        );
                    }
                    // Header-level agreement/classification errors
                    return (
                        (err.columnUid === 'agreementId' || err.columnUid === 'header') &&
                        (column.uid === 'agreementId' || column.uid === 'classificationId')
                    );
                });

                const cellHasError = hasValidationError || hasAgreementClassificationError;

                // Get error messages for tooltip/aria-label
                const cellErrorMessages = payStubErrors
                    .filter((error: ValidationError) => {
                        if (error.detailId) {
                            return (
                                error.detailId === detailId &&
                                (error.columnUid === column.uid ||
                                    (error.columnUid === 'agreementId' &&
                                        (column.uid === 'agreementId' || column.uid === 'classificationId')))
                            );
                        }
                        return (
                            error.columnUid === column.uid ||
                            error.columnUid === 'header' ||
                            (error.columnUid === 'agreementId' && (column.uid === 'agreementId' || column.uid === 'classificationId'))
                        );
                    })
                    .map((error: ValidationError) => error.message)
                    .join('; ');

                // Check different highlighting states for default values
                const cellDefaultState = (() => {
                    if (column.uid === 'agreementId') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultAgreementId != null;
                        const hasCurrentValue = mergedData.agreementId != null;

                        if (hasCurrentValue && hasDefaultValue && !isAgreementDefault) {
                            return 'nonDefault'; // Value differs from employee default
                        } else if (hasCurrentValue && !hasDefaultValue) {
                            return 'noDefault'; // Employee has no default for this field
                        }
                    } else if (column.uid === 'classificationId') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultClassificationId != null;
                        const hasCurrentValue = mergedData.classificationId != null;

                        if (hasCurrentValue && hasDefaultValue && !isClassificationDefault) {
                            return 'nonDefault'; // Value differs from employee default
                        } else if (hasCurrentValue && !hasDefaultValue) {
                            return 'noDefault'; // Employee has no default for this field
                        }
                    } else if (column.uid === 'hourlyRate') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultHourlyRate != null;
                        const hasCurrentValue = mergedData.hourlyRate != null;

                        if (hasCurrentValue && hasDefaultValue && !isHourlyRateDefault) {
                            return 'nonDefault'; // Value differs from employee default
                        } else if (hasCurrentValue && !hasDefaultValue) {
                            return 'noDefault'; // Employee has no default for this field
                        }
                    }
                    return 'default'; // Default state (no highlighting)
                })();

                // Backward compatibility flags
                const cellHasNonDefault = cellDefaultState === 'nonDefault';
                const cellHasNoDefault = cellDefaultState === 'noDefault';

                // Check if this cell has unsaved edits
                const cellHasUnsavedEdits = (() => {
                    const edits = temporaryPayStubDetailEdits.get(detailId);
                    return edits && edits[field] !== undefined;
                })();

                // Determine combined state for unsaved edits with default value context
                const unsavedEditState = (() => {
                    if (!cellHasUnsavedEdits) return 'none';

                    // For unsaved edits, check what the edited value's relationship is to defaults
                    if (column.uid === 'agreementId') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultAgreementId != null;
                        if (hasDefaultValue) {
                            // Check if the current edited value differs from default
                            const editedValue = temporaryPayStubDetailEdits.get(detailId)?.[field];
                            const isDifferentFromDefault = editedValue != null && editedValue !== finalEmployeeDefaults.defaultAgreementId;
                            return isDifferentFromDefault ? 'nonDefault' : 'default';
                        } else {
                            return 'noDefault';
                        }
                    } else if (column.uid === 'classificationId') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultClassificationId != null;
                        if (hasDefaultValue) {
                            const editedValue = temporaryPayStubDetailEdits.get(detailId)?.[field];
                            const isDifferentFromDefault =
                                editedValue != null && editedValue !== finalEmployeeDefaults.defaultClassificationId;
                            return isDifferentFromDefault ? 'nonDefault' : 'default';
                        } else {
                            return 'noDefault';
                        }
                    } else if (column.uid === 'hourlyRate') {
                        const hasDefaultValue = finalEmployeeDefaults?.defaultHourlyRate != null;
                        if (hasDefaultValue) {
                            const editedValue = temporaryPayStubDetailEdits.get(detailId)?.[field];
                            const defaultRateAsNumber = parseFloat(finalEmployeeDefaults.defaultHourlyRate);
                            const isDifferentFromDefault =
                                editedValue != null && !isNaN(defaultRateAsNumber) && editedValue !== defaultRateAsNumber;
                            return isDifferentFromDefault ? 'nonDefault' : 'default';
                        } else {
                            return 'noDefault';
                        }
                    }

                    return 'default'; // For non-default-tracked fields, just treat as regular edit
                })();

                // Combined state flags
                const cellHasUnsavedEditWithNonDefault = unsavedEditState === 'nonDefault';
                const cellHasUnsavedEditWithNoDefault = unsavedEditState === 'noDefault';

                if (isNumeric) cellAlign = 'right';
                else if (isDayColumn) cellAlign = 'center';

                const colIndex = columns.findIndex((c) => c.uid === column.uid);
                const isActive = activeCell?.[0] === index && activeCell?.[1] === colIndex;
                const isEditingActive = editingCell?.[0] === index && editingCell?.[1] === colIndex;

                // Enhanced accessibility props
                const accessibilityProps = {
                    'aria-label':
                        `${column.name || getFieldDisplayName(column.uid)} for ${employee?.firstName || ''} ${employee?.lastName || ''}`.trim(),
                    'aria-describedby': cellHasError ? `error-${detailId}-${column.uid}` : undefined,
                    'aria-invalid': cellHasError ? 'true' : 'false',
                    'aria-required': isFieldRequired(column.uid, mergedData),
                    role: 'gridcell'
                };

                const commonEditableProps = {
                    isDisabled,
                    isQuiet: false,
                    rowIndex: index,
                    colIndex,
                    isActive,
                    isEditingActive,
                    onActivate: handleCellActivate,
                    onDeactivate: handleCellDeactivate,
                    hasError: cellHasError,
                    errorMessage: cellErrorMessages,
                    ...accessibilityProps
                };

                let cellContent: React.ReactNode = null;

                if (column.uid === 'actions') {
                    cellContent = (
                        <TooltipTrigger>
                            <ActionButton
                                isQuiet
                                onPress={handleDeleteToggle}
                                isDisabled={readOnly}
                                aria-label={isDeleted ? 'Restore Detail Row' : 'Clear Detail Row'}>
                                {isDeleted ? <UndoIcon /> : <Close />}
                            </ActionButton>
                            <Tooltip>
                                <Text>{isDeleted ? 'Restore row' : 'Clear row'}</Text>
                            </Tooltip>
                        </TooltipTrigger>
                    );
                } else if (column.uid === 'name') {
                    const dateObj = parseWorkDate(mergedData.workDate);
                    const dayText = formatDayName(dateObj);

                    cellContent = (
                        <Flex gap="size-50" alignItems="center" justifyContent="center">
                            {!readOnly && (
                                <TooltipTrigger>
                                    <ActionButton
                                        isQuiet
                                        onPress={handleDuplicate}
                                        isDisabled={isDisabled || Boolean(isDeleted)}
                                        aria-label="Duplicate Detail Row">
                                        <CopyIcon />
                                    </ActionButton>
                                    <Tooltip>
                                        <Text>Duplicate row</Text>
                                    </Tooltip>
                                </TooltipTrigger>
                            )}
                            <ReadOnlyCell value={dayText} />
                        </Flex>
                    );
                } else if (column.uid === 'workDate') {
                    const dateObj = parseWorkDate(mergedData.workDate);
                    const dateText = formatWorkDate(dateObj);

                    cellContent = <ReadOnlyCell value={dateText} />;
                } else if (column.uid === 'totalHours') {
                    const st = Number(mergedData.stHours || 0);
                    const ot = Number(mergedData.otHours || 0);
                    const dt = Number(mergedData.dtHours || 0);
                    const calculatedTotal = st + ot + dt;
                    cellContent = <NumericCellContent value={calculatedTotal} format="n2" formatNumber={formatNumber} />;
                } else if (editableColumnUids.has(column.uid)) {
                    if (
                        (column.uid === 'bonus' && !showBonusColumn) ||
                        (column.uid === 'expenses' && !showExpensesColumn) ||
                        (column.uid === 'dtHours' && !showDTHoursColumn)
                    ) {
                        cellContent = <ReadOnlyCell value={getNumberField(field)} />;
                    } else {
                        if (['stHours', 'otHours', 'dtHours', 'hourlyRate', 'bonus', 'expenses'].includes(column.uid)) {
                            // Generate test ID for hours inputs specifically
                            const testId =
                                payStubIndex !== undefined && column.uid === 'stHours' ? `hours-input-ps-${payStubIndex}` : undefined;

                            cellContent = (
                                <EditableNumberCell
                                    {...commonEditableProps}
                                    value={getNumberField(field)}
                                    onChange={createNumberFieldChangeHandler(field)}
                                    format={['bonus', 'expenses', 'hourlyRate'].includes(column.uid) ? 'c2' : 'n2'}
                                    formatNumber={formatNumber}
                                    testId={testId}
                                />
                            );
                        } else if (column.uid === 'jobCode') {
                            cellContent = (
                                <EditableTextFieldCell
                                    {...commonEditableProps}
                                    value={getStringField('jobCode')}
                                    onChange={handleJobCodeChange}
                                    transformValue={transformJobCodeValue}
                                />
                            );
                        } else if (column.uid === 'costCenter') {
                            if (!showCostCenterColumn) {
                                cellContent = <ReadOnlyCell value={getStringField('costCenter')} />;
                            } else {
                                cellContent = (
                                    <EditableTextFieldCell
                                        {...commonEditableProps}
                                        value={getStringField('costCenter')}
                                        onChange={handleCostCenterChange}
                                    />
                                );
                            }
                        } else if (column.uid === 'agreementId') {
                            cellContent = (
                                <AgreementComboBoxCell
                                    {...commonEditableProps}
                                    payStubEmployeeId={employee?.id || null}
                                    employerGuid={employerGuid}
                                    value={getStringField('agreementId')}
                                    onChange={handleAgreementChange}
                                />
                            );
                        } else if (column.uid === 'classificationId') {
                            cellContent = (
                                <ClassificationComboBoxCell
                                    {...commonEditableProps}
                                    agreementId={getStringField('agreementId')}
                                    value={getStringField('classificationId')}
                                    onChange={handleClassificationChange}
                                />
                            );
                        } else if (column.uid === 'subClassificationId') {
                            cellContent = (
                                <SubClassificationComboBoxCell
                                    {...commonEditableProps}
                                    agreementId={getStringField('agreementId')}
                                    classificationId={getStringField('classificationId')}
                                    value={getStringField('subClassificationId')}
                                    onChange={handleSubClassificationChange}
                                />
                            );
                        } else if (column.uid === 'earningsCode') {
                            if (!showEarningsCodesColumn) {
                                cellContent = <ReadOnlyCell value={getStringField('earningsCode')} />;
                            } else {
                                cellContent = (
                                    <EarningsCodeComboBoxCell
                                        {...commonEditableProps}
                                        value={getStringField('earningsCode')}
                                        onChange={handleEarningsCodeChange}
                                    />
                                );
                            }
                        }
                    }
                } else {
                    // For other fields, try to get as string first, then number
                    const stringValue = getStringField(field);
                    const numberValue = getNumberField(field);
                    cellContent = <ReadOnlyCell value={stringValue || numberValue} />;
                }

                const cellWrapper = (
                    <View UNSAFE_className={styles.cellContentWrapper} UNSAFE_style={isDayColumn ? { justifyContent: 'center' } : {}}>
                        {cellContent}
                        {/* Screen reader accessible error message */}
                        {cellHasError && (
                            <div id={`error-${detailId}-${column.uid}`} className="sr-only" aria-live="polite" role="alert">
                                {cellErrorMessages}
                            </div>
                        )}
                    </View>
                );

                // Build class list for cell styling with proper precedence
                // Priority: Error > Unsaved Edits (with variants) > Non-Default Values > No Default Values
                // Phase 4.1: Add hasErrors class for validation errors (FR-06)
                const cellClasses = [
                    cellHasError && styles.hasErrors, // Phase 4.1: Cell-level highlights
                    cellHasError && styles.validationErrorCell,
                    !cellHasError && cellHasUnsavedEditWithNonDefault && styles.editedNonDefaultCell, // Green background + blue bar for unsaved edits that differ from default
                    !cellHasError && cellHasUnsavedEditWithNoDefault && styles.editedNoDefaultCell, // Green background + blue bar for unsaved edits where no default exists
                    !cellHasError &&
                        cellHasUnsavedEdits &&
                        !cellHasUnsavedEditWithNonDefault &&
                        !cellHasUnsavedEditWithNoDefault &&
                        styles.editedCell, // Green background + green bar for regular unsaved edits
                    !cellHasError && !cellHasUnsavedEdits && cellHasNonDefault && styles.nonDefaultValueCell, // Blue background + bar for non-default values
                    !cellHasError && !cellHasUnsavedEdits && cellHasNoDefault && styles.noDefaultValueCell // Blue bar only for no default values
                ]
                    .filter(Boolean)
                    .join(' ');

                // Determine tooltip content based on cell state with priority for combined states
                // Phase 4.5: Tooltip shows first error message on hover/focus (accessibility)
                let tooltipContent = null;
                if (cellHasError) {
                    // Phase 4.2: Tooltip on hover with first error message
                    tooltipContent = cellErrorMessages || 'Validation Error';
                } else if (cellHasUnsavedEditWithNonDefault) {
                    // Combined state: unsaved edit that differs from employee default
                    let defaultValueText = '';
                    if (column.uid === 'agreementId' && finalEmployeeDefaults?.defaultAgreementId) {
                        const agreementName = getAgreementNameById(finalEmployeeDefaults.defaultAgreementId);
                        defaultValueText = agreementName || `Agreement ID ${finalEmployeeDefaults.defaultAgreementId}`;
                    } else if (column.uid === 'classificationId' && finalEmployeeDefaults?.defaultClassificationId) {
                        const classificationName = getClassificationNameById(finalEmployeeDefaults.defaultClassificationId);
                        defaultValueText = classificationName || `Classification ID ${finalEmployeeDefaults.defaultClassificationId}`;
                    } else if (column.uid === 'hourlyRate' && finalEmployeeDefaults?.defaultHourlyRate) {
                        defaultValueText = `$${parseFloat(finalEmployeeDefaults.defaultHourlyRate).toFixed(2)}`;
                    }

                    if (defaultValueText) {
                        tooltipContent = `Unsaved Edit - Non-Default Value. Default value is: ${defaultValueText}`;
                    } else {
                        tooltipContent = 'Unsaved Edit - Non-Default Value';
                    }
                } else if (cellHasUnsavedEditWithNoDefault) {
                    // Combined state: unsaved edit where employee has no default for this field
                    tooltipContent = 'Unsaved Edit - Employee default not set';
                } else if (cellHasUnsavedEdits) {
                    // Regular unsaved edit (matches default or non-default-tracked field)
                    // Check if the edited value is empty/null but there's a default available
                    const editedValue = temporaryPayStubDetailEdits.get(detailId)?.[field];
                    const isEditedValueEmpty = editedValue === null || editedValue === undefined || editedValue === '';

                    if (isEditedValueEmpty) {
                        // Show default value if available when edited value is empty
                        let defaultValueText = '';
                        if (column.uid === 'agreementId' && finalEmployeeDefaults?.defaultAgreementId) {
                            const agreementName = getAgreementNameById(finalEmployeeDefaults.defaultAgreementId);
                            defaultValueText = agreementName || `Agreement ID ${finalEmployeeDefaults.defaultAgreementId}`;
                        } else if (column.uid === 'classificationId' && finalEmployeeDefaults?.defaultClassificationId) {
                            const classificationName = getClassificationNameById(finalEmployeeDefaults.defaultClassificationId);
                            defaultValueText = classificationName || `Classification ID ${finalEmployeeDefaults.defaultClassificationId}`;
                        } else if (column.uid === 'hourlyRate' && finalEmployeeDefaults?.defaultHourlyRate) {
                            defaultValueText = `$${parseFloat(finalEmployeeDefaults.defaultHourlyRate).toFixed(2)}`;
                        }

                        if (defaultValueText) {
                            tooltipContent = `Unsaved Edit. Default value is: ${defaultValueText}`;
                        } else {
                            tooltipContent = 'Unsaved Edit';
                        }
                    } else {
                        tooltipContent = 'Unsaved Edit';
                    }
                } else if (cellHasNonDefault) {
                    // Show what the default value would be for this field
                    let defaultValueText = '';
                    if (column.uid === 'agreementId' && finalEmployeeDefaults?.defaultAgreementId) {
                        const agreementName = getAgreementNameById(finalEmployeeDefaults.defaultAgreementId);
                        defaultValueText = agreementName || `Agreement ID ${finalEmployeeDefaults.defaultAgreementId}`;
                    } else if (column.uid === 'classificationId' && finalEmployeeDefaults?.defaultClassificationId) {
                        const classificationName = getClassificationNameById(finalEmployeeDefaults.defaultClassificationId);
                        defaultValueText = classificationName || `Classification ID ${finalEmployeeDefaults.defaultClassificationId}`;
                    } else if (column.uid === 'hourlyRate' && finalEmployeeDefaults?.defaultHourlyRate) {
                        defaultValueText = `$${parseFloat(finalEmployeeDefaults.defaultHourlyRate).toFixed(2)}`;
                    }

                    if (defaultValueText) {
                        tooltipContent = `Non-Default Value. Default value is: ${defaultValueText}`;
                    } else {
                        tooltipContent = 'Non-Default Value';
                    }
                } else if (cellHasNoDefault) {
                    tooltipContent = 'Employee default not set';
                }

                return (
                    <td
                        key={cellKey}
                        style={{ width: column.width ? `${column.width}px` : undefined }}
                        className={cellClasses}
                        title={tooltipContent || undefined} // Native browser tooltip
                        role="gridcell"
                        aria-label={`${getFieldDisplayName(column.uid)} for ${employee?.firstName || ''} ${employee?.lastName || ''}`.trim()}
                        aria-describedby={cellHasError ? `error-${detailId}-${column.uid}` : undefined}
                        aria-invalid={cellHasError ? 'true' : 'false'}
                        tabIndex={isActive ? 0 : -1}>
                        {cellWrapper}
                    </td>
                );
            })}
        </tr>
    );
};

export default TimeSheetDetailRow;
