import React, { Suspense, useMemo } from 'react';
import { ProgressCircle } from '@adobe/react-spectrum';
import { usePaginationFragment, useFragment } from 'react-relay';
// TODO: useTimesheetQueryData was removed from TimesheetUIContext - need to replace with direct query data
import { EmployeeDataPaginationFragment } from '@/src/fragments/EmployeeDataFragment';
import { useTimesheetContext } from '../TimesheetContext';
import UploadTimeSheetContent from './UploadTimeSheetContent';
import type { UploadTimeSheetFragments_timeSheetConsolidated$key } from '@/relay/__generated__/UploadTimeSheetFragments_timeSheetConsolidated.graphql';
import { UploadTimeSheetConsolidatedFragment } from './fragments/UploadTimeSheetFragments';

/**
 * Props for UploadTimeSheetWithEmployeeData component
 */
interface UploadTimeSheetWithEmployeeDataProps {
    /** Employer GUID for fetching employee data */
    employerGuid: string;
    /** TimeSheet fragment reference for upload operations */
    timeSheetRef: UploadTimeSheetFragments_timeSheetConsolidated$key;
    /** Query reference containing employee data from parent query */
    queryRef: any;
    /** Callback when upload dialog is closed */
    onClose: () => void;
    /** Whether the component is in read-only mode */
    readOnly?: boolean;
}

/**
 * CSV Upload component that uses employee data from parent query (Phase 5)
 * 
 * This component follows the Phase 5 implementation plan by:
 * 1. Getting employee data from the parent timesheet query via context
 * 2. Using usePaginationFragment to access employee data properly
 * 3. Processing employee data and passing it to child components
 * 4. Eliminating the need for separate employee data queries
 * 
 * Architecture:
 * - Uses useTimesheetQueryData to access page-level employee data
 * - Processes employee data from Relay fragments with proper data flow
 * - Supports both SSN and Employee ID matching for CSV uploads
 * - Follows Relay best practices for data accessing and component composition
 * 
 * @see /docs/CSV_UPLOAD_RELAY_INTEGRATION_PLAN.md for implementation details
 */
const UploadTimeSheetWithEmployeeData: React.FC<UploadTimeSheetWithEmployeeDataProps> = ({
    employerGuid,
    timeSheetRef,
    queryRef,
    onClose,
    readOnly = false
}) => {
    // Resolve the timesheet fragment to get data
    const timeSheetData = useFragment(UploadTimeSheetConsolidatedFragment, timeSheetRef);
    
    const { data: employeeData, loadNext, hasNext } = usePaginationFragment(
        EmployeeDataPaginationFragment,
        queryRef as Parameters<typeof usePaginationFragment>[1]  // Employee data from parent query
    );

    // Convert fragment data for CSV processing (keep existing logic)
    const processedEmployeeData = useMemo(() => {
        // Type guard to ensure employeeData has the expected structure
        if (!employeeData || typeof employeeData !== 'object' || !('employeesByEmployerGuidAsync' in employeeData)) return [];
        
        const connection = employeeData.employeesByEmployerGuidAsync;
        if (!connection || typeof connection !== 'object' || !('edges' in connection) || !Array.isArray(connection.edges)) return [];
        
        return connection.edges.map((edge: { node: { id: string; firstName: string; lastName: string; externalEmployeeId: string; ssn: string } }) => ({
            id: edge.node.id,
            firstName: edge.node.firstName,
            lastName: edge.node.lastName,
            externalEmployeeId: edge.node.externalEmployeeId,
            ssn: edge.node.ssn
        })) || [];
    }, [employeeData]);

    return (
        <UploadTimeSheetContent
            timeSheetRef={timeSheetRef}
            onClose={onClose}
            readOnly={readOnly}
            employeeData={processedEmployeeData}
            hasMoreEmployees={hasNext}
            loadMoreEmployees={() => loadNext(50)}
        />
    );
};

export default UploadTimeSheetWithEmployeeData;
