/**
 * Unit tests for Data Transformation Utilities
 */

import { transformValidatedData } from './dataTransformation';
import { ProcessedTimesheetEntry, PayStubUpload } from '../types';
import { Constants } from '@/src/constants/global';
import type { UploadTimeSheetFragments_employees$data } from '@/relay/__generated__/UploadTimeSheetFragments_employees.graphql';
import type { UploadTimeSheetFragments_agreements$data } from '@/relay/__generated__/UploadTimeSheetFragments_agreements.graphql';

// Mock uuid generation to make tests deterministic
jest.mock('uuid', () => ({
    v4: jest.fn().mockReturnValue('mock-uuid')
}));

// Mock Constants for earnings codes
jest.mock('@/src/constants/global', () => ({
    Constants: {
        TimesheetEarningCodesList: [
            { value: 'REG', text: 'Regular' },
            { value: 'OT', text: 'Overtime' },
            { value: 'VAC', text: 'Vacation' }
        ]
    }
}));

describe('Data Transformation Utilities', () => {
    describe('transformValidatedData', () => {
        // Mock Relay fragment data that matches UploadTimeSheetFragments_employees$data
        const mockContextEmployees: UploadTimeSheetFragments_employees$data = [
            {
                id: '1',
                firstName: 'John',
                lastName: 'Smith',
                ssn: ['123456789'], // GraphQL returns SSN as array of bytes
                externalEmployeeId: '123',
                ' $fragmentType': 'UploadTimeSheetFragments_employees'
            },
            {
                id: '2',
                firstName: 'Jane',
                lastName: 'Doe',
                ssn: ['987654321'],
                externalEmployeeId: '456',
                ' $fragmentType': 'UploadTimeSheetFragments_employees'
            },
            {
                id: 'guid-employee',
                firstName: 'Bob',
                lastName: 'Johnson',
                ssn: ['111223333'],
                externalEmployeeId: '789',
                ' $fragmentType': 'UploadTimeSheetFragments_employees'
            }
        ];

        // Mock Relay fragment data that matches UploadTimeSheetFragments_agreements$data
        const mockContextAgreements: UploadTimeSheetFragments_agreements$data = [
            {
                id: '101',
                name: 'Agreement 1',
                ' $fragmentType': 'UploadTimeSheetFragments_agreements'
            },
            {
                id: '102',
                name: 'Agreement 2',
                ' $fragmentType': 'UploadTimeSheetFragments_agreements'
            }
        ];

        // Helper function to create a valid processed entry
        const createValidEntry = (overrides = {}): ProcessedTimesheetEntry => ({
            externalEmployeeId: '123',
            workDate: '2023-01-15',
            stHours: 8,
            otHours: 2,
            dtHours: 0,
            hourlyRate: 25.5,
            bonus: 100,
            expenses: 50,
            rowIndex: 1,
            isValid: true,
            errors: [],
            ...overrides
        });

        it('should match employee by external ID', () => {
            const entries = [createValidEntry()];
            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].employeeId).toBe(1); // Matched to first employee (converted from string '1' to number)
            expect(result.payStubs[0].name).toBe('John Smith'); // firstName + lastName
            expect(result.rowErrors.length).toBe(0);
        });

        it('should match employee by SSN', () => {
            const entries = [
                createValidEntry({
                    externalEmployeeId: undefined,
                    ssn: '987654321'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].employeeId).toBe(2); // Matched to second employee (converted from string '2' to number)
            expect(result.payStubs[0].name).toBe('Jane Doe'); // firstName + lastName
            expect(result.rowErrors.length).toBe(0);
        });

        it('should handle string employee IDs (GUIDs)', () => {
            const entries = [
                createValidEntry({
                    externalEmployeeId: '789'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].employeeId).toBe('guid-employee'); // String ID preserved
            expect(result.payStubs[0].name).toBe('Bob Johnson'); // firstName + lastName
            expect(result.rowErrors.length).toBe(0);
        });

        it('should match agreement by name', () => {
            const entries = [
                createValidEntry({
                    agreementName: 'Agreement 1'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].details[0].agreementId).toBe(101);
            expect(result.rowErrors.length).toBe(0);
        });

        it('should match agreement case-insensitively', () => {
            const entries = [
                createValidEntry({
                    agreementName: 'agreement 2' // Lowercase
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].details[0].agreementId).toBe(102);
            expect(result.rowErrors.length).toBe(0);
        });

        it('should report error for unmatched agreement', () => {
            const entries = [
                createValidEntry({
                    agreementName: 'Non-existent Agreement'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(0); // No valid pay stubs
            expect(result.rowErrors.length).toBe(1);
            expect(result.rowErrors[0].code).toBe('AGREEMENT_NOT_FOUND');
        });

        it('should match earnings code by name', () => {
            const entries = [
                createValidEntry({
                    earningsCode: 'Regular' // Should match to 'REG'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].details[0].earningsCode).toBe('REG');
            expect(result.rowErrors.length).toBe(0);
        });

        it('should report error for unmatched earnings code', () => {
            const entries = [
                createValidEntry({
                    earningsCode: 'Non-existent Code'
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1); // Still creates pay stub
            expect(result.payStubs[0].details[0].earningsCode).toBeNull(); // But earnings code is null
            expect(result.rowErrors.length).toBe(1);
            expect(result.rowErrors[0].code).toBe('INVALID_VALUE');
        });

        it('should report error for unmatched employee', () => {
            const entries = [
                createValidEntry({
                    externalEmployeeId: 'unknown-id',
                    ssn: undefined
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(0); // No valid pay stubs
            expect(result.rowErrors.length).toBe(1);
            expect(result.rowErrors[0].code).toBe('EMPLOYEE_NOT_FOUND');
        });

        it('should group entries by employee', () => {
            const entries = [createValidEntry({ workDate: '2023-01-15' }), createValidEntry({ workDate: '2023-01-16' })];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1); // One pay stub for the employee
            expect(result.payStubs[0].details.length).toBe(2); // With two detail entries
            expect(result.rowErrors.length).toBe(0);
        });

        it('should calculate aggregated values correctly', () => {
            const entries = [
                createValidEntry({
                    stHours: 8,
                    otHours: 2,
                    dtHours: 1,
                    bonus: 100,
                    expenses: 50
                }),
                createValidEntry({
                    stHours: 7,
                    otHours: 3,
                    dtHours: 0,
                    bonus: 150,
                    expenses: 75
                })
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].stHours).toBe(15); // 8 + 7
            expect(result.payStubs[0].otHours).toBe(5); // 2 + 3
            expect(result.payStubs[0].dtHours).toBe(1); // 1 + 0
            expect(result.payStubs[0].totalHours).toBe(21); // 15 + 5 + 1
            expect(result.payStubs[0].bonus).toBe(250); // 100 + 150
            expect(result.payStubs[0].expenses).toBe(125); // 50 + 75
        });

        it('should filter out invalid entries', () => {
            const entries = [createValidEntry(), createValidEntry({ isValid: false })];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(1);
            expect(result.payStubs[0].details.length).toBe(1); // Only one valid detail
        });

        it('should handle multiple employees', () => {
            const entries = [
                createValidEntry({ externalEmployeeId: '123' }), // First employee
                createValidEntry({ externalEmployeeId: '456' }) // Second employee
            ];

            const result = transformValidatedData(entries, mockContextEmployees, mockContextAgreements);

            expect(result.payStubs.length).toBe(2); // Two pay stubs for two employees
            expect(result.payStubs[0].employeeId).toBe(1);
            expect(result.payStubs[1].employeeId).toBe(2);
        });
    });

    // Note: convertToModifiablePayStubs function removed as part of fragment migration
    // All data transformations now use Relay fragments directly
});
