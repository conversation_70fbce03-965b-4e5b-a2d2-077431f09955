import React, { useReducer, useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useFragment, useRelayEnvironment, graphql } from 'react-relay';
import { Button, Text, DialogContainer } from '@adobe/react-spectrum';
import Upload from '@spectrum-icons/workflow/DataUpload';
import UploadDialog from './UploadDialog';
import ProcessingDialog from './ProcessingDialog';
import {
    ProcessingState,
    UploadAction,
    UploadActionType,
    UploadState,
    csvColumnMappings,
    NormalizedCsvHeader,
    PayStubUpload
} from './types';
import { parseCsvFile } from './utils/csvProcessing';
import { validateCsvData } from './utils/dataValidation';
import { transformEnrichedData, convertEnrichedToRelayPayStubInput } from './utils/dataTransformation';
import { enrichPayStubData, type DefaultApplication } from './utils/dataEnrichment';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { bulkAddPayStubs } from '@/src/mutations/timesheet/BulkAddPayStubsMutation';
import { useErrorRecovery } from '@/src/hooks/useErrorRecovery';
import { TimesheetError, ErrorType } from '@/errorHandling';
import type { UploadTimeSheetFragments_timeSheetConsolidated$key } from '@/relay/__generated__/UploadTimeSheetFragments_timeSheetConsolidated.graphql';

// Import the consolidated fragment from the dedicated fragments file
import { UploadTimeSheetConsolidatedFragment } from './fragments/UploadTimeSheetFragments';

/**
 * Legacy interfaces for backward compatibility - will be removed in future versions
 */
export interface EmployeeData {
    value: number;
    text: string;
    SSN?: string | null;
    externalEmployeeId?: string | null;
}

export interface AgreementData {
    value: number;
    text: string;
}

/**
 * UploadTimeSheet component - Provides UI for uploading CSV files
 *
 * Architecture Improvement:
 * - Now uses a single consolidated Relay fragment for simplified data management
 * - Eliminates complex multi-fragment dependencies that made testing difficult
 * - Maintains existing functionality while improving maintainability
 * - Employee and agreement data is fetched during enrichment phase via API calls
 */
interface UploadTimeSheetProps {
    timeSheet: UploadTimeSheetFragments_timeSheetConsolidated$key;
    readOnly: boolean;
}

/**
 * Initial state for the upload reducer (no payStubs field - data goes to Relay store)
 */
const initialUploadState: UploadState = {
    status: ProcessingState.IDLE,
    file: null,
    data: [],
    processedData: [],
    payStubs: [], // Keep for compatibility but not used for storing data
    errors: [],
    progress: 0,
    headers: [],
    rowCount: 0,
    appliedDefaults: []
};

/**
 * Reducer function for managing upload state
 * @param state Current state
 * @param action Action to perform
 * @returns New state
 */
const uploadReducer = (state: UploadState, action: UploadAction): UploadState => {
    switch (action.type) {
        case UploadActionType.SET_FILE:
            return {
                ...state,
                file: action.payload.file,
                status: ProcessingState.IDLE,
                errors: [], // Clear previous errors
                progress: 0,
                data: [],
                processedData: [],
                payStubs: [],
                headers: [],
                rowCount: 0,
                appliedDefaults: []
            };
        case UploadActionType.CLEAR_FILE:
            return {
                ...state,
                file: null,
                status: ProcessingState.IDLE,
                errors: [],
                progress: 0,
                appliedDefaults: []
            };
        case UploadActionType.RESET:
            return initialUploadState;
        case UploadActionType.PARSE_START:
            return {
                ...state,
                status: ProcessingState.PARSING_FILE,
                data: [], // Clear previous data
                errors: [], // Clear previous errors
                progress: 0,
                headers: [],
                rowCount: 0
            };
        case UploadActionType.PARSE_PROGRESS:
            return {
                ...state,
                progress: action.payload.progress
            };
        case UploadActionType.PARSE_SUCCESS:
            return {
                ...state,
                status: ProcessingState.VALIDATING_DATA, // Move to next state
                data: action.payload.data,
                headers: action.payload.headers,
                rowCount: action.payload.rowCount,
                progress: 100
            };
        case UploadActionType.PARSE_ERROR:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: [...state.errors, ...action.payload.errors],
                progress: 0
            };
        case UploadActionType.VALIDATE_DATA_START:
            return {
                ...state,
                status: ProcessingState.VALIDATING_DATA,
                errors: state.errors.filter((error) => error.type !== 'row'), // Clear previous row errors
                progress: 0
            };
        case UploadActionType.VALIDATE_DATA_SUCCESS:
            return {
                ...state,
                status: ProcessingState.ENRICHING_DATA, // Move to next state
                processedData: action.payload.processedData,
                progress: 100
            };
        case UploadActionType.VALIDATE_DATA_ERROR:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: [...state.errors, ...action.payload.errors],
                progress: 0
            };
        case UploadActionType.ENRICH_DATA_START:
            return {
                ...state,
                status: ProcessingState.ENRICHING_DATA,
                errors: state.errors.filter(
                    (error) =>
                        error.code !== 'EMPLOYEE_NOT_FOUND' &&
                        error.code !== 'AGREEMENT_NOT_FOUND' &&
                        error.code !== 'CLASSIFICATION_NOT_FOUND' &&
                        error.code !== 'SUBCLASSIFICATION_NOT_FOUND'
                ), // Clear previous matching errors
                progress: 0
            };
        case UploadActionType.ENRICH_DATA_PROGRESS:
            return {
                ...state,
                progress: action.payload.progress
            };
        case UploadActionType.ENRICH_DATA_SUCCESS:
            return {
                ...state,
                status: ProcessingState.TRANSFORMING_DATA, // Move to next state
                processedData: action.payload.processedData,
                appliedDefaults: action.payload.appliedDefaults,
                progress: 100
            };
        case UploadActionType.ENRICH_DATA_ERROR:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: [...state.errors, ...action.payload.errors],
                progress: 0
            };
        case UploadActionType.TRANSFORM_DATA_START:
            return {
                ...state,
                status: ProcessingState.TRANSFORMING_DATA,
                progress: 0
            };
        case UploadActionType.TRANSFORM_DATA_SUCCESS:
            return {
                ...state,
                status: ProcessingState.FINALIZING_DATA, // Move to final data processing
                payStubs: action.payload.payStubs,
                payStubsToCreate: action.payload.payStubs, // Store for context update
                progress: 100
            };
        case UploadActionType.TRANSFORM_DATA_ERROR:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: [...state.errors, ...action.payload.errors],
                progress: 0
            };
        case UploadActionType.FINALIZE_DATA_START:
            return {
                ...state,
                status: ProcessingState.FINALIZING_DATA,
                progress: 0
            };
        case UploadActionType.FINALIZE_DATA_SUCCESS:
            return {
                ...state,
                status: ProcessingState.SUCCESS,
                progress: 100
            };
        case UploadActionType.FINALIZE_DATA_ERROR:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: [...state.errors, ...action.payload.errors],
                progress: 0
            };
        case UploadActionType.UPLOAD_SUCCESS:
            return {
                ...state,
                status: ProcessingState.SUCCESS,
                successMessage: action.payload.message,
                progress: 100
            };
        case UploadActionType.UPLOAD_FAILURE:
            return {
                ...state,
                status: ProcessingState.ERROR,
                successMessage: undefined,
                errors: action.payload.errors
                    ? [...state.errors, ...action.payload.errors]
                    : [...state.errors, { type: 'general', message: action.payload.message }],
                progress: 0
            };
        default:
            return state;
    }
};

/**
 * Helper function to safely parse a string to a number
 * Returns null if the input is undefined, empty, or not a valid number
 */
const safeParseFloat = (value?: string): number | null => {
    if (!value || value.trim() === '') {
        return null;
    }

    // Remove any commas and spaces
    const cleanedValue = value.replace(/[,\s]/g, '');
    const parsed = parseFloat(cleanedValue);

    return isNaN(parsed) ? null : parsed;
};

const UploadTimeSheet: React.FC<UploadTimeSheetProps> = ({ timeSheet: timeSheetRef, readOnly }) => {
    const environment = useRelayEnvironment();

    // Single fragment call - simplified architecture
    // eslint-disable-next-line custom/relay-fragment-dependencies
    const timeSheetData = useFragment(UploadTimeSheetConsolidatedFragment, timeSheetRef);

    // All hooks must be called before any early returns
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isProcessingDialogOpen, setIsProcessingDialogOpen] = useState(false);
    const [state, dispatch] = useReducer(uploadReducer, initialUploadState);

    // Use refs to track processing state and prevent multiple simultaneous operations
    const isProcessingRef = useRef(false);
    const lastProcessedStateRef = useRef<ProcessingState>(ProcessingState.IDLE);

    // Get global error handler from Zustand store
    const setGlobalError = useTimesheetUIStore(state => state.setGlobalError);

    // Create stable callback functions to prevent useEffect infinite loops
    const handleRetrySuccess = useCallback(() => {
        console.log('Upload retry succeeded');
    }, []);

    const handleRetryFailure = useCallback(
        (error: any) => {
            setGlobalError(error);
        },
        [setGlobalError]
    );

    const { recoverFromError } = useErrorRecovery({
        onRetrySuccess: handleRetrySuccess,
        onRetryFailure: handleRetryFailure
    });

    // Create a stable recovery function to prevent useEffect dependencies from changing
    const stableRecoverFromError = useCallback(recoverFromError, [recoverFromError]);

    // Memoize the timesheet data to prevent unnecessary re-renders
    const memoizedTimeSheetData = useMemo(() => {
        if (!timeSheetData) return null;
        return {
            id: timeSheetData.id,
            numericId: timeSheetData.numericId,
            employerGuid: timeSheetData.employerGuid
        };
    }, [timeSheetData]);

    // Handle file selection from the dialog
    const handleFileSelected = useCallback((file: File) => {
        dispatch({ type: UploadActionType.SET_FILE, payload: { file } });
    }, []);

    // Handle file clearing from the dialog
    const handleFileCleared = useCallback(() => {
        dispatch({ type: UploadActionType.CLEAR_FILE });
    }, []);

    // Handle file upload from the dialog
    const handleFileUpload = useCallback(
        async (file: File) => {
            // Set the file in the state
            handleFileSelected(file);

            // Close the upload dialog immediately
            // This ensures the dialog is closed before any async processing starts
            setIsDialogOpen(false);

            try {
                // Parse the CSV file
                await parseCsvFile(file, dispatch, csvColumnMappings);
            } catch (error) {
                // Log error but don't display in UI as the error is already handled by the reducer
                console.error('Error parsing CSV file:', error);
            }
        },
        [handleFileSelected]
    );

    // Effect to manage ProcessingDialog visibility based on state
    useEffect(() => {
        const shouldShowProcessingDialog =
            state.status === ProcessingState.PARSING_FILE ||
            state.status === ProcessingState.VALIDATING_DATA ||
            state.status === ProcessingState.ENRICHING_DATA ||
            state.status === ProcessingState.TRANSFORMING_DATA ||
            state.status === ProcessingState.FINALIZING_DATA ||
            state.status === ProcessingState.ERROR ||
            state.status === ProcessingState.SUCCESS;

        // Use functional updates to avoid circular dependencies
        setIsProcessingDialogOpen((prev) => {
            if (shouldShowProcessingDialog && !prev) {
                // Ensure the upload dialog is closed before showing the processing dialog
                setIsDialogOpen(false);
                return true;
            } else if (!shouldShowProcessingDialog && prev) {
                return false;
            }
            return prev;
        });
    }, [state.status]);

    // Effect to trigger validation after successful parsing
    useEffect(() => {
        // Prevent infinite loops by checking if we've already processed this state
        if (
            state.status === ProcessingState.VALIDATING_DATA &&
            state.data.length > 0 &&
            lastProcessedStateRef.current !== ProcessingState.VALIDATING_DATA &&
            !isProcessingRef.current
        ) {
            // Mark as processing to prevent duplicate operations
            isProcessingRef.current = true;
            lastProcessedStateRef.current = ProcessingState.VALIDATING_DATA;
            // Start validation
            dispatch({ type: UploadActionType.VALIDATE_DATA_START });

            try {
                // Validate the CSV data
                const { validRows, rowErrors } = validateCsvData(state.data);

                if (rowErrors.length > 0) {
                    // If we have row errors, dispatch error action
                    dispatch({
                        type: UploadActionType.VALIDATE_DATA_ERROR,
                        payload: { errors: rowErrors }
                    });
                    // Reset processing flag
                    isProcessingRef.current = false;
                } else {
                    // If validation passes, dispatch success action
                    // Create ProcessedTimesheetEntry objects from the valid rows
                    const processedData = validRows.map((row, index) => {
                        // Convert the date to a standard format
                        const dateStr = row[NormalizedCsvHeader.DATE] || '';

                        return {
                            // Employee identification fields
                            employeeId: null, // Will be populated in enrichment phase
                            employeeName: undefined,
                            externalEmployeeId: row[NormalizedCsvHeader.EXTERNALEMPLOYEEID],
                            ssn: row[NormalizedCsvHeader.SSN],

                            // Classification data
                            agreementId: null, // Will be populated in enrichment phase
                            agreementName: row[NormalizedCsvHeader.AGREEMENT],
                            classificationId: null, // Will be populated in enrichment phase
                            classificationName: row[NormalizedCsvHeader.CLASSIFICATION],
                            subClassificationId: null, // Will be populated in enrichment phase
                            subClassificationName: row[NormalizedCsvHeader.SUBCLASSIFICATION],

                            // Work date information
                            day: row[NormalizedCsvHeader.DAY],
                            workDate: dateStr, // Will be standardized in transformation phase

                            // Financial data (convert strings to numbers or null)
                            hourlyRate: safeParseFloat(row[NormalizedCsvHeader.HOURLYRATE]),
                            jobCode: row[NormalizedCsvHeader.JOBCODE],
                            earningsCode: row[NormalizedCsvHeader.EARNINGSCODE],
                            costCenter: row[NormalizedCsvHeader.COSTCENTER],
                            stHours: safeParseFloat(row[NormalizedCsvHeader.STHOURS]),
                            otHours: safeParseFloat(row[NormalizedCsvHeader.OTHOURS]),
                            dtHours: safeParseFloat(row[NormalizedCsvHeader.DTHOURS]),
                            bonus: safeParseFloat(row[NormalizedCsvHeader.DIRECTPAY]),
                            expenses: safeParseFloat(row[NormalizedCsvHeader.EXPENSES]),

                            // Validation state
                            rowIndex: index,
                            isValid: true, // It passed validation
                            errors: [] // No errors
                        };
                    });

                    dispatch({
                        type: UploadActionType.VALIDATE_DATA_SUCCESS,
                        payload: { processedData }
                    });
                    // Reset processing flag
                    isProcessingRef.current = false;
                }
            } catch (error) {
                console.error('Error validating CSV data:', error);
                dispatch({
                    type: UploadActionType.VALIDATE_DATA_ERROR,
                    payload: {
                        errors: [
                            {
                                type: 'row',
                                rowIndex: -1,
                                message: `Validation error: ${error instanceof Error ? error.message : String(error)}`
                            }
                        ]
                    }
                });
                // Reset processing flag
                isProcessingRef.current = false;
            }
        }
    }, [state.status, state.data]);

    // Effect to trigger data enrichment after successful validation
    useEffect(() => {
        // Prevent infinite loops by checking if we've already processed this state
        if (
            state.status === ProcessingState.ENRICHING_DATA &&
            state.processedData.length > 0 &&
            lastProcessedStateRef.current !== ProcessingState.ENRICHING_DATA &&
            !isProcessingRef.current
        ) {
            // Mark as processing to prevent duplicate operations
            isProcessingRef.current = true;
            lastProcessedStateRef.current = ProcessingState.ENRICHING_DATA;
            // Start enrichment
            dispatch({ type: UploadActionType.ENRICH_DATA_START });

            // Define a function to update progress
            const updateProgress = (progress: number) => {
                dispatch({
                    type: UploadActionType.ENRICH_DATA_PROGRESS,
                    payload: { progress }
                });
            };

            // Enrich the data with API calls
            enrichPayStubData(state.processedData, updateProgress)
                .then(({ enrichedEntries, rowErrors, appliedDefaults }) => {
                    if (rowErrors.length > 0) {
                        // If we have row errors, dispatch error action
                        dispatch({
                            type: UploadActionType.ENRICH_DATA_ERROR,
                            payload: { errors: rowErrors }
                        });
                    } else {
                        // If enrichment succeeds, dispatch success action
                        dispatch({
                            type: UploadActionType.ENRICH_DATA_SUCCESS,
                            payload: { processedData: enrichedEntries, appliedDefaults }
                        });
                    }
                    // Reset processing flag
                    isProcessingRef.current = false;
                })
                .catch((error) => {
                    console.error('Error enriching CSV data:', error);
                    dispatch({
                        type: UploadActionType.ENRICH_DATA_ERROR,
                        payload: {
                            errors: [
                                {
                                    type: 'row',
                                    rowIndex: -1,
                                    message: `Enrichment error: ${error instanceof Error ? error.message : String(error)}`
                                }
                            ]
                        }
                    });
                    // Reset processing flag
                    isProcessingRef.current = false;
                });
        }
    }, [state.status, state.processedData]);

    // Effect to trigger data transformation after successful enrichment
    useEffect(() => {
        // Prevent infinite loops by checking if we've already processed this state
        if (
            state.status === ProcessingState.TRANSFORMING_DATA &&
            state.processedData.length > 0 &&
            lastProcessedStateRef.current !== ProcessingState.TRANSFORMING_DATA &&
            !isProcessingRef.current
        ) {
            // Mark as processing to prevent duplicate operations
            isProcessingRef.current = true;
            lastProcessedStateRef.current = ProcessingState.TRANSFORMING_DATA;
            // Start transformation
            dispatch({ type: UploadActionType.TRANSFORM_DATA_START });

            try {
                // Transform the enriched data into PayStub objects
                // Note: Employee and agreement data is already enriched in processedData from the enrichment phase
                const { payStubs, rowErrors } = transformEnrichedData(state.processedData);

                if (rowErrors.length > 0) {
                    // If we have row errors, dispatch error action
                    dispatch({
                        type: UploadActionType.TRANSFORM_DATA_ERROR,
                        payload: { errors: rowErrors }
                    });
                } else {
                    // If transformation succeeds, dispatch success action
                    dispatch({
                        type: UploadActionType.TRANSFORM_DATA_SUCCESS,
                        payload: { payStubs }
                    });
                }
                // Reset processing flag
                isProcessingRef.current = false;
            } catch (error) {
                console.error('Error transforming CSV data:', error);
                dispatch({
                    type: UploadActionType.TRANSFORM_DATA_ERROR,
                    payload: {
                        errors: [
                            {
                                type: 'row',
                                rowIndex: -1,
                                message: `Transformation error: ${error instanceof Error ? error.message : String(error)}`
                            }
                        ]
                    }
                });
                // Reset processing flag
                isProcessingRef.current = false;
            }
        }
    }, [state.status, state.processedData]);

    // Effect to handle finalizing data and updating Relay store via bulk mutation
    useEffect(() => {
        // Prevent infinite loops by checking if we've already processed this state
        if (
            state.status === ProcessingState.FINALIZING_DATA &&
            state.processedData &&
            state.processedData.length > 0 &&
            lastProcessedStateRef.current !== ProcessingState.FINALIZING_DATA &&
            !isProcessingRef.current
        ) {
            // Mark as processing to prevent duplicate operations
            isProcessingRef.current = true;
            lastProcessedStateRef.current = ProcessingState.FINALIZING_DATA;
            // Start finalization
            dispatch({ type: UploadActionType.FINALIZE_DATA_START });

            const handleUploadComplete = async () => {
                try {
                    // Convert to Relay input format
                    // Note: Employee and agreement data is already enriched in processedData from the enrichment phase
                    const relayPayStubs = convertEnrichedToRelayPayStubInput(state.processedData);

                    // If we have no pay stubs to create, show a message
                    if (relayPayStubs.length === 0) {
                        // Special case for empty CSV files
                        if (state.rowCount === 0) {
                            dispatch({
                                type: UploadActionType.FINALIZE_DATA_SUCCESS
                            });
                        } else {
                            dispatch({
                                type: UploadActionType.UPLOAD_SUCCESS,
                                payload: { message: 'No valid pay stubs to upload.' }
                            });
                        }
                        return;
                    }

                    // Use bulk mutation instead of individual operations
                    // Add null safety check before accessing properties
                    if (!memoizedTimeSheetData) {
                        throw new Error('Timesheet data is not available');
                    }

                    await bulkAddPayStubs(environment, {
                        timeSheetId: memoizedTimeSheetData.id,
                        numericId: memoizedTimeSheetData.numericId,
                        employerGuid: memoizedTimeSheetData.employerGuid,
                        payStubs: relayPayStubs
                    });

                    // Dispatch success action with count of pay stubs added
                    dispatch({
                        type: UploadActionType.UPLOAD_SUCCESS,
                        payload: {
                            message: `Successfully uploaded ${relayPayStubs.length} pay stubs to the timesheet.`
                        }
                    });
                    // Reset processing flag
                    isProcessingRef.current = false;
                } catch (error) {
                    const timesheetError = new TimesheetError({
                        type: ErrorType.MUTATION,
                        message: error instanceof Error ? error.message : 'Upload failed',
                        userMessage: 'Failed to upload timesheet data. Please try again.',
                        technical: `Upload error: ${error}`,
                        suggestions: [
                            'Check your data format',
                            'Verify employee and agreement data',
                            'Try uploading a smaller file',
                            'Contact support if the issue persists'
                        ],
                        canRetry: true,
                        context: { uploadData: state.processedData }
                    });

                    dispatch({
                        type: UploadActionType.UPLOAD_FAILURE,
                        payload: { message: timesheetError.message }
                    });

                    // Reset processing flag
                    isProcessingRef.current = false;

                    // Attempt automatic recovery
                    stableRecoverFromError(timesheetError, () => handleUploadComplete(), 'upload-operation');
                }
            };

            handleUploadComplete();
        }
    }, [state.status, state.processedData, state.rowCount, environment, memoizedTimeSheetData, stableRecoverFromError]);

    // Ensure we don't show both dialogs at the same time
    // This helps prevent race conditions in the tests
    const prevProcessingDialogOpen = useRef(isProcessingDialogOpen);
    useEffect(() => {
        // Only close upload dialog when processing dialog opens (not when it closes)
        if (isProcessingDialogOpen && !prevProcessingDialogOpen.current) {
            // Use functional update to avoid dependency on setIsDialogOpen
            setIsDialogOpen(() => false);
        }
        prevProcessingDialogOpen.current = isProcessingDialogOpen;
    }, [isProcessingDialogOpen]);

    // Handle closing the processing dialog
    const handleCloseProcessingDialog = useCallback(() => {
        setIsProcessingDialogOpen(false);
        dispatch({ type: UploadActionType.RESET });
        // Reset refs to ensure clean state for next upload
        isProcessingRef.current = false;
        lastProcessedStateRef.current = ProcessingState.IDLE;
    }, []);

    const openUploadDialog = useCallback(() => setIsDialogOpen(true), []);
    const closeUploadDialog = useCallback(() => setIsDialogOpen(false), []);

    // Add null safety check after all hooks
    if (!timeSheetData || !memoizedTimeSheetData) {
        return (
            <div>
                <Text>Upload Time Sheet</Text>
                <Text>No timesheet data available</Text>
            </div>
        );
    }

    return (
        <>
            <Button variant="secondary" isDisabled={readOnly} onPress={openUploadDialog}>
                <Upload />
                <Text>Upload</Text>
            </Button>

            {isDialogOpen && (
                <DialogContainer onDismiss={closeUploadDialog}>
                    <UploadDialog
                        onClose={closeUploadDialog}
                        onUpload={handleFileUpload}
                        onFileSelected={handleFileSelected}
                        onFileCleared={handleFileCleared}
                        selectedFile={state.file}
                    />
                </DialogContainer>
            )}

            {/* Processing Dialog */}
            <ProcessingDialog
                isOpen={isProcessingDialogOpen}
                onClose={handleCloseProcessingDialog}
                status={state.status}
                errors={state.errors}
                progress={state.progress}
                rowCount={state.rowCount}
                fileName={state.file?.name}
                dispatch={dispatch}
                successMessage={state.successMessage}
            />
        </>
    );
};

export default UploadTimeSheet;
