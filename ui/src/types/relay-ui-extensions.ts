/**
 * UI Extension Types for Relay Generated Types
 *
 * This file creates bridge types that combine Relay-generated GraphQL types
 * with UI-specific state fields. This replaces local interface duplications
 * while maintaining backward compatibility during migration.
 *
 * Following RELAY-RULES.md: "Generated Relay types MUST be used"
 * Following RELAY-PITFALLS.md: Avoid type casting and use proper fragment composition
 */

// Import Relay-generated types
import type {
    TimeSheetDetailRow_payStubDetail$data,
    TimeSheetDetailRow_payStubDetail$key
} from '@/relay/__generated__/TimeSheetDetailRow_payStubDetail.graphql';

import type { PayStubTable_payStub$data, PayStubTable_payStub$key } from '@/relay/__generated__/PayStubTable_payStub.graphql';

import type {
    TimesheetDetail_timeSheet$data,
    TimesheetDetail_timeSheet$key
} from '@/relay/__generated__/TimesheetDetail_timeSheet.graphql';

import type {
    fragments_AgreementBasicInfo$data,
    fragments_AgreementBasicInfo$key
} from '@/relay/__generated__/fragments_AgreementBasicInfo.graphql';

import type {
    fragments_ClassificationBasicInfo$data,
    fragments_ClassificationBasicInfo$key
} from '@/relay/__generated__/fragments_ClassificationBasicInfo.graphql';

import type {
    fragments_SubClassificationBasicInfo$data,
    fragments_SubClassificationBasicInfo$key
} from '@/relay/__generated__/fragments_SubClassificationBasicInfo.graphql';

import type {
    TimeSheetDetailRow_employee$data,
    TimeSheetDetailRow_employee$key
} from '@/relay/__generated__/TimeSheetDetailRow_employee.graphql';

import type {
    EmployeeSelector_employee$data,
    EmployeeSelector_employee$key
} from '@/relay/__generated__/EmployeeSelector_employee.graphql';

import type { EmployeeDataFragment$data, EmployeeDataFragment$key } from '@/relay/__generated__/EmployeeDataFragment.graphql';

// =============================================================================
// CORE RELAY TYPE EXPORTS (Clean interface)
// =============================================================================

/**
 * Core Relay types exported with consistent naming
 * These should be used as the base for all data operations
 */
export type RelayPayStubDetail = TimeSheetDetailRow_payStubDetail$data;
export type RelayPayStubDetailRef = TimeSheetDetailRow_payStubDetail$key;

export type RelayPayStub = PayStubTable_payStub$data;
export type RelayPayStubRef = PayStubTable_payStub$key;

export type RelayTimeSheet = TimesheetDetail_timeSheet$data;
export type RelayTimeSheetRef = TimesheetDetail_timeSheet$key;

// Employee Types - Multiple fragments available for different use cases
export type RelayEmployee = EmployeeSelector_employee$data; // Most common usage - selector/picker components
export type RelayEmployeeRef = EmployeeSelector_employee$key;

export type RelayEmployeeDetailed = TimeSheetDetailRow_employee$data; // Detailed employee data for timesheet rows
export type RelayEmployeeDetailedRef = TimeSheetDetailRow_employee$key;

export type RelayEmployeeData = EmployeeDataFragment$data; // Full employee data collection with agreements
export type RelayEmployeeDataRef = EmployeeDataFragment$key;

export type RelayAgreement = fragments_AgreementBasicInfo$data;
export type RelayAgreementRef = fragments_AgreementBasicInfo$key;

export type RelayClassification = fragments_ClassificationBasicInfo$data;
export type RelayClassificationRef = fragments_ClassificationBasicInfo$key;

export type RelaySubClassification = fragments_SubClassificationBasicInfo$data;
export type RelaySubClassificationRef = fragments_SubClassificationBasicInfo$key;

// =============================================================================
// UI EXTENSION TYPES
// =============================================================================

/**
 * PayStubDetail with UI state extensions
 * Combines Relay data with UI-only state fields for frontend operations
 */
export interface PayStubDetailUI extends RelayPayStubDetail {
    // UI state fields (not in Relay/GraphQL)
    delete?: boolean; // UI flag: marked for deletion
    inError?: boolean; // UI flag: validation error
    isTemporary?: boolean; // UI flag: placeholder row

    // Backward compatibility helpers (computed/derived)
    name?: string; // Day name (computed from workDate)
    totalHours?: number | null; // Computed from stHours + otHours + dtHours
    reportLineItemId?: number | null; // May not be in fragment, kept for compatibility

    // Employee reference (may be computed from parent PayStub)
    employeeId?: string | number | null;
}

/**
 * PayStub with UI state extensions
 * Combines Relay data with UI-only state fields
 */
export interface PayStubUI extends Omit<RelayPayStub, 'details' | 'employee'> {
    // Override details to use UI extension type
    details: ReadonlyArray<PayStubDetailUI>;

    // Keep the original employee object from Relay
    employee: RelayPayStub['employee'];

    // UI state fields (not in Relay/GraphQL)
    expanded?: boolean; // UI flag: detail view expanded
    delete?: boolean; // UI flag: marked for deletion
    inError?: boolean; // UI flag: validation error

    // Computed aggregation fields (may not be in Relay fragment)
    stHours?: number | null; // Aggregated from details
    otHours?: number | null; // Aggregated from details
    dtHours?: number | null; // Aggregated from details
    bonus?: number | null; // Aggregated from details
    expenses?: number | null; // Aggregated from details

    // Backward compatibility - computed display text
    employeeDisplayName?: string; // Display text "LastName, FirstName" (computed)
}

/**
 * TimeSheet with UI state extensions
 */
export interface TimeSheetUI extends Omit<RelayTimeSheet, 'payStubs'> {
    // Override payStubs structure if needed for UI
    payStubs?: {
        edges?: ReadonlyArray<{
            node: PayStubUI;
        }>;
    } | null;
}

/**
 * Agreement with UI compatibility layer
 * Maps Relay's id/name structure to legacy value/text structure
 */
export interface AgreementUI {
    id: string;
    name: string;
    // Computed compatibility fields
    value: string | number; // Maps to id
    text: string; // Maps to name
}

/**
 * Classification with UI compatibility layer
 */
export interface ClassificationUI {
    id: string;
    name: string;
    // Computed compatibility fields
    value: string | number; // Maps to id
    text: string; // Maps to name
}

/**
 * SubClassification with UI compatibility layer
 */
export interface SubClassificationUI {
    id: string;
    name: string;
    // Computed compatibility fields
    value: string | number; // Maps to id
    text: string; // Maps to name
}

/**
 * Employee with UI compatibility layer
 * Plain interface compatible with both Relay data and API responses
 */
export interface EmployeeUI {
    id: string;
    firstName?: string | null | undefined; // Optional for backward compatibility
    lastName?: string | null | undefined; // Optional for backward compatibility
    middleName?: string | null | undefined;
    active?: boolean | null | undefined;

    // Computed compatibility fields
    value: string | number; // Maps to id
    text: string; // Computed "LastName, FirstName" format

    // Additional fields that may be available from other fragments
    externalEmployeeId?: string | null;
    SSN?: string | null;
    ssn?: string | string[] | null; // Support both formats - GraphQL may return array
    settings?: Record<string, unknown> | null; // Employee-specific defaults
    dateOfHire?: string | null;
    dateOfTermination?: string | null;
    suffix?: string | null;
}

/**
 * Employee data extracted from EmployeeDataFragment - for data provider contexts
 */
export interface EmployeeDataUI {
    employees: ReadonlyArray<EmployeeUI>;
    agreements: ReadonlyArray<AgreementUI>;
    totalCount: number;
}

// =============================================================================
// TYPE TRANSFORMATION UTILITIES
// =============================================================================

/**
 * Transforms Relay Agreement to UI-compatible Agreement
 */
export function transformAgreementToUI(agreement: RelayAgreement): AgreementUI {
    return {
        ...agreement,
        value: agreement.id,
        text: agreement.name
    };
}

/**
 * Transforms Relay Classification to UI-compatible Classification
 */
export function transformClassificationToUI(classification: RelayClassification): ClassificationUI {
    return {
        ...classification,
        value: classification.id,
        text: classification.name
    };
}

/**
 * Transforms Relay SubClassification to UI-compatible SubClassification
 */
export function transformSubClassificationToUI(subClassification: RelaySubClassification): SubClassificationUI {
    return {
        ...subClassification,
        value: subClassification.id,
        text: subClassification.name
    };
}

/**
 * Transforms Relay Employee to UI-compatible Employee
 * Works with EmployeeSelector_employee fragment
 */
export function transformEmployeeToUI(employee: RelayEmployee): EmployeeUI {
    const displayName =
        employee.lastName && employee.firstName
            ? `${employee.lastName}, ${employee.firstName}`
            : employee.lastName || employee.firstName || 'Unknown';

    return {
        ...employee,
        value: employee.id,
        text: displayName
    };
}

/**
 * Transforms employee data from EmployeeDataFragment to UI format
 * Handles the nested structure from the data fragment
 */
export function transformEmployeeDataToUI(data: RelayEmployeeData): EmployeeDataUI {
    const employees: EmployeeUI[] =
        data.employeesByEmployerGuidAsync?.edges?.map((edge) => {
            const emp = edge.node;
            const displayName =
                emp.lastName && emp.firstName ? `${emp.lastName}, ${emp.firstName}` : emp.lastName || emp.firstName || 'Unknown Employee';

            // Handle SSN field - convert readonly array to mutable array if needed
            const ssnValue: string | string[] | null | undefined = emp.ssn
                ? Array.isArray(emp.ssn)
                    ? ([...emp.ssn] as string[])
                    : (emp.ssn as unknown as string)
                : null;

            return {
                id: emp.id,
                firstName: emp.firstName,
                lastName: emp.lastName,
                middleName: emp.middleName,
                active: emp.active,
                value: emp.id,
                text: displayName,
                externalEmployeeId: emp.externalEmployeeId,
                ssn: ssnValue,
                dateOfHire: emp.dateOfHire,
                dateOfTermination: emp.dateOfTermination,
                suffix: emp.suffix
            };
        }) || [];

    const agreements: AgreementUI[] =
        data.signatoryAgreements?.nodes?.map((agreement) => ({
            id: agreement.id,
            name: agreement.name,
            value: agreement.id,
            text: agreement.name
        })) || [];

    return {
        employees,
        agreements,
        totalCount: data.employeesByEmployerGuidAsync?.totalCount || 0
    };
}

/**
 * Creates an EmployeeUI from any employee-like object
 * Useful for processing various employee data structures
 */
export function createEmployeeUI(employeeData: {
    id: string;
    firstName?: string | null;
    lastName?: string | null;
    middleName?: string | null;
    active?: boolean | null;
    externalEmployeeId?: string | null;
    [key: string]: unknown;
}): EmployeeUI {
    const displayName =
        employeeData.lastName && employeeData.firstName
            ? `${employeeData.lastName}, ${employeeData.firstName}`
            : employeeData.lastName || employeeData.firstName || 'Unknown Employee';

    return {
        id: employeeData.id,
        firstName: employeeData.firstName || null,
        lastName: employeeData.lastName || '',
        middleName: employeeData.middleName,
        active: employeeData.active,
        value: employeeData.id,
        text: displayName,
        externalEmployeeId: employeeData.externalEmployeeId
    };
}

/**
 * Transforms Relay PayStubDetail to UI-compatible PayStubDetail
 */
export function transformPayStubDetailToUI(
    detail: RelayPayStubDetail,
    uiState?: {
        delete?: boolean;
        inError?: boolean;
        isTemporary?: boolean;
        employeeId?: string | number | null;
    }
): PayStubDetailUI {
    // Compute totalHours if not provided
    const totalHours = (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0);

    // Compute day name from workDate (basic implementation)
    const workDate = detail.workDate ? new Date(detail.workDate as string) : null;
    const dayName = workDate ? workDate.toLocaleDateString('en-US', { weekday: 'long' }) : '';

    return {
        ...detail,
        totalHours,
        name: dayName,
        ...uiState
    };
}

/**
 * Transforms Relay PayStub to UI-compatible PayStub
 */
export function transformPayStubToUI(
    payStub: RelayPayStub,
    uiState?: {
        expanded?: boolean;
        delete?: boolean;
        inError?: boolean;
    }
): PayStubUI {
    // Transform details - cast is safe since details are resolved data in PayStubTable fragment
    const detailsUI = payStub.details.map((detail) =>
        transformPayStubDetailToUI(detail as unknown as RelayPayStubDetail, { employeeId: payStub.employeeId })
    );

    // Compute aggregated values
    const stHours = detailsUI.reduce((sum, detail) => sum + (Number(detail.stHours) || 0), 0);
    const otHours = detailsUI.reduce((sum, detail) => sum + (Number(detail.otHours) || 0), 0);
    const dtHours = detailsUI.reduce((sum, detail) => sum + (Number(detail.dtHours) || 0), 0);
    const bonus = detailsUI.reduce((sum, detail) => sum + (Number(detail.bonus) || 0), 0);
    const expenses = detailsUI.reduce((sum, detail) => sum + (Number(detail.expenses) || 0), 0);

    // Employee display name is now handled by EmployeeNameDisplay component
    // which resolves the employee fragment reference properly
    const employeeDisplayName = 'Unknown Employee'; // Fallback - actual name resolved in UI components

    return {
        ...payStub,
        details: detailsUI,
        stHours,
        otHours,
        dtHours,
        bonus,
        expenses,
        employeeDisplayName,
        ...uiState
    };
}

// =============================================================================
// BACKWARD COMPATIBILITY EXPORTS
// =============================================================================

/**
 * @deprecated Use RelayPayStubDetail and PayStubDetailUI instead
 * Temporary backward compatibility export
 */
export type PayStubDetail = PayStubDetailUI;

/**
 * @deprecated Use RelayPayStub and PayStubUI instead
 * Temporary backward compatibility export
 */
export type PayStub = PayStubUI;

/**
 * @deprecated Use RelayAgreement and AgreementUI instead
 * Temporary backward compatibility export
 */
export type Agreement = AgreementUI;

/**
 * @deprecated Use RelayClassification and ClassificationUI instead
 * Temporary backward compatibility export
 */
export type Classification = ClassificationUI;

/**
 * @deprecated Use RelaySubClassification and SubClassificationUI instead
 * Temporary backward compatibility export
 */
export type SubClassification = SubClassificationUI;

/**
 * @deprecated Use RelayEmployee and EmployeeUI instead
 * Temporary backward compatibility export
 */
export type Employee = EmployeeUI;

/**
 * @deprecated EarningsCode interface for backward compatibility
 * This might be better moved to constants since earnings codes are typically static data
 */
export interface EarningsCode {
    value: string;
    text: string;
}

// =============================================================================
// TYPE GUARDS
// =============================================================================

/**
 * Type guard for PayStubDetailUI
 */
export function isPayStubDetailUI(obj: unknown): obj is PayStubDetailUI {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        'workDate' in obj &&
        typeof obj.id !== 'undefined' &&
        typeof obj.workDate !== 'undefined'
    );
}

/**
 * Type guard for PayStubUI
 */
export function isPayStubUI(obj: unknown): obj is PayStubUI {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        'details' in obj &&
        typeof obj.id !== 'undefined' &&
        Array.isArray(obj.details)
    );
}

/**
 * Type guard for RelayPayStubDetail
 */
export function isRelayPayStubDetail(obj: unknown): obj is RelayPayStubDetail {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        'workDate' in obj &&
        typeof obj.id !== 'undefined' &&
        typeof obj.workDate !== 'undefined'
    );
}

/**
 * Type guard for RelayPayStub
 */
export function isRelayPayStub(obj: unknown): obj is RelayPayStub {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        'id' in obj &&
        'details' in obj &&
        typeof obj.id !== 'undefined' &&
        Array.isArray(obj.details)
    );
}
