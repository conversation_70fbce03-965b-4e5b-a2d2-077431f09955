/**
 * Schema-Aware Flat Draft Types
 * 
 * This module provides TypeScript interfaces that automatically sync with
 * GraphQL schema changes via Relay's type generation system.
 * 
 * Features:
 * - Automatic schema synchronization via Relay-generated types
 * - Flat structure for optimal performance and simplicity
 * - UI-field namespacing with _ui prefix to prevent schema collisions
 * - Runtime validation with structural type guards
 * - Single source of truth: GraphQL schema drives all type definitions
 * 
 * Red-Team Recommendations Implemented:
 * - Writable helper to strip readonly modifiers
 * - Clean helper to remove Relay fragment properties
 * - UI-field namespace with _ui prefix
 * - Structural runtime validation
 * - Strict field reference via DETAIL_COLUMN_MAP
 */

// Import all relevant Relay-generated fragment types
import type { TimeSheetDetailRow_payStubDetail$data } from '@/relay/__generated__/TimeSheetDetailRow_payStubDetail.graphql';
import type { PayStubTable_payStub$data } from '@/relay/__generated__/PayStubTable_payStub.graphql';
import { sanitizeNumericInput, sanitizeUserInput, validateForSQLInjection } from '../utils/securityUtils';

// =============================================================================
// TYPE TRANSFORMATION UTILITIES
// =============================================================================

/**
 * Red-Team Recommendation: Helper to strip readonly modifiers
 */
type Writable<T> = { -readonly [K in keyof T]: T[K] };

/**
 * Red-Team Recommendation: Remove Relay fragment helper properties
 */
type RelayFragmentHelpers = {
  readonly " $fragmentType": string;
  readonly " $fragmentSpreads": any;
  readonly " $data"?: any;
  readonly " $refType"?: any;
};

/**
 * Clean type by removing readonly and Relay helpers
 */
type Clean<T> = Writable<Omit<T, keyof RelayFragmentHelpers>>;

/**
 * Flatten GraphQL type for draft usage
 */
type FlattenGraphQLType<T> = {
  [K in keyof T]?: T[K];
};

/**
 * Extract pure data type from GraphQL fragment
 */
type GraphQLDataOnly<T> = T extends { readonly [key: string]: any } 
  ? Clean<T>
  : never;

// =============================================================================
// UI-SPECIFIC FIELDS (Red-Team: _ui prefix namespace)
// =============================================================================

/**
 * Base UI fields that don't exist in GraphQL schema
 * Red-Team Recommendation: _ui prefix to prevent schema collisions
 */
export interface BaseUIFields {
  /** Deletion state - prefixed to prevent schema conflicts */
  _uiDelete?: boolean;
  
  /** Temporary row state - for newly added rows */
  _uiIsTemporary?: boolean;
  
  /** Validation state */
  _uiValidationErrors?: string[];
  
  /** Draft metadata */
  _uiLastModified?: number;
  _uiModifiedBy?: string;
  _uiDraftId?: string;
}

/**
 * Detail-specific UI fields
 */
export interface DetailUIFields extends BaseUIFields {
  /** Cell-level editing state */
  _uiEditingCells?: Set<string>;
  
  /** Auto-fill state */
  _uiIsAutoFilled?: boolean;
  _uiAutoFillSource?: string;
}

/**
 * PayStub-specific UI fields
 */
export interface PayStubUIFields extends BaseUIFields {
  /** Expansion state */
  _uiExpanded?: boolean;
  
  /** Selection state */
  _uiSelected?: boolean;
  
  /** Editing mode */
  _uiEditingMode?: 'view' | 'edit' | 'add';
}

// =============================================================================
// SCHEMA-AWARE FLAT DRAFT TYPES
// =============================================================================

/**
 * Schema-aware flat draft type for PayStub details
 * Automatically syncs with GraphQL schema changes
 */
export type FlatPayStubDetailDraft = 
  FlattenGraphQLType<GraphQLDataOnly<TimeSheetDetailRow_payStubDetail$data>> 
  & DetailUIFields;

/**
 * Schema-aware flat draft type for PayStub
 * Automatically syncs with GraphQL schema changes
 * Note: Excludes details array - only PayStub-level fields
 * 
 * IMPORTANT: totalHours field (if present) is CLIENT-ONLY computed value.
 * It should NOT be included in GraphQL mutations as it's calculated server-side
 * from detail records.
 */
export type FlatPayStubDraft = 
  FlattenGraphQLType<Omit<GraphQLDataOnly<PayStubTable_payStub$data>, 'details' | 'employee'>> 
  & PayStubUIFields;

/**
 * Extended PayStub draft type that supports header-level hours for the header-only workflow.
 * This type includes fields that may not yet be exposed in the current GraphQL fragment
 * but are supported by the backend mutation inputs.
 * 
 * TODO: When PayStubTable_payStub fragment is updated to include header-level hour fields,
 * this type can be removed and FlatPayStubDraft can be used directly.
 */
export type ExtendedFlatPayStubDraft = FlatPayStubDraft & {
  // Header-level hour fields for header-only PayStub workflow
  stHours?: number | null;
  otHours?: number | null;
  dtHours?: number | null;
  bonus?: number | null;
  expenses?: number | null;
};

// =============================================================================
// RUNTIME VALIDATION (Red-Team Recommendations)
// =============================================================================

/**
 * Red-Team Recommendation: Structural validation instead of placeholder
 * Validates that object contains only flat fields (no nested objects)
 * Enhanced with security validation
 */
export function isFlatPayStubDetailDraft(obj: unknown): obj is FlatPayStubDetailDraft {
  if (!obj || typeof obj !== 'object') return false;
  
  const candidate = obj as Record<string, unknown>;
  
  // Validate object structure and security
  for (const [key, value] of Object.entries(candidate)) {
    // Ensure key is valid
    if (!key || typeof key !== 'string') {
      console.warn('[SECURITY] Invalid key in flat draft:', key);
      return false;
    }
    
    // Check for prototype pollution attempts
    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
      console.warn('[SECURITY] Prototype pollution attempt detected:', key);
      return false;
    }
    
    // Ensure all values are primitives, arrays, or null/undefined (no nested objects)
    if (value !== null && value !== undefined && typeof value === 'object' && !Array.isArray(value)) {
      return false;
    }
    
    // Validate string values for security
    if (typeof value === 'string' && !validateForSQLInjection(value)) {
      console.warn('[SECURITY] Potential injection attempt in draft field:', key);
      return false;
    }
  }
  
  return true;
}

/**
 * Structural validation for PayStub drafts
 */
export function isFlatPayStubDraft(obj: unknown): obj is FlatPayStubDraft {
  if (!obj || typeof obj !== 'object') return false;
  
  // Ensure all values are primitives, arrays, or null/undefined (no nested objects)
  return Object.values(obj).every(v => 
    v === null || 
    v === undefined || 
    typeof v !== 'object' || 
    Array.isArray(v)
  );
}

/**
 * Structural validation for extended PayStub drafts (includes header-level hours)
 */
export function isExtendedFlatPayStubDraft(obj: unknown): obj is ExtendedFlatPayStubDraft {
  if (!obj || typeof obj !== 'object') return false;
  
  // Ensure all values are primitives, arrays, or null/undefined (no nested objects)
  return Object.values(obj).every(v => 
    v === null || 
    v === undefined || 
    typeof v !== 'object' || 
    Array.isArray(v)
  );
}

// =============================================================================
// UTILITY TYPES AND FIELD MAPPING
// =============================================================================

/**
 * Utility types for common operations
 */
export type PayStubDetailDraftKeys = keyof FlatPayStubDetailDraft;
export type PayStubDraftKeys = keyof FlatPayStubDraft;

/**
 * Non-UI field keys for type-safe column mapping
 * Excludes UI fields (those starting with _ui)
 */
export type PayStubDetailDataKeys = Exclude<PayStubDetailDraftKeys, keyof DetailUIFields>;

/**
 * Red-Team Recommendation: Column mapping using strict field reference
 * This ensures schema changes surface immediately in components
 * Enhanced with type safety to catch schema mismatches at compile time
 */
export const DETAIL_COLUMN_MAP = {
  'stHours': 'stHours',
  'otHours': 'otHours', 
  'dtHours': 'dtHours',
  'bonus': 'bonus',
  'expenses': 'expenses',
  'agreementId': 'agreementId',
  'classificationId': 'classificationId',
  'subClassificationId': 'subClassificationId',
  'hourlyRate': 'hourlyRate',
  'jobCode': 'jobCode',
  'costCenter': 'costCenter',
  'earningsCode': 'earningsCode',
  'workDate': 'workDate',
  'payStubId': 'payStubId',
  'id': 'id',
} as const satisfies Record<PayStubDetailDataKeys, PayStubDetailDataKeys>;

/**
 * UI-only field mapping (fields with _ui prefix)
 * Enhanced with type safety to catch field name changes at compile time
 */
export const UI_FIELD_MAP = {
  'delete': '_uiDelete',
  'isTemporary': '_uiIsTemporary',
  'validationErrors': '_uiValidationErrors',
  'lastModified': '_uiLastModified',
  'modifiedBy': '_uiModifiedBy',
  'draftId': '_uiDraftId',
  'editingCells': '_uiEditingCells',
  'isAutoFilled': '_uiIsAutoFilled',
  'autoFillSource': '_uiAutoFillSource',
} as const;

// Type to ensure UI_FIELD_MAP values are valid PayStubDetailDraftKeys
type ValidateUIFieldMap = typeof UI_FIELD_MAP[keyof typeof UI_FIELD_MAP] extends PayStubDetailDraftKeys ? true : never;
const _uiFieldMapValidation: ValidateUIFieldMap = true;

/**
 * Type-safe column map access
 * Ensures only valid field names can be used and returns the correct type
 */
export type DetailColumnMapKeys = keyof typeof DETAIL_COLUMN_MAP;
export type UIFieldMapKeys = keyof typeof UI_FIELD_MAP;

/**
 * Type-safe field name validation function
 * Throws compile-time error if field name doesn't exist in the schema
 */
export function getValidatedFieldName<T extends DetailColumnMapKeys>(fieldName: T): PayStubDetailDataKeys {
  return DETAIL_COLUMN_MAP[fieldName];
}

/**
 * Type-safe UI field name validation function
 */
export function getValidatedUIFieldName<T extends UIFieldMapKeys>(fieldName: T): keyof DetailUIFields {
  return UI_FIELD_MAP[fieldName];
}

// =============================================================================
// VALIDATION AND HELPERS
// =============================================================================

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
  warningMessage?: string;
}

/**
 * Validate a specific field in a flat draft
 * Red-Team Recommendation: Reference DETAIL_COLUMN_MAP for validation
 * Enhanced with comprehensive security validation
 */
export function validateFlatDraftField(
  field: PayStubDetailDraftKeys, 
  value: unknown
): ValidationResult {
  // Sanitize field name for security
  const sanitizedField = sanitizeUserInput(String(field));
  if (!sanitizedField) {
    return {
      isValid: false,
      errorMessage: 'Invalid field name provided'
    };
  }
  
  // Check if field exists in our column mapping
  const isDataField = Object.values(DETAIL_COLUMN_MAP).includes(field as PayStubDetailDataKeys);
  const isUIField = Object.values(UI_FIELD_MAP).includes(field as keyof DetailUIFields);
  
  if (!isDataField && !isUIField) {
    return {
      isValid: false,
      errorMessage: `Unknown field: ${sanitizedField}. Field must exist in DETAIL_COLUMN_MAP or UI_FIELD_MAP.`
    };
  }
  
  // Enhanced validation with security checks
  switch (field) {
    case 'stHours':
    case 'otHours':
    case 'dtHours':
    case 'hourlyRate':
    case 'bonus':
    case 'expenses':
      if (value !== null && value !== undefined) {
        const sanitizedNum = sanitizeNumericInput(value);
        if (sanitizedNum === null) {
          return { isValid: false, errorMessage: `${sanitizedField} must be a valid number` };
        }
        if (sanitizedNum < 0) {
          return { isValid: false, errorMessage: `${sanitizedField} cannot be negative` };
        }
        // Check for realistic maximum values
        if (sanitizedNum > 1000000) {
          return { isValid: false, errorMessage: `${sanitizedField} value is unrealistically large` };
        }
      }
      break;
      
    case 'agreementId':
    case 'classificationId':
    case 'subClassificationId':
      if (value !== null && value !== undefined) {
        const sanitizedNum = sanitizeNumericInput(value);
        if (sanitizedNum === null || !Number.isInteger(sanitizedNum)) {
          return { isValid: false, errorMessage: `${sanitizedField} must be a valid integer` };
        }
        if (sanitizedNum < 0 || sanitizedNum > Number.MAX_SAFE_INTEGER) {
          return { isValid: false, errorMessage: `${sanitizedField} must be a positive integer within safe bounds` };
        }
      }
      break;
      
    case 'jobCode':
    case 'costCenter':
    case 'earningsCode':
      if (value !== null && value !== undefined) {
        if (typeof value !== 'string') {
          return { isValid: false, errorMessage: `${sanitizedField} must be a string` };
        }
        const sanitizedValue = sanitizeUserInput(value);
        if (!validateForSQLInjection(sanitizedValue)) {
          return { isValid: false, errorMessage: `${sanitizedField} contains invalid characters` };
        }
        if (sanitizedValue.length > 100) {
          return { isValid: false, errorMessage: `${sanitizedField} is too long (max 100 characters)` };
        }
      }
      break;
      
    case '_uiDelete':
    case '_uiIsTemporary':
    case '_uiIsAutoFilled':
      if (value !== null && value !== undefined && typeof value !== 'boolean') {
        return { isValid: false, errorMessage: `${sanitizedField} must be a boolean` };
      }
      break;
      
    case '_uiValidationErrors':
      if (value !== null && value !== undefined) {
        if (!Array.isArray(value)) {
          return { isValid: false, errorMessage: `${sanitizedField} must be an array` };
        }
        // Validate array contents
        if (value.some(item => typeof item !== 'string')) {
          return { isValid: false, errorMessage: `${sanitizedField} must contain only strings` };
        }
        if (value.length > 100) {
          return { isValid: false, errorMessage: `${sanitizedField} array is too large` };
        }
      }
      break;
      
    case 'payStubId':
    case 'id':
      if (value !== null && value !== undefined) {
        if (typeof value !== 'string') {
          return { isValid: false, errorMessage: `${sanitizedField} must be a string` };
        }
        const sanitizedValue = sanitizeUserInput(value);
        if (!validateForSQLInjection(sanitizedValue)) {
          return { isValid: false, errorMessage: `${sanitizedField} contains invalid characters` };
        }
        if (sanitizedValue.length > 255) {
          return { isValid: false, errorMessage: `${sanitizedField} is too long (max 255 characters)` };
        }
      }
      break;
  }
  
  return { isValid: true };
}

/**
 * Development-only assertion to reject nested objects in drafts
 * Red-Team Recommendation: Add dev-only assertion in store writer
 */
export function assertFlatDraft(draft: unknown, context: string): void {
  if (process.env.NODE_ENV === 'development') {
    if (!isFlatPayStubDetailDraft(draft)) {
      console.error(`[SCHEMA-AWARE-TYPES] Invalid flat draft detected in ${context}:`, draft);
      console.trace('Stack trace for nested object in flat draft:');
      throw new Error(`Flat draft validation failed in ${context}. Draft contains nested objects.`);
    }
  }
}

// All exports are already done inline above - no need to re-export