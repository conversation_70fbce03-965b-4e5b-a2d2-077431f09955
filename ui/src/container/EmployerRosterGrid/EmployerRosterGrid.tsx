import uuid4 from 'uuid4';
import { Flex } from '@adobe/react-spectrum';
import { useLazyLoadQuery } from 'react-relay';
import { useSWRQuery } from '@/src/relay/useSWRQuery';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import set from 'lodash/set';
import { ToastQueue } from '@react-spectrum/toast';
import { SavedCustomView } from '@/src/types/views';
import { RecordSourceSelectorProxy } from 'relay-runtime';
import { getCurrentViewUIFromSelectedView } from './helper';
import { graphql, useFragment, useMutation } from 'react-relay/hooks';
import { EmployerRosterTable } from '@/src/components/EmployerRoster';
import { EmployerRosterHeader } from '@/src/components/EmployerRoster';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { getDefaultEmployerRosterQueryVariables } from '@/src/services/employer-roster';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import { EMPLOYER_ROSTER_DATABASE_CUSTOM_VIEW_NAME } from '@/src/constants/employer-roster';
import { getFilterByColumnData, getSortOrderByColumnData } from '@/src/services/employer-roster';
import { getViewById, getUpdatedView, getDecodedDefaultViewId, SavedView, FilterConfig } from '@/src/services/saved-views';
import { EmployerRosterViews, EmployerRosterViewsOptions, ToastMessages } from '@/src/constants/views';
import { getDeletedViews, getDecodedCustomSavedViews, getAddedViews } from '@/src/services/saved-views';
import { exportRosterView, getDefaultBenefitElectionsQueryVariables } from '@/src/services/employer-roster';
import { EmployerRosterGridQuery$variables } from '@/relay/__generated__/EmployerRosterGridQuery.graphql';
import { employerRosterColumnData } from '@/src/components/EmployerRoster/EmployerRosterTable/EPRTable.data';
import { EmployerRosterViewSortInput } from '@/relay/__generated__/EmployerRosterGridRefetchQuery.graphql';
import { EmployerRosterViewFilterInput } from '@/relay/__generated__/EmployerRosterGridRefetchQuery.graphql';
import BenefitElectionsTable from '@/src/components/EmployerRoster/BenefitElectionsTable/BenefitElectionsTable';
import { UpdateCustomViewsInput } from '@/relay/__generated__/EmployerRosterGridUpdateCustomViewsMutation.graphql';
import {
    BenefitElectionsRefetchQuery$variables,
    BenefitElectionsRosterDtoFilterInput
} from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { EmployerRosterGridQuery as EmployerRosterGridQueryType } from '@/relay/__generated__/EmployerRosterGridQuery.graphql';
import { EmployerRosterGridCustomViewsFragment$key } from '@/relay/__generated__/EmployerRosterGridCustomViewsFragment.graphql';
import { EmployerRosterGridCustomViewsFragment$data } from '@/relay/__generated__/EmployerRosterGridCustomViewsFragment.graphql';
import { EmployerRosterGridCustomViewsFragment_updatable$key } from '@/relay/__generated__/EmployerRosterGridCustomViewsFragment_updatable.graphql';
import { useBenefitElectionsColumnData } from '@/src/components/EmployerRoster/BenefitElectionsTable/BenefitElections.data';
import { EmployerRosterGridUpdateCustomViewsMutation as EmployerRosterGridUpdateCustomViewsMutationType } from '@/relay/__generated__/EmployerRosterGridUpdateCustomViewsMutation.graphql';
import { EmployerRosterGridBenefitElectionsQuery as EmployerRosterGridBenefitElectionsQueryType } from '@/relay/__generated__/EmployerRosterGridBenefitElectionsQuery.graphql';
import { EmployerRosterGridCustomViewsQuery as EmployerRosterGridCustomViewsQueryType } from '@/relay/__generated__/EmployerRosterGridCustomViewsQuery.graphql';
import { ColumnType } from '@/src/types/rosters';

type Props = {
    chapterIdEncoded: string;
};

export const EmployerRosterGridCustomViewsFragment = graphql`
    fragment EmployerRosterGridCustomViewsFragment on CustomViews {
        id
        value
    }
`;

export const EmployerRosterGridQuery = graphql`
    query EmployerRosterGridQuery($chapterId: ID!, $order: [EmployerRosterViewSortInput!], $where: EmployerRosterViewFilterInput!) {
        ...EmployerRosterTableFragment @arguments(chapterId: $chapterId, order: $order, where: $where)
    }
`;

export const EmployerRosterGridBenefitElectionsQuery = graphql`
    query EmployerRosterGridBenefitElectionsQuery(
        $chapterId: ID!
        $benefitElectionsRosterOrder: [BenefitElectionsRosterDtoSortInput!]
        $benefitElectionsRosterWhere: BenefitElectionsRosterDtoFilterInput!
    ) {
        ...BenefitElectionsTableFragment
            @arguments(chapterId: $chapterId, order: $benefitElectionsRosterOrder, where: $benefitElectionsRosterWhere)
    }
`;

export const EmployerRosterGridCustomViewsQuery = graphql`
    query EmployerRosterGridCustomViewsQuery($customViewsName: String!) {
        customViewsByType(name: $customViewsName) {
            ...EmployerRosterGridCustomViewsFragment
            ...EmployerRosterGridCustomViewsFragment_updatable
        }
    }
`;

export const EmployerRosterGridUpdateCustomViewsMutation = graphql`
    mutation EmployerRosterGridUpdateCustomViewsMutation($input: UpdateCustomViewsInput!) {
        updateCustomViews(input: $input) {
            customViews {
                ...EmployerRosterGridCustomViewsFragment
                value
            }
        }
    }
`;

const EmployerRosterGridContainer = ({ chapterIdEncoded: chapterIdEncoded }: Props) => {
    if (!chapterIdEncoded) {
        return <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />;
    }

    return (
        <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />}>
            <EmployerRosterGrid chapterIdEncoded={chapterIdEncoded} />
        </Suspense>
    );
};

const CustomViewsLoader = ({
    chapterIdEncoded: chapterId,
    children
}: {
    chapterIdEncoded: string;
    children: (customViewsData: any) => React.ReactNode;
}) => {
    const customViewsData = useSWRQuery(EmployerRosterGridCustomViewsQuery, {
        customViewsName: EMPLOYER_ROSTER_DATABASE_CUSTOM_VIEW_NAME
    });

    return <>{children(customViewsData)}</>;
};

const EmployerRosterViewLoader = ({
    chapterId,
    filter,
    sortOrder,
    children
}: {
    chapterId: string;
    filter: EmployerRosterGridQuery$variables;
    sortOrder: any;
    children: (data: any) => React.ReactNode;
}) => {
    const data = useSWRQuery<EmployerRosterGridQueryType>(EmployerRosterGridQuery, {
        ...filter,
        order: sortOrder,
        chapterId
    });

    return <>{children(data)}</>;
};

const BenefitElectionsViewLoader = ({
    chapterId,
    filter,
    sortOrder,
    children
}: {
    chapterId: string;
    filter: BenefitElectionsRefetchQuery$variables;
    sortOrder: any;
    children: (data: any) => React.ReactNode;
}) => {
    const data = useSWRQuery(EmployerRosterGridBenefitElectionsQuery, {
        chapterId,
        benefitElectionsRosterWhere: filter.where ?? null,
        benefitElectionsRosterOrder: sortOrder
    });

    return <>{children(data)}</>;
};

const EmployerRosterGrid = ({ chapterIdEncoded: chapterIdEncoded }: Props) => {
    // Memoize default query parameters to prevent infinite loops in useEffect
    const DEFAULT_ROOT_QUERY_PARAMS = useMemo(() => getDefaultEmployerRosterQueryVariables(chapterIdEncoded), [chapterIdEncoded]);
    const DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS = useMemo(
        () => getDefaultBenefitElectionsQueryVariables(chapterIdEncoded),
        [chapterIdEncoded]
    );

    // Get the metadata-based default column data
    const metadataBasedEmployerRosterColumns = employerRosterColumnData();
    const initialEmployerRosterColumns = useMemo(() => cloneDeep(metadataBasedEmployerRosterColumns), [metadataBasedEmployerRosterColumns]);
    const [currentEmployerRosterColumns, setCurrentEmployerRosterColumns] = useState(initialEmployerRosterColumns);

    // Benefit elections columns setup
    const metadataBasedBenefitElectionsColumns = useBenefitElectionsColumnData();
    const initialBenefitElectionsColumns = useMemo(
        () => cloneDeep(metadataBasedBenefitElectionsColumns),
        [metadataBasedBenefitElectionsColumns]
    );
    const [currentBenefitElectionsColumns, setCurrentBenefitElectionsColumns] = useState(initialBenefitElectionsColumns);

    // Pre-load the metadata for use in callbacks
    const freshEmployerRosterMetadata = useMemo(() => metadataBasedEmployerRosterColumns, [metadataBasedEmployerRosterColumns]);
    const freshBenefitElectionsMetadata = useMemo(() => metadataBasedBenefitElectionsColumns, [metadataBasedBenefitElectionsColumns]);

    const applyFreshMetadata = useCallback(
        (columns: ColumnType[], isEmployerRoster = true) => {
            const freshMetadata = isEmployerRoster ? freshEmployerRosterMetadata : freshBenefitElectionsMetadata;

            return columns.map((currentCol: ColumnType) => {
                const freshMetadataCol = freshMetadata.find((col) => col.key === currentCol.key);
                return {
                    ...currentCol,
                    label: freshMetadataCol?.label || currentCol.label,
                    columnLabel: freshMetadataCol?.columnLabel || currentCol.columnLabel
                };
            });
        },
        [freshEmployerRosterMetadata, freshBenefitElectionsMetadata]
    );

    const [filter, setFilter] = useState<EmployerRosterGridQuery$variables | BenefitElectionsRefetchQuery$variables>(
        DEFAULT_ROOT_QUERY_PARAMS
    );
    const [selectedCustomView, setSelectedCustomView] = useState<SavedCustomView | null>(null);
    const [selectedSystemView, setSelectedSystemView] = useState<EmployerRosterViews>(EmployerRosterViews.EMPLOYER_ROSTER);

    const [defaultViewId, setDefaultViewId] = useState<string>(EmployerRosterViews.EMPLOYER_ROSTER);
    const columndDataForHeader =
        selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER ? currentEmployerRosterColumns : currentBenefitElectionsColumns;
    const [sortOrder, setSortOrder] = useState<EmployerRosterGridQuery$variables['order']>(DEFAULT_ROOT_QUERY_PARAMS.order);

    const filterConditionsForVisibleColumns = useMemo(
        () => getFilterByColumnData(filter as EmployerRosterGridQuery$variables, currentEmployerRosterColumns),
        [filter, currentEmployerRosterColumns]
    );
    const filterForBenefitElections: BenefitElectionsRefetchQuery$variables = useMemo(
        () =>
            getFilterByColumnData(
                { where: filter.where, order: filter.order } as BenefitElectionsRefetchQuery$variables,
                currentBenefitElectionsColumns
            ),
        [filter, currentBenefitElectionsColumns]
    );

    const employerRosterSortOrder = useMemo(
        () => getSortOrderByColumnData([...(sortOrder ?? [])], currentEmployerRosterColumns, [...(DEFAULT_ROOT_QUERY_PARAMS.order ?? [])]),
        [sortOrder, currentEmployerRosterColumns, DEFAULT_ROOT_QUERY_PARAMS.order]
    );
    const benefitElectionsSortOrder = useMemo(
        () =>
            getSortOrderByColumnData([...(sortOrder ?? [])], currentBenefitElectionsColumns, [
                ...(DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS.order ?? [])
            ]),
        [sortOrder, currentBenefitElectionsColumns, DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS.order]
    );

    const [deletedEmployerIds, setDeletedEmployerIds] = useState<string[]>([]);
    const [pendingCustomViewSelectId, setPendingCustomViewSelectId] = useState<string | null>(null);

    const [commitUpdateCustomViews, isCommitUpdateCustomViewsInFlight] = useMutation(EmployerRosterGridUpdateCustomViewsMutation);

    const renderContent = (customViewsData: any) => {
        const customSavedViews = useFragment<EmployerRosterGridCustomViewsFragment$key>(
            EmployerRosterGridCustomViewsFragment,
            customViewsData.customViewsByType
        );

        const decodedCustomSavedViews = useMemo(() => getDecodedCustomSavedViews(customSavedViews), [customSavedViews]);

        const isSavedViewDisabled = useMemo(() => {
            if (!selectedCustomView) {
                let tempColumnData = currentEmployerRosterColumns;
                let tempDefaultColumnData = initialEmployerRosterColumns;
                let tempFilter: any = DEFAULT_ROOT_QUERY_PARAMS;
                let tempSortOrder = DEFAULT_ROOT_QUERY_PARAMS.order;

                if (selectedSystemView === EmployerRosterViews.BENEFIT_ELECTIONS) {
                    tempColumnData = currentBenefitElectionsColumns;
                    tempDefaultColumnData = initialBenefitElectionsColumns;
                    tempFilter = DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS;
                    tempSortOrder = DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS.order;
                }

                return isEqual(filter, tempFilter) && isEqual(sortOrder, tempSortOrder) && isEqual(tempColumnData, tempDefaultColumnData);
            }

            const tempColumnData =
                selectedCustomView.type === EmployerRosterViews.EMPLOYER_ROSTER
                    ? currentEmployerRosterColumns
                    : currentBenefitElectionsColumns;

            return (
                isEqual(filter, selectedCustomView.filter) &&
                isEqual(sortOrder, selectedCustomView.sortOrder) &&
                isEqual(tempColumnData, selectedCustomView.columns)
            );
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [
            filter,
            sortOrder,
            selectedCustomView,
            currentEmployerRosterColumns,
            currentBenefitElectionsColumns,
            selectedSystemView,
            initialEmployerRosterColumns,
            initialBenefitElectionsColumns
        ]);

        useEffect(() => {
            const defaultViewId = getDecodedDefaultViewId(customSavedViews);

            if (!defaultViewId) {
                const systemDefaultViewId = EmployerRosterViewsOptions.find((option) => option.isSystemDefault)?.id;

                if (systemDefaultViewId) {
                    setDefaultViewId(systemDefaultViewId);
                    onSaveDefaultViewId(systemDefaultViewId, decodedCustomSavedViews);
                    onRootSystemViewChangeHandler(systemDefaultViewId);
                }
            } else {
                const isBuiltInView = EmployerRosterViewsOptions.find((option) => option.id === defaultViewId);
                if (!isBuiltInView) {
                    onRootCustomViewChangeHandler(defaultViewId, decodedCustomSavedViews);
                } else {
                    onRootSystemViewChangeHandler(defaultViewId);
                }
                setDefaultViewId(defaultViewId);
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        useEffect(() => {
            if (pendingCustomViewSelectId) {
                onRootCustomViewChangeHandler(pendingCustomViewSelectId, decodedCustomSavedViews);
                setPendingCustomViewSelectId(null);
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [customSavedViews]);

        const onUpdateCustomViewHandler = ({
            input,
            onCompleted,
            onError,
            successMessage,
            errorMessage
        }: {
            input: UpdateCustomViewsInput;
            onCompleted: Function;
            onError: Function;
            successMessage: string;
            errorMessage: string;
        }) => {
            commitUpdateCustomViews({
                variables: {
                    input: {
                        id: customSavedViews?.id,
                        name: EMPLOYER_ROSTER_DATABASE_CUSTOM_VIEW_NAME,
                        ...input
                    }
                },
                optimisticUpdater: (store: RecordSourceSelectorProxy) => {
                    try {
                        const fragment = graphql`
                            fragment EmployerRosterGridCustomViewsFragment_updatable on CustomViews @updatable {
                                id
                                value
                            }
                        `;
                        if (customSavedViews) {
                            const { updatableData } = store.readUpdatableFragment<EmployerRosterGridCustomViewsFragment_updatable$key>(
                                fragment,
                                customViewsData.customViewsByType
                            );
                            updatableData.value = input.value;
                        } else {
                            console.warn('customSavedViews is null or undefined');
                        }
                    } catch (error) {
                        ToastQueue.negative(`Error updating store: ${JSON.stringify(error)}`, { timeout: 7000 });
                    }
                },
                onCompleted: (response: any) => {
                    const value = response?.updateCustomViews?.customViews?.value;

                    if (value) {
                        ToastQueue.positive(`${successMessage}`, {
                            timeout: 7000
                        });
                        if (onCompleted) {
                            onCompleted();
                        }
                    } else {
                        ToastQueue.negative(`${errorMessage}`, { timeout: 7000 });
                        if (onError) {
                            onError();
                        }
                    }
                },
                onError: (error) => {
                    ToastQueue.negative(`${errorMessage}: ${error.message}`, {
                        timeout: 7000
                    });
                    if (onError) {
                        onError();
                    }
                }
            });
        };

        const onSaveDefaultViewId = (id: string, savedViews: any) => {
            onUpdateCustomViewHandler({
                input: {
                    value: { views: savedViews, defaultViewId: id }
                },
                onCompleted: () => {},
                onError: () => {},
                successMessage: `${ToastMessages.UPDATE_DEFAULT_VIEW}`,
                errorMessage: `${ToastMessages.ERROR_UPDATE_DEFAULT_VIEW}`
            });
        };

        const onRootSaveViewHandler = ({
            savedLocalView,
            name,
            description
        }: {
            savedLocalView: any;
            name: string;
            description: string;
        }) => {
            const id = uuid4();
            // Get a fresh column definition based on the current metadata
            let columns = [];

            if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
                columns = applyFreshMetadata(currentEmployerRosterColumns, true);
            } else {
                columns = applyFreshMetadata(currentBenefitElectionsColumns, false);
            }

            const { chapterId, ...filterToSave } = filter;

            // Convert filter to FilterConfig format
            // Note: We need to convert the filter structure as GraphQL filters allow null values
            // but our SavedView FilterConfig expects undefined
            const convertedFilter: FilterConfig | undefined = filterToSave.where
                ? {
                      where: {
                          and: filterToSave.where.and ? (filterToSave.where.and as any[]) : undefined
                      }
                  }
                : undefined;

            // Convert EmployerRosterViewSortInput[] to SortConfig[]
            const convertedSortOrder = sortOrder
                ? sortOrder.map((sortInput) => {
                      const key = Object.keys(sortInput)[0];
                      const value = sortInput[key as keyof typeof sortInput];
                      return { [key]: value } as { [key: string]: 'ASC' | 'DESC' };
                  })
                : undefined;

            const newSavedView: SavedView = {
                id,
                name,
                description,
                filter: convertedFilter,
                columns,
                sortOrder: convertedSortOrder
            };
            const updatedSavedViews = getAddedViews(newSavedView, decodedCustomSavedViews);

            onUpdateCustomViewHandler({
                input: {
                    value: { views: updatedSavedViews, defaultViewId }
                },
                onCompleted: () => {
                    setPendingCustomViewSelectId(id);
                },
                onError: () => {},
                successMessage: `${ToastMessages.CREATE_VIEW} "${name}"`,
                errorMessage: `${ToastMessages.ERROR_CREATE_VIEW} "${name}"`
            });
        };

        const onRootUpdateViewHandler = (id: string, updatedLocalView: any) => {
            // Get a fresh column definition based on the current metadata
            let columns = [];

            if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
                columns = applyFreshMetadata(currentEmployerRosterColumns, true);
            } else {
                columns = applyFreshMetadata(currentBenefitElectionsColumns, false);
            }

            const { chapterId, ...filterToSave } = filter;

            // Convert filter to FilterConfig format
            // Note: We need to convert the filter structure as GraphQL filters allow null values
            // but our SavedView FilterConfig expects undefined
            const convertedFilter: FilterConfig | undefined = filterToSave.where
                ? {
                      where: {
                          and: filterToSave.where.and ? (filterToSave.where.and as any[]) : undefined
                      }
                  }
                : undefined;

            // Convert EmployerRosterViewSortInput[] to SortConfig[]
            const convertedSortOrder = sortOrder
                ? sortOrder.map((sortInput) => {
                      const key = Object.keys(sortInput)[0];
                      const value = sortInput[key as keyof typeof sortInput];
                      return { [key]: value } as { [key: string]: 'ASC' | 'DESC' };
                  })
                : undefined;

            const updatedView: Partial<SavedView> = {
                filter: convertedFilter,
                columns,
                sortOrder: convertedSortOrder
            };
            const { updatedSavedViews, updatedViewResponse } = getUpdatedView(id, updatedView, decodedCustomSavedViews);

            if (updatedSavedViews) {
                onUpdateCustomViewHandler({
                    input: {
                        value: { views: updatedSavedViews, defaultViewId }
                    },
                    onCompleted: () => {
                        setPendingCustomViewSelectId(id);
                    },
                    onError: () => {},
                    successMessage: `${ToastMessages.UPDATE_VIEW} "${updatedViewResponse?.name}"`,
                    errorMessage: `${ToastMessages.ERROR_UPDATE_VIEW} "${updatedViewResponse?.name}"`
                });
            }
        };

        const onRootRenameViewHandler = (id: string, name: string, description: string) => {
            const updatedView = {
                name,
                description
            };
            const { updatedSavedViews, updatedViewResponse } = getUpdatedView(id, updatedView, decodedCustomSavedViews);

            if (updatedSavedViews) {
                onUpdateCustomViewHandler({
                    input: {
                        value: { views: updatedSavedViews, defaultViewId }
                    },
                    onCompleted: () => {},
                    onError: () => {},
                    successMessage: `${ToastMessages.UPDATE_VIEW} "${updatedViewResponse?.name}"`,
                    errorMessage: `${ToastMessages.ERROR_UPDATE_VIEW} "${updatedViewResponse?.name}"`
                });
            }
        };

        const onRootDeleteViewHandler = (id: string) => {
            const { updatedSavedViews, deletedView } = getDeletedViews(id, decodedCustomSavedViews);

            if (deletedView) {
                onUpdateCustomViewHandler({
                    input: {
                        value: { views: updatedSavedViews, defaultViewId }
                    },
                    onCompleted: () => {},
                    onError: () => {},
                    successMessage: `"${deletedView?.name}"${ToastMessages.DELETE_VIEW}`,
                    errorMessage: `${ToastMessages.ERROR_DELETE_VIEW} "${deletedView?.name}"`
                });
            }
        };

        const onRootChangeDefaultViewHandler = (id: string) => {
            setDefaultViewId(id);
            onSaveDefaultViewId(id, decodedCustomSavedViews);
        };

        const onRootCustomViewChangeHandler = (id: string | null, savedViews: any) => {
            if (id) {
                const view = getViewById(id, savedViews);

                // When applying a saved view, ensure it uses the current chapterId
                if (view) {
                    // Update column labels with current metadata before setting the view
                    let updatedColumns = [...(view.columns || [])];

                    if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
                        updatedColumns = applyFreshMetadata(view.columns || [], true);
                    } else if (selectedSystemView === EmployerRosterViews.BENEFIT_ELECTIONS) {
                        updatedColumns = applyFreshMetadata(view.columns || [], false);
                    }

                    setSelectedCustomView({
                        ...view,
                        columns: updatedColumns,
                        filter: {
                            chapterId: chapterIdEncoded,
                            order: sortOrder || [],
                            where: view.filter?.where || {}
                        }
                    } as SavedCustomView);
                } else {
                    setSelectedCustomView(null);
                }
            } else {
                setSelectedCustomView(null);
            }
        };

        const onDeleteAndChangeDefaultViewHandler = (id: string, newDefaultViewId: string) => {
            const { updatedSavedViews, deletedView } = getDeletedViews(id, decodedCustomSavedViews);

            if (deletedView) {
                const newDefaultView =
                    getViewById(newDefaultViewId, decodedCustomSavedViews) ||
                    EmployerRosterViewsOptions.find((option) => option.id === newDefaultViewId);

                onUpdateCustomViewHandler({
                    input: {
                        value: { views: updatedSavedViews, defaultViewId: newDefaultViewId }
                    },
                    onCompleted: () => {
                        setDefaultViewId(newDefaultViewId);
                    },
                    onError: () => {},
                    successMessage: `${ToastMessages.DELETE_DEFAIULT_VIEW(deletedView?.name || '', newDefaultView?.name || '')}`,
                    errorMessage: `${ToastMessages.ERROR_DELETE_VIEW} "${deletedView?.name}"`
                });
            }
        };

        // --- Helper Logic to Clean Where Clause for Benefit Elections ---
        const getBenefitElectionsWhereClause = (): BenefitElectionsRosterDtoFilterInput => {
            const currentWhere = filter.where as EmployerRosterViewFilterInput | BenefitElectionsRosterDtoFilterInput | undefined;
            if (!currentWhere || !Array.isArray(currentWhere.and)) {
                return {}; // Return empty if no structure or no 'and' array
            }
            // Clone the 'and' array and filter out the relationshipStatusId condition
            const cleanedAnd = currentWhere.and.filter((condition) => !condition.relationshipStatusId); // Remove the specific condition

            // Return new object with cleaned 'and' array, or empty if 'and' becomes empty
            // Ensure the structure matches BenefitElectionsRosterDtoFilterInput if needed
            return cleanedAnd.length > 0 ? { and: cleanedAnd } : {};
        };
        // Calculate the cleaned clause once
        const benefitElectionsWhereClause = getBenefitElectionsWhereClause();
        // --- End Helper Logic ---

        const renderEmployerRosterView = (employerRosterData: any) => (
            <EmployerRosterTable
                queryRef={employerRosterData}
                columnData={currentEmployerRosterColumns}
                filter={{
                    chapterId: chapterIdEncoded,
                    where: (filter.where as EmployerRosterViewFilterInput) ?? {},
                    order: employerRosterSortOrder
                }}
                onRemoveEmployer={onRemoveEmployerHandler}
                deletedEmployerIds={deletedEmployerIds}
                onRootSortChange={onRootSortChangeHandler}
            />
        );

        const renderBenefitElectionsView = (benefitElectionsData: any) => (
            <BenefitElectionsTable
                queryRef={benefitElectionsData}
                columnData={currentBenefitElectionsColumns}
                filter={{
                    chapterId: chapterIdEncoded,
                    // Use the CLEANED where clause for Benefit Elections
                    where: benefitElectionsWhereClause,
                    order: benefitElectionsSortOrder
                }}
                onRootSortChange={onRootSortChangeHandler}
            />
        );

        const renderCurrentView = () => {
            if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
                return (
                    <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%' }} />}>
                        <EmployerRosterViewLoader
                            chapterId={chapterIdEncoded}
                            filter={{
                                chapterId: chapterIdEncoded,
                                // Use the original where clause
                                where: (filter.where as EmployerRosterViewFilterInput) ?? {}
                            }}
                            sortOrder={employerRosterSortOrder}>
                            {renderEmployerRosterView}
                        </EmployerRosterViewLoader>
                    </Suspense>
                );
            } else {
                // Benefit Elections View
                return (
                    <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%' }} />}>
                        <BenefitElectionsViewLoader
                            chapterId={chapterIdEncoded}
                            filter={{
                                chapterId: chapterIdEncoded,
                                // Use the CLEANED where clause
                                where: benefitElectionsWhereClause
                            }}
                            sortOrder={benefitElectionsSortOrder}>
                            {renderBenefitElectionsView}
                        </BenefitElectionsViewLoader>
                    </Suspense>
                );
            }
        };

        return (
            <Flex direction="column" alignItems="center" width="100%" flex={1}>
                <Flex width="100%" direction="column" marginBottom="size-100" columnGap="size-100" flex={1}>
                    <EmployerRosterHeader
                        chapterIdEncoded={chapterIdEncoded}
                        onExport={onExportHandler}
                        defaultViewId={defaultViewId}
                        columnData={columndDataForHeader}
                        onRootSaveView={onRootSaveViewHandler}
                        selectedSystemView={selectedSystemView}
                        selectedCustomView={selectedCustomView}
                        isSavedViewDisabled={isSavedViewDisabled}
                        customSavedViews={decodedCustomSavedViews}
                        onFilterChange={onRootFilterChangeHandler}
                        onRootUpdateView={onRootUpdateViewHandler}
                        onRootDeleteView={onRootDeleteViewHandler}
                        onRootRenameView={onRootRenameViewHandler}
                        onToggleColumnShow={onToggleColumnShowHandler}
                        onRootSystemViewChange={onRootSystemViewChangeHandler}
                        onRootSelectCustomView={(id) => onRootCustomViewChangeHandler(id, decodedCustomSavedViews)}
                        onRootChangeDefaultView={onRootChangeDefaultViewHandler}
                        onDeleteAndChangeDefaultView={onDeleteAndChangeDefaultViewHandler}
                    />

                    {renderCurrentView()}
                </Flex>
            </Flex>
        );
    };

    useEffect(() => {
        setFilter((prevFilter) => ({ ...prevFilter, chapterId: chapterIdEncoded }));

        // When chapterId changes via proxy combo box and a saved view is selected,
        // we need to update the filter object but retain other saved view settings
        if (selectedCustomView) {
            setSortOrder(selectedCustomView.sortOrder);
            if (selectedCustomView.type === EmployerRosterViews.EMPLOYER_ROSTER) {
                setFilter((prevFilter) => ({
                    ...selectedCustomView.filter,
                    chapterId: chapterIdEncoded
                }));
            } else if (selectedCustomView.type === EmployerRosterViews.BENEFIT_ELECTIONS) {
                setFilter((prevFilter) => ({
                    ...selectedCustomView.filter,
                    chapterId: chapterIdEncoded
                }));
            }
        }
    }, [chapterIdEncoded, selectedCustomView]);

    useEffect(() => {
        if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER && !selectedCustomView) {
            setCurrentEmployerRosterColumns(initialEmployerRosterColumns);
            setFilter(DEFAULT_ROOT_QUERY_PARAMS);
            setSortOrder(DEFAULT_ROOT_QUERY_PARAMS.order);
        } else if (selectedSystemView === EmployerRosterViews.BENEFIT_ELECTIONS && !selectedCustomView) {
            setCurrentBenefitElectionsColumns(initialBenefitElectionsColumns);
            setFilter(DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS);
            setSortOrder(DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS.order);
        }
    }, [
        selectedSystemView,
        selectedCustomView,
        DEFAULT_ROOT_QUERY_PARAMS,
        DEFAULT_BENEFIT_ELECTIONS_ROOT_QUERY_PARAMS,
        initialEmployerRosterColumns,
        initialBenefitElectionsColumns
    ]);

    const onRootRestoreViewWithNewSavedViewHandler = useCallback(() => {
        if (!selectedCustomView) return;

        if (selectedCustomView.type === EmployerRosterViews.EMPLOYER_ROSTER) {
            setSelectedSystemView(EmployerRosterViews.EMPLOYER_ROSTER);
            setFilter(selectedCustomView.filter);
            setSortOrder(selectedCustomView.sortOrder);
            setCurrentEmployerRosterColumns(selectedCustomView.columns);
        } else if (selectedCustomView.type === EmployerRosterViews.BENEFIT_ELECTIONS) {
            setSelectedSystemView(EmployerRosterViews.BENEFIT_ELECTIONS);
            setFilter(selectedCustomView.filter);
            setSortOrder(selectedCustomView.sortOrder);
            setCurrentBenefitElectionsColumns(selectedCustomView.columns);
        }
    }, [selectedCustomView, setCurrentBenefitElectionsColumns]);

    useEffect(() => {
        if (selectedCustomView) {
            onRootRestoreViewWithNewSavedViewHandler();
        }
    }, [selectedCustomView, onRootRestoreViewWithNewSavedViewHandler]);

    const onToggleColumnShowHandler = (values: string[]) => {
        const currentColumnDataForView =
            selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER ? currentEmployerRosterColumns : currentBenefitElectionsColumns;
        const updatedColumnData = cloneDeep(currentColumnDataForView);

        for (let i = 0; i < updatedColumnData.length; i++) {
            const column = updatedColumnData[i];
            column.show = !column.canHide || values.includes(column.columnLabel);
        }

        // First update the columns without changing the filter
        if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
            setCurrentEmployerRosterColumns(updatedColumnData);
        } else {
            setCurrentBenefitElectionsColumns(updatedColumnData);
        }
    };

    const onRootFilterChangeHandler = (newWhereClause: EmployerRosterViewFilterInput | null | undefined) => {
        setFilter((prevFilter) => ({
            ...prevFilter,
            where: newWhereClause ?? {}
        }));
    };

    const onRootSortChangeHandler = (sortData: EmployerRosterViewSortInput[]) => {
        setSortOrder(sortData);
    };

    const onRootSystemViewChangeHandler = (view: string | null) => {
        if (view !== null) {
            setSelectedSystemView(view as EmployerRosterViews);
        }
    };

    const onRemoveEmployerHandler = (employerId: string) => {
        setDeletedEmployerIds((prevIds) => [...prevIds, employerId]);
    };

    const onExportHandler = (format: string) => {
        if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER) {
            exportRosterView(chapterIdEncoded, currentEmployerRosterColumns, format, selectedSystemView);
        } else if (selectedSystemView === EmployerRosterViews.BENEFIT_ELECTIONS) {
            exportRosterView(chapterIdEncoded, currentBenefitElectionsColumns, format, selectedSystemView);
        }
    };

    return (
        <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />}>
            <CustomViewsLoader chapterIdEncoded={chapterIdEncoded}>{renderContent}</CustomViewsLoader>
        </Suspense>
    );
};

// Export the container component as the default
export default EmployerRosterGridContainer;
