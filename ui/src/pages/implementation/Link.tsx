import { useState, useEffect, useCallback, useMemo } from 'react'; // Added useMemo and useCallback
// import { Dialog } from '@progress/kendo-react-dialogs';
import ChapterProxyCombobox from '@/src/components/ProxyComboboxes/ChapterProxyCombobox'; // Removed query import as it's passed via prop
import type { ChapterProxyComboboxQuery as ProxyComboboxChapterQueryType } from '@/relay/__generated__/ChapterProxyComboboxQuery.graphql'; // Corrected import path
import { PreloadedQuery } from 'react-relay/hooks'; // Added import
import { Constants } from '@/src/constants/global';
import { graphql } from 'relay-runtime';
import { useMutation, ConnectionHandler } from 'react-relay';
import { useStore, StoreState } from '@/lib';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { ActionButton } from '@/src/components/UI/ActionButton/ActionButton';
import { ProxyComboboxOption } from '@/src/components/ProxyComboboxes/types';
import {
    Flex,
    Text,
    Form,
    ComboBox,
    Item,
    // TextField, // No longer needed
    Button,
    Heading,
    Content,
    ButtonGroup,
    DialogContainer,
    Divider,
    Dialog
} from '@adobe/react-spectrum';
import Add from '@spectrum-icons/workflow/Add';

const LinkThirdPartyMutation = graphql`
    mutation LinkThirdPartyMutation($input: LinkThirdPartyInput!, $connections: [ID!]!) {
        linkThirdParty(input: $input) {
            thirdPartyInfoDtoEdge @appendEdge(connections: $connections) {
                cursor
                node {
                    value
                    label
                    guid
                }
            }
        }
    }
`;

// Define expected structure from backend including isLinked
interface BackendOrgOption extends ProxyComboboxOption {
    isLinked: boolean;
}

// Updated LinkProps
interface LinkProps {
    setShowErrorDialog: (show: boolean) => void;
    onSuccess: (message: string) => void;
    chapterQueryRef: PreloadedQuery<ProxyComboboxChapterQueryType>;
    chapterOrganizations: BackendOrgOption[]; // Expect BackendOrgOption now
    isChapterOrgsLoading: boolean;
    fetchOrgsForChapter: (chapterId: number | null) => Promise<void>;
}

// Define types for the dialog state
type LinkDialogType = 'inputRequired' | 'confirmLink' | 'duplicateRelationship' | 'inputError' | null;

const Link = ({
    setShowErrorDialog,
    onSuccess,
    chapterQueryRef,
    chapterOrganizations, // Now expects BackendOrgOption[]
    isChapterOrgsLoading,
    fetchOrgsForChapter
}: LinkProps) => {
    // Removed boolean dialog states
    // const [showInputRequiredDialog, ...] = useState(false);
    // const [showLinkConfirmationDialog, ...] = useState(false);
    // const [showDuplicateRelationshipDialog, ...] = useState(false);
    // const [showInputErrorDialog, ...] = useState(false);

    // Add state for active dialog
    const [activeDialog, setActiveDialog] = useState<LinkDialogType>(null);

    const [selectedOrgTypeId, setSelectedOrgTypeId] = useState<string>(Constants.OrganizationTypes.FundAdministrator);
    const [selectedOrgType, setSelectedOrgType] = useState<string>(Constants.OrganizationTypes.FundAdministrator);
    const [selectedLinkSiteId, setSelectedLinkSiteId] = useState<number | null>(null); // Added state for Link Site Sponsor ID
    const [selectedLinkSiteName, setSelectedLinkSiteName] = useState<string | null>(null); // Added state for Link Site Sponsor Name
    const [duplicateRelationshipMessage, setDuplicateRelationshipMessage] = useState('');
    const [noMatchingOrgMessage, setNoMatchingOrgMessage] = useState('');
    const [selectedOrgToLink, setSelectedOrgToLink] = useState<BackendOrgOption | null>(null);
    const [orgInputValue, setOrgInputValue] = useState<string>('');
    // filteredOrganizations now depends on chapterOrganizations prop
    const [filteredOrganizations, setFilteredOrganizations] = useState<BackendOrgOption[]>([]);
    const proxyThirdPartiesConnectionId = useStore((state: StoreState) => state.proxyThirdPartiesConnectionId);
    const proxyEmployersConnectionId = useStore((state: StoreState) => state.proxyEmployersConnectionId);

    const [commitLinkMutation, linkMutationInFlight] = useMutation(LinkThirdPartyMutation);

    const linkDropdownOptions = [
        {
            id: Constants.OrganizationTypes.FundAdministrator,
            name: Constants.OrganizationTypes.FundAdministrator
        },
        { id: Constants.OrganizationTypes.Union, name: Constants.OrganizationTypes.Union }
    ];

    function handleOrgTypeChange(selectedId: React.Key | null) {
        const selectedIdStr = selectedId as string;
        if (!selectedIdStr) return;
        const selectedTypeObj = linkDropdownOptions.find(o => o.id === selectedIdStr);
        setSelectedOrgTypeId(selectedIdStr);
        setSelectedOrgType(selectedTypeObj ? selectedTypeObj.name : '');
    }

    // Removed useEffect that depended on allOrganizations

    // Update filteredOrganizations when chapterOrganizations prop changes
    useEffect(() => {
        setFilteredOrganizations(chapterOrganizations);
        setOrgInputValue('');
        setSelectedOrgToLink(null);
    }, [chapterOrganizations]);

    // Handler for organization combobox input change (uses chapterOrganizations now)
    const handleOrgInputChange = (value: string) => {
        setOrgInputValue(value);
        if (value === '') {
            setFilteredOrganizations(chapterOrganizations);
        } else {
            const lowerCaseValue = value.toLowerCase();
            setFilteredOrganizations(
                chapterOrganizations.filter(org =>
                    org.label.toLowerCase().includes(lowerCaseValue)
                )
            );
        }
    };

    // Handler for Site Sponsor selection change
    const handleChapterChange = useCallback((selected: ProxyComboboxOption | null) => {
        const newChapterId = selected ? selected.value : null;
        const newChapterName = selected ? selected.label : null;

        setSelectedLinkSiteId(newChapterId);
        setSelectedLinkSiteName(newChapterName);
        setSelectedOrgToLink(null); // Reset org selection when chapter changes
        setOrgInputValue(''); // Reset org input value
        // Fetch organizations for the newly selected chapter
        fetchOrgsForChapter(newChapterId);
    }, [fetchOrgsForChapter]); // Add fetchOrgsForChapter dependency


    const handleLink = () => {
        setActiveDialog(null); // Close dialog before mutation
        if (!selectedOrgToLink || !selectedLinkSiteId) return;

        const orgNameToLink = selectedOrgToLink.label;
        const LinkSuccessMessage = `You have succesfully linked ${orgNameToLink} to ${selectedLinkSiteName}`;
        const specificDuplicateRelationshipMessage = `${orgNameToLink} already has a relationship with the site ${selectedLinkSiteName}.`;
        const specificNoMatchingOrgMessage = `${orgNameToLink} does not exist. Check spelling/spacing. If it's new, use the "Create" section above.`;

        commitLinkMutation({
            variables: {
                input: {
                    name: orgNameToLink, // Pass the selected org's name
                    chapterId: selectedLinkSiteId, // Use state specific to Link section
                    relationshipTypeId: selectedOrgType === Constants.OrganizationTypes.Union ? 3 : 4,
                    username: ClientUtils.getUsername()
                },
                connections: [
                    ConnectionHandler.getConnectionID(proxyThirdPartiesConnectionId, 'ThirdPartyProxyComboboxFragment_thirdParties', {
                        chapterId: selectedLinkSiteId // Use state specific to Link section
                    }),
                    ConnectionHandler.getConnectionID(proxyEmployersConnectionId, 'EmployerProxyComboboxFragment_employers', {
                        chapterId: selectedLinkSiteId // Use state specific to Link section
                    })
                ]
            },
            onCompleted() {
                onSuccess(LinkSuccessMessage);
                setSelectedOrgToLink(null);
                setOrgInputValue('');
                if (selectedLinkSiteId) {
                    fetchOrgsForChapter(selectedLinkSiteId);
                }
            },
            onError(error: any) {
                const errorCode = error.cause?.[0]?.extensions?.message;
                if (errorCode === 'No third party with name') {
                    setNoMatchingOrgMessage(specificNoMatchingOrgMessage);
                    setActiveDialog('inputError');
                } else if (errorCode === 'Relationship already exists') {
                    setDuplicateRelationshipMessage(specificDuplicateRelationshipMessage);
                    setActiveDialog('duplicateRelationship');
                } else {
                    setShowErrorDialog(true);
                }
            }
        });
    };

    const handleLinkClick = () => {
        if (!selectedOrgToLink || !selectedOrgTypeId || !selectedLinkSiteId) {
            setActiveDialog('inputRequired');
            return;
        }
        setActiveDialog('confirmLink');
    };

    // Use this single close handler for DialogContainer
    const handleDialogClose = () => {
        setActiveDialog(null);
        setDuplicateRelationshipMessage('');
        setNoMatchingOrgMessage('');
    };

    const isLoading = linkMutationInFlight || isChapterOrgsLoading; // Consider chapter org loading state

    // Determine if the link button should be disabled
    // Determine if the Organization Name combobox should be disabled
    const isOrgComboboxDisabled = !selectedLinkSiteId || isChapterOrgsLoading || linkMutationInFlight;

    const isLinkDisabled =
        !selectedOrgToLink || // Check if an org is selected from combobox
        !selectedOrgTypeId ||
        !selectedLinkSiteId ||
        isLoading; // Use combined loading state

    // Calculate disabled keys based on the IsLinked property
    const disabledOrgKeys = useMemo(() =>
        new Set(
            chapterOrganizations // Use the full list from props
                .filter(org => org.isLinked) // Filter for already linked orgs
                .map(org => org.value.toString()) // Map to their keys (assuming value is the key)
        ),
        [chapterOrganizations] // Recalculate when the list changes
    );

    return (
        <div className="LinkToClient" style={{ marginTop: 'var(--spectrum-global-dimension-size-400)' }}>
            <Heading level={2} marginBottom="size-200">Link Organization</Heading>
            <Text>
                <strong>Use this section to link non-employer organizations</strong> that need access to the administrative responsibilities under a new Site
                <br /><br />
                <strong> Only System Administrators have permission to create new relationships between non-employer organizations.</strong>
            </Text>
            <Form marginTop="size-200" width="auto" validationBehavior="native">
                 <Flex direction="row" gap="size-300" alignItems="start" marginTop="size-200">
                    <ComboBox<typeof linkDropdownOptions[0]>
                        label={<strong style={{ marginRight: '8px' }}>Organization type:</strong>}
                        labelPosition="side"
                        labelAlign="start"
                        items={linkDropdownOptions}
                        selectedKey={selectedOrgTypeId}
                        onSelectionChange={handleOrgTypeChange}
                        width="size-4600"
                        isDisabled={isLoading} // Disable if linking or loading chapter orgs
                    >
                        {(item) => <Item key={item.id}>{item.name}</Item>}
                    </ComboBox>

                    {/* Site Sponsor Dropdown - Use label prop */}
                    <ChapterProxyCombobox
                        label={<strong>Site Sponsor:</strong>}
                        labelPosition="side"
                        labelAlign="start"
                        width="size-4600"
                        queryRef={chapterQueryRef}
                        currentValue={selectedLinkSiteId?.toString() || null}
                        onChange={handleChapterChange}
                        title="" // Pass empty title as label is used
                        // isDisabled prop is not supported by ChapterProxyCombobox, remove if present
                    />
                 </Flex>

                 {/* Organization Name ComboBox */}
                 <Flex direction="row" gap="size-300" alignItems="start" marginTop="size-200">
                    <ComboBox<BackendOrgOption>
                        label={<strong>Organization name:</strong>}
                        labelPosition="side"
                        labelAlign="start"
                        items={filteredOrganizations}
                        selectedKey={selectedOrgToLink?.value?.toString() ?? null}
                        inputValue={orgInputValue}
                        onInputChange={handleOrgInputChange}
                        onSelectionChange={(key) => {
                            const selected = chapterOrganizations.find(org => org.value.toString() === key) || null;
                            setSelectedOrgToLink(selected);
                            setOrgInputValue(selected ? selected.label : '');
                            setFilteredOrganizations(chapterOrganizations);
                        }}
                        width="size-4600"
                        isDisabled={isOrgComboboxDisabled}
                        allowsCustomValue={true}
                        loadingState={isChapterOrgsLoading ? 'loading' : 'idle'}
                        disabledKeys={disabledOrgKeys}
                    >
                        {(item) => <Item key={item.value}>{item.label}</Item>}
                    </ComboBox>
                 </Flex>
            </Form>

            <ActionButton
                wrapperProps={{
                    width: 'size-2000'
                }}
                isDisabled={isLinkDisabled}
                onPress={!isLoading ? handleLinkClick : () => {}}
            >
                <Flex alignItems="center" justifyContent="center" gap="size-100">
                    {linkMutationInFlight ? <Text>Linking...</Text> : <Add size="S" />}
                    {!linkMutationInFlight && <Text>Link Organization</Text>}
                </Flex>
            </ActionButton>

            {/* Replaced Kendo Dialogs with Spectrum DialogContainer */}
            <DialogContainer onDismiss={handleDialogClose}>
                {activeDialog === 'inputRequired' && (
                    <Dialog>
                        <Heading>Input Required</Heading>
                        <Divider />
                        <Content><Text>You must select the Organization Type, Site Sponsor, and Organization Name.</Text></Content>
                        <ButtonGroup>
                            <Button variant="primary" onPress={handleDialogClose} autoFocus>Ok</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
                {activeDialog === 'duplicateRelationship' && (
                    <Dialog>
                        <Heading>Unable to link</Heading>
                        <Divider />
                        <Content><Text>{duplicateRelationshipMessage}</Text></Content>
                        <ButtonGroup>
                            <Button variant="primary" onPress={handleDialogClose} autoFocus>Ok</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
                {activeDialog === 'inputError' && (
                    <Dialog>
                        <Heading>Organization Does Not Exist</Heading>
                        <Divider />
                        <Content><Text>{noMatchingOrgMessage}</Text></Content>
                        <ButtonGroup>
                            <Button variant="primary" onPress={handleDialogClose} autoFocus>Ok</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
                {activeDialog === 'confirmLink' && (
                    <Dialog>
                        <Heading>Confirm Link Organization</Heading>
                        <Divider />
                        <Content>
                            <Text>
                               {`Are you sure you want to link ${selectedOrgToLink?.label ?? 'the selected organization'} to Site Sponsor ${selectedLinkSiteName}?`}
                            </Text>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={handleDialogClose}>No</Button>
                            <Button variant="primary" onPress={handleLink} isDisabled={isLoading} autoFocus>Yes</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>
        </div>
    );
};

export default Link;
