import { useMemo } from 'react';
import { useLazyLoadQuery } from 'react-relay';
import { graphql } from 'react-relay';
import { toGlobalId } from '@/src/services/RelayIdService';
import { transformNodesResponse, type DropdownOption } from './useComboBoxQuery';
import type { useBatchComboBoxQueriesQuery } from '@/relay/__generated__/useBatchComboBoxQueriesQuery.graphql';

/**
 * Configuration for batch query execution
 */
export interface BatchQueryConfig {
    /** Whether to execute the query based on dependencies */
    shouldExecute: boolean;
    /** Agreement ID for filtering classifications and sub-classifications */
    agreementId: string | null | undefined;
    /** Classification ID for filtering sub-classifications */
    classificationId: string | null | undefined;
}

/**
 * Result from batch query containing options for all dropdown components
 */
export interface BatchQueryResult {
    /** Classification options for the given agreement */
    classifications: DropdownOption[];
    /** Sub-classification options for the given agreement and classification */
    subClassifications: DropdownOption[];
    /** Whether any query is currently loading */
    isLoading: boolean;
    /** Whether any query encountered an error */
    hasError: boolean;
    /** Error message if any error occurred */
    errorMessage?: string;
}

/**
 * Batch query for fetching both classifications and sub-classifications in a single request
 * This reduces the number of network requests and improves performance for cascading dropdowns
 *
 * @param config - Configuration object with agreement and classification IDs
 * @returns Object containing options for both classifications and sub-classifications
 *
 * @example
 * ```typescript
 * const { classifications, subClassifications, isLoading } = useBatchComboBoxQueries({
 *   shouldExecute: !!agreementId,
 *   agreementId: 'agreement-123',
 *   classificationId: 'classification-456'
 * });
 * ```
 */
export function useBatchComboBoxQueries(config: BatchQueryConfig): BatchQueryResult {
    const { shouldExecute, agreementId, classificationId } = config;

    // GraphQL query that fetches both classifications and sub-classifications
    const batchQuery = graphql`
        query useBatchComboBoxQueriesQuery($agreementId: ID!, $classificationId: ID!) {
            # Fetch classifications by agreement
            classificationsByAgreementId(agreementId: $agreementId) {
                nodes {
                    id
                    name
                }
            }

            # Fetch sub-classifications by agreement and classification
            subClassificationsByAgreementAndClassification(agreementId: $agreementId, classificationId: $classificationId) {
                nodes {
                    id
                    name
                }
            }
        }
    `;

    // Only execute when we have at least an agreement ID
    const shouldExecuteClassifications = shouldExecute && !!agreementId;
    // Only execute sub-classifications when we have both IDs
    const shouldExecuteSubClassifications = shouldExecute && !!(agreementId && classificationId);

    // Use placeholder IDs when not executing to maintain query structure
    const queryVariables = {
        agreementId: toGlobalId('Agreement', agreementId || '0'),
        classificationId: toGlobalId('ClassificationName', classificationId || '0')
    };

    const data = useLazyLoadQuery<useBatchComboBoxQueriesQuery>(batchQuery, queryVariables, {
        // Use store-only when not executing to avoid unnecessary network requests
        fetchPolicy: shouldExecuteClassifications ? 'store-or-network' : 'store-only'
    });

    const result = useMemo(() => {
        const isLoading = shouldExecuteClassifications && !data;

        if (!shouldExecute) {
            return {
                classifications: [],
                subClassifications: [],
                isLoading: false,
                hasError: false
            };
        }

        try {
            // Return empty arrays when loading (data is null)
            if (isLoading) {
                return {
                    classifications: [],
                    subClassifications: [],
                    isLoading,
                    hasError: false
                };
            }

            // Transform classifications data
            const classifications = shouldExecuteClassifications
                ? transformNodesResponse(data as any, 'classificationsByAgreementId.nodes')
                : [];

            // Transform sub-classifications data only if we should execute that part
            const subClassifications = shouldExecuteSubClassifications
                ? transformNodesResponse(data as any, 'subClassificationsByAgreementAndClassification.nodes')
                : [];

            return {
                classifications,
                subClassifications,
                isLoading,
                hasError: false
            };
        } catch (error) {
            console.error('Error transforming batch ComboBox data:', error);
            return {
                classifications: [],
                subClassifications: [],
                isLoading,
                hasError: true,
                errorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }, [data, shouldExecute, shouldExecuteClassifications, shouldExecuteSubClassifications]);

    return result;
}

/**
 * Hook for using classifications from batch query
 * This is a convenience hook that extracts just the classifications from the batch result
 *
 * @param config - Batch query configuration
 * @returns Classification options and loading state
 */
export function useClassificationsFromBatch(config: BatchQueryConfig) {
    const { classifications, isLoading, hasError, errorMessage } = useBatchComboBoxQueries(config);

    return {
        options: classifications,
        isLoading,
        hasError,
        errorMessage
    };
}

/**
 * Hook for using sub-classifications from batch query
 * This is a convenience hook that extracts just the sub-classifications from the batch result
 *
 * @param config - Batch query configuration
 * @returns Sub-classification options and loading state
 */
export function useSubClassificationsFromBatch(config: BatchQueryConfig) {
    const { subClassifications, isLoading, hasError, errorMessage } = useBatchComboBoxQueries(config);

    return {
        options: subClassifications,
        isLoading,
        hasError,
        errorMessage
    };
}
