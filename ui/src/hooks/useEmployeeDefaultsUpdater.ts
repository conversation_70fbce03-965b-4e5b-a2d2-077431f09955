/**
 * Employee Defaults Updater Hook
 * 
 * This hook provides functionality to update employee default settings
 * using proper Relay patterns. It uses the useMutation hook to execute
 * the saveSetting mutation for updating employee defaults.
 * 
 * This hook follows Relay best practices and provides proper error handling,
 * loading states, and optimistic updates.
 */

import { useMutation } from 'react-relay';
import { UpdateEmployeeSettingMutation } from '../mutations/UpdateEmployeeDefaultsMutation';
import { type EmployeeDefaultsUpdate } from '../services/timesheet-detail';
import { useTimesheetDetailService } from '../context/TimesheetDetailServiceContext';
import type { UpdateEmployeeDefaultsMutation as UpdateEmployeeDefaultsMutationType } from '@/relay/__generated__/UpdateEmployeeDefaultsMutation.graphql';

/**
 * Interface for the hook's return value
 */
export interface UseEmployeeDefaultsUpdaterResult {
  updateEmployeeDefaults: (
    employeeId: string,
    employeeGuid: string,
    data: EmployeeDefaultsUpdate
  ) => Promise<void>;
  isUpdating: boolean;
  error: Error | null;
}

/**
 * Hook for updating employee default settings
 * 
 * This hook provides a function to update employee defaults using proper
 * Relay mutation patterns. It handles multiple setting updates and provides
 * loading and error states.
 * 
 * Now uses dependency injection to access the TimesheetDetailService.
 * Requires the component to be wrapped with TimesheetDetailServiceProvider.
 * 
 * @returns Object containing update function, loading state, and error state
 * 
 * @example
 * ```tsx
 * const { updateEmployeeDefaults, isUpdating, error } = useEmployeeDefaultsUpdater();
 * 
 * const handleUpdate = async () => {
 *   await updateEmployeeDefaults(employeeId, employeeGuid, {
 *     defaultAgreementId: 123,
 *     defaultHourlyRate: 25.50
 *   });
 * };
 * ```
 */
export function useEmployeeDefaultsUpdater(): UseEmployeeDefaultsUpdaterResult {
  // Get service instance from context (dependency injection)
  const service = useTimesheetDetailService();
  
  const [commitMutation, isMutationInFlight] = useMutation<UpdateEmployeeDefaultsMutationType>(
    UpdateEmployeeSettingMutation
  );

  const updateEmployeeDefaults = async (
    employeeId: string,
    employeeGuid: string,
    data: EmployeeDefaultsUpdate
  ): Promise<void> => {
    // Prepare mutation inputs using the injected service
    const mutationInputs = service.prepareMutationInputs(
      employeeId,
      employeeGuid,
      data
    );

    if (mutationInputs.length === 0) {
      throw new Error('No valid update data provided');
    }

    // Execute mutations sequentially for each setting
    // Note: We could potentially optimize this to execute in parallel,
    // but sequential execution ensures proper error handling and order
    for (const input of mutationInputs) {
      await new Promise<void>((resolve, reject) => {
        commitMutation({
          variables: { input },
          onCompleted: (response, errors) => {
            if (errors) {
              reject(new Error(errors.map(e => e.message).join(', ')));
              return;
            }
            
            const result = response?.saveSetting?.operationResultOfSetting;
            if (!result?.success) {
              reject(new Error(result?.message || 'Failed to save setting'));
              return;
            }
            
            resolve();
          },
          onError: (error) => {
            reject(error);
          },
          // Optimistic update could be added here if needed
          // optimisticResponse: {...}
        });
      });
    }
  };

  return {
    updateEmployeeDefaults,
    isUpdating: isMutationInFlight,
    error: null // Error handling is done in the promise rejection
  };
}

export default useEmployeeDefaultsUpdater;