/**
 * Babel configuration specifically for <PERSON><PERSON> tests.
 * This setup ensures that <PERSON><PERSON> can correctly transpile TypeScript, JSX,
 * and any modern JavaScript features used in your tests or source code.
 */
module.exports = {
    presets: [
        [
            '@babel/preset-env', // Transforms ES6+ JavaScript into a more compatible version
            {
                targets: { node: 'current' } // Target the current version of Node.js running the tests
            }
        ],
        [
            '@babel/preset-react', // Enables Babel to transpile JSX and other React-specific syntax
            {
                runtime: 'automatic' // Uses the new JSX transform without needing to import React explicitly
            }
        ],
        '@babel/preset-typescript' // Add TypeScript preset to handle TS files directly in Babel
    ],
    plugins: [
        // Relay plugin for transforming GraphQL queries - moved to first position
        [
            'babel-plugin-relay',
            {
                schema: './src/relay/schema.graphql',
                eagerESModules: true // Add this to ensure proper module handling
            }
        ],

        // Transform Vite's import.meta.env to process.env for Jest compatibility
        'babel-plugin-transform-vite-meta-env',

        // Provides runtime helpers for Babel transformations, preventing code duplication.
        '@babel/plugin-transform-runtime',

        // Allows Babel to transform class properties (e.g., class fields, static properties).
        '@babel/plugin-transform-class-properties'
    ]
};
