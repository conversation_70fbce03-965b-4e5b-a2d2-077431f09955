using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

namespace backend.Services;

public class MailService(
    IOptions<MailSettings> mailSettings,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : IMailService
{
    private readonly MailSettings _mailSettings = mailSettings.Value;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    public async Task SendEmailAsync(MailRequest mailRequest, HttpRequest request)
    {
        var email = new MimeMessage();
        email.Sender = MailboxAddress.Parse(_mailSettings.Mail);
        email.From.Add(MailboxAddress.Parse(_mailSettings.Mail));
        foreach (string toEmail in mailRequest.ToEmail)
        {
            email.To.Add(MailboxAddress.Parse(toEmail));
        }
        foreach (string ccEmail in mailRequest.CCList)
        {
            email.Cc.Add(MailboxAddress.Parse(ccEmail));
        }
        email.ReplyTo.Add(MailboxAddress.Parse(AuthUtils.GetUserEmail(request.HttpContext)));
        email.Subject = mailRequest.Subject;
        var builder = new BodyBuilder();
        if (mailRequest.Attachments != null)
        {
            // TODO: Add attachments
        }
        builder.HtmlBody = mailRequest.Body;
        email.Body = builder.ToMessageBody();
        using var smtp = new SmtpClient();
        smtp.Connect(
            _mailSettings.Host,
            _mailSettings.Port,
            SecureSocketOptions.StartTlsWhenAvailable
        );
        if (mailRequest.UseAuth)
        {
            smtp.Authenticate(_mailSettings.Mail, _mailSettings.Password);
        }
        await smtp.SendAsync(email);
        smtp.Disconnect(true);
    }
}
