using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Extensions;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class RateTableDataExtractionController(
    ILogger<RateTableDataExtractionController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<RateTableDataExtractionController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet("site-area")]
    [Authorize]
    public ActionResult<IEnumerable<SiteArea>> GetSiteAreas([FromQuery] OptionalGuidQuery query)
    {
        Guid ThirdPartyGUID = AuthUtils.GetOrgGUID(query, Request);
        int ThirdPartyID = _db.Roots.Where((r) => r.Guid == ThirdPartyGUID).First().Id;
        Organization ThirdParty = _db.Organizations.Where((o) => o.Id == ThirdPartyID).First();
        if (ThirdParty.DorganizationTypeId == 2) // 2 means the organization is a site sponsor
        {
            return _db
                .Organizations.Where((o) => o.Id == ThirdPartyID)
                .Select((o) => new SiteArea { ID = o.Id, Name = o.Name })
                .ToList();
        }
        else
        {
            // 4 is the relationship type ID for Chapter to Fund Admin
            return _db
                .Relationships.Where(
                    (r) => r.RightPartyId == ThirdPartyID && r.DrelationshipTypeId == 4
                )
                .Join(
                    _db.Organizations,
                    r => r.LeftPartyId,
                    o => o.Id,
                    (r, o) => new SiteArea { ID = o.Id, Name = o.Name }
                )
                .ToList();
        }
    }

    [HttpGet("agreements")]
    [Authorize]
    public ActionResult<IEnumerable<RateExtractAgreement>> GetAgreements(
        [FromQuery] RateTableExtractAgreementsQuery query
    )
    {
        return _db
            .Agreements.Where((a) => a.ChapterId == query.ChapterID)
            .Join(
                _db.RateSchedules,
                a => a.Id,
                r => r.AgreementId,
                (a, r) =>
                    new
                    {
                        ID = a.Id,
                        a.Name,
                        r.EffectiveStartDate,
                        r.EffectiveEndDate,
                    }
            )
            .Where(
                (r) =>
                    query.WorkMonth >= r.EffectiveStartDate
                    && (!r.EffectiveEndDate.HasValue || query.WorkMonth <= r.EffectiveEndDate)
            )
            .Select((r) => new RateExtractAgreement { ID = r.ID, Name = r.Name })
            .ToList();
    }

    [HttpPost]
    [Authorize]
    public ActionResult<IEnumerable<RateTableExtractReportResult>> GetReportResults(
        [FromBody] RateTableExtractReportBody body
    )
    {
        return _db
            .Agreements.Where((a) => body.AgreementIDs.Contains(a.Id))
            .Join(
                _db.RateSchedules,
                a => a.Id,
                r => r.AgreementId,
                (a, r) =>
                    new
                    {
                        Agreement = a.Name,
                        RateScheduleID = r.Id,
                        r.EffectiveStartDate,
                        r.EffectiveEndDate,
                    }
            )
            .Where(
                (r) =>
                    body.WorkMonth >= r.EffectiveStartDate
                    && (!r.EffectiveEndDate.HasValue || body.WorkMonth <= r.EffectiveEndDate)
            )
            .Join(
                _db.Rates,
                (rs) => rs.RateScheduleID,
                (r) => r.RateScheduleId,
                (rs, r) =>
                    new
                    {
                        Agreement = rs.Agreement,
                        Value = r.Value.HasValue
                            ? r.Value.Value.ToString("0.##")
                            : r.CustomCalculation,
                        Minimum = r.MinimumBound,
                        Maximum = r.MaximumBound,
                        BenefitID = r.BenefitId,
                        ClassificationNameID = r.ClassificationNameId,
                        SubClassificationID = r.SubClassificationId,
                        DCalculationMethodID = r.DcalculationMethodId,
                        DCalculationModifierID = r.DcalculationModifierId,
                    }
            )
            .Where((r) => r.BenefitID != 2)
            .Join(
                _db.ClassificationNames,
                r => r.ClassificationNameID,
                c => c.Id,
                (r, c) =>
                    new
                    {
                        r.Agreement,
                        Classification = c.Name,
                        r.Value,
                        r.Minimum,
                        r.Maximum,
                        r.BenefitID,
                        r.SubClassificationID,
                        r.DCalculationMethodID,
                        r.DCalculationModifierID,
                    }
            )
            .LeftJoin(
                _db.SubClassifications,
                r => r.SubClassificationID,
                s => s.Id,
                (r, s) =>
                    new
                    {
                        r.Agreement,
                        r.Classification,
                        SubClassification = s.Name,
                        r.Value,
                        r.Minimum,
                        r.Maximum,
                        r.BenefitID,
                        r.DCalculationMethodID,
                        r.DCalculationModifierID,
                    }
            )
            .Join(
                _db.Benefits,
                r => r.BenefitID,
                b => b.Id,
                (r, b) =>
                    new
                    {
                        r.Agreement,
                        r.Classification,
                        r.SubClassification,
                        Benefit = b.Name,
                        r.Value,
                        r.Minimum,
                        r.Maximum,
                        r.DCalculationMethodID,
                        r.DCalculationModifierID,
                    }
            )
            .Join(
                _db.DcalculationMethods,
                r => r.DCalculationMethodID,
                d => d.Id,
                (r, d) =>
                    new
                    {
                        r.Agreement,
                        r.Classification,
                        r.SubClassification,
                        r.Benefit,
                        r.Value,
                        r.Minimum,
                        r.Maximum,
                        Calculation = d.Name,
                        r.DCalculationModifierID,
                    }
            )
            .Join(
                _db.DcalculationModifiers,
                r => r.DCalculationModifierID,
                d => d.Id,
                (r, d) =>
                    new RateTableExtractReportResult
                    {
                        Agreement = r.Agreement,
                        Classification = r.Classification,
                        SubClassification = r.SubClassification,
                        Benefit = r.Benefit,
                        Value = r.Value,
                        Minimum = r.Minimum,
                        Maximum = r.Maximum,
                        Calculation = r.Calculation,
                        CalculationModifier = d.Name,
                    }
            )
            .OrderBy(r => r.Agreement)
            .ThenBy(r => r.Benefit)
            .ThenBy(r => r.Classification)
            .ToList();
    }

    public class SiteArea
    {
        public int ID { get; set; }
        public required string Name { get; set; }
    }

    public class RateExtractAgreement
    {
        public int ID { get; set; }
        public required string Name { get; set; }
    }

    public class RateTableExtractAgreementsQuery
    {
        public int ChapterID { get; set; }
        public DateTime WorkMonth { get; set; }
    }

    public class RateTableExtractReportResult
    {
        public required string Agreement { get; set; }
        public required string Classification { get; set; }
        public string? SubClassification { get; set; }
        public required string Benefit { get; set; }
        public string? Value { get; set; }
        public required string Calculation { get; set; }
        public required string CalculationModifier { get; set; }
        public decimal? Minimum { get; set; }
        public decimal? Maximum { get; set; }
    }

    public class RateTableExtractReportBody
    {
        public DateTime WorkMonth { get; set; }
        public required int[] AgreementIDs { get; set; }
    }
}
