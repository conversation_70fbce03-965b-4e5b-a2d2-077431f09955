using backend.Data.DTOs; // Added for ThirdPartyInfoDto
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore; // Added for ToListAsync

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class OrganizationsController(
    ILogger<OrganizationsController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<OrganizationsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet("logo")]
    [Authorize]
    public OrgLogoResponse GetOrganizationLogo([FromQuery] OptionalGuidQuery query)
    {
        Organization org = GetOrganization(query);
        return new OrgLogoResponse
        {
            LogoFileName =
                org.LogoFilePath != null
                    ? string.Format(
                        "{0}://{1}{2}/UploadedImages/{3}",
                        Request.Scheme,
                        Request.Host,
                        Request.PathBase,
                        org.LogoFilePath
                    )
                    : null,
        };
    }

    [HttpPost("logo")]
    [Authorize]
    public async Task<IActionResult> SaveOrganizationLogo([FromForm] SaveLogoBody body)
    {
        Organization org = GetOrganization(body);
        DeleteExistingOrgLogo(org);
        org.LogoFilePath = await SaveImage(body.FormFile);
        _db.SaveChanges();
        return Ok();
    }

    private Organization GetOrganization(OptionalGuidQuery query)
    {
        Guid orgGuid = AuthUtils.GetOrgGUID(query, Request);
        int orgId = _db.Roots.Where(r => r.Guid == orgGuid).First().Id;
        return _db.Organizations.Where(o => o.Id == orgId).First();
    }

    private async Task<string> SaveImage(IFormFile file)
    {
        string imageName =
            new string(
                System.IO.Path.GetFileNameWithoutExtension(file.FileName).Take(10).ToArray()
            ).Replace(' ', '-')
            + DateTime.Now.ToString("yymmssfff")
            + System.IO.Path.GetExtension(file.FileName);
        var imagePath = System.IO.Path.Combine(_env.ContentRootPath, "UploadedImages", imageName);
        using (var fileStream = new FileStream(imagePath, FileMode.Create))
        {
            await file.CopyToAsync(fileStream);
        }
        return imageName;
    }

    // Corrected implementation: Accepts REQUIRED chapterId and filters Unions, Fund Admins, and non-limited Chapters
    [HttpGet("all")]
    [Authorize]
    public async Task<ActionResult<IEnumerable<ThirdPartyInfoDto>>> GetAllOrganizations(
        [FromQuery] int chapterId,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Get IDs of organizations already linked to the current chapterId
            var linkedOrgIds = await _db
                .Relationships.Where(r =>
                    r.LeftPartyId == chapterId
                    && (r.DrelationshipTypeId == 3 || r.DrelationshipTypeId == 4)
                ) // Relationship types for Union/FundAdmin
                .Select(r => r.RightPartyId) // Select the ID of the linked organization
                .Distinct()
                .ToListAsync(cancellationToken);

            // --- Queries for each organization type ---

            // Base query for Unions
            IQueryable<ThirdPartyInfoDto> unionQuery =
                from unions in _db.Unions
                join organizations in _db.Organizations on unions.Id equals organizations.Id
                join root in _db.Roots on unions.Id equals root.Id
                select new ThirdPartyInfoDto
                {
                    Value = unions.Id,
                    Label = organizations.Name,
                    Guid = root.Guid,
                    Id = unions.Id,
                    IsLinked = linkedOrgIds.Contains(unions.Id), // Set IsLinked based on linkedOrgIds
                };

            // Base query for Fund Administrators
            IQueryable<ThirdPartyInfoDto> fundAdminQuery =
                from fundAdmin in _db.FundAdministrators
                join organizations in _db.Organizations on fundAdmin.Id equals organizations.Id
                join root in _db.Roots on fundAdmin.Id equals root.Id
                select new ThirdPartyInfoDto
                {
                    Value = fundAdmin.Id,
                    Label = organizations.Name,
                    Guid = root.Guid,
                    Id = fundAdmin.Id,
                    IsLinked = linkedOrgIds.Contains(fundAdmin.Id), // Set IsLinked based on linkedOrgIds
                };

            // Base query for non-limited Chapters (excluding the current chapter)
            IQueryable<ThirdPartyInfoDto> chapterQuery =
                from chapter in _db.Chapters
                where chapter.Limited == false && chapter.Id != chapterId
                join organizations in _db.Organizations on chapter.Id equals organizations.Id
                join root in _db.Roots on chapter.Id equals root.Id
                select new ThirdPartyInfoDto
                {
                    Value = chapter.Id,
                    Label = organizations.Name,
                    Guid = root.Guid,
                    Id = chapter.Id,
                    IsLinked = linkedOrgIds.Contains(chapter.Id), // Set IsLinked based on linkedOrgIds
                };

            // Combine all potential organizations
            var combinedQuery = unionQuery.Concat(fundAdminQuery).Concat(chapterQuery);

            // Execute the final query and order results
            List<ThirdPartyInfoDto> resultList = await combinedQuery
                .OrderBy(x => x.Label)
                .ToListAsync(cancellationToken);

            return Ok(resultList);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error fetching organizations for chapterId {ChapterId}",
                chapterId
            );
            return StatusCode(
                StatusCodes.Status500InternalServerError,
                "An error occurred while fetching organizations."
            );
        }
    }

    private void DeleteExistingOrgLogo(Organization org)
    {
        if (org.LogoFilePath != null)
        {
            var imagePath = System.IO.Path.Combine(
                _env.ContentRootPath,
                "UploadedImages",
                org.LogoFilePath
            );
            if (System.IO.File.Exists(imagePath))
            {
                System.IO.File.Delete(imagePath);
            }
        }
    }

    public class SaveLogoBody : OptionalGuidQuery
    {
        public required string FileName { get; set; }
        public required IFormFile FormFile { get; set; }
    }

    public class OrgLogoResponse
    {
        public string? LogoFileName { get; set; }
    }
}
