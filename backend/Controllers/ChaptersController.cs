using backend.Data.DTOs;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ChaptersController(
    ILogger<ChaptersController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<ChaptersController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public ActionResult<ChapterDTO> GetChapter([FromQuery] OptionalGuidQuery query)
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        Chapter? matchingChapter = _db.Chapters.Where(c => c.Id == ChapterID).FirstOrDefault();
        if (matchingChapter == null)
        {
            return NotFound();
        }
        return new ChapterDTO
        {
            Id = matchingChapter.Id,
            EmployerAssociationId = matchingChapter.EmployerAssociationId,
            EmployeeAssociationId = matchingChapter.EmployeeAssociationId,
            Limited = matchingChapter.Limited,
        };
    }

    [HttpGet("roster")]
    [Authorize]
    public IEnumerable<ChaptersInfoDto> GetChapterRosterItems()
    {
        return _db
            .Chapters.Join(_db.Organizations, c => c.Id, o => o.Id, (c, o) => new { c.Id, o.Name })
            .Join(
                _db.Roots,
                o => o.Id,
                r => r.Id,
                (o, r) =>
                    new ChaptersInfoDto
                    {
                        Value = o.Id,
                        Guid = r.Guid,
                        Label = o.Name,
                    }
            );
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> CreateChapter([FromBody] CreateChapterRequestBody body)
    {
        Organization? matchingChapter = await _db.Organizations.SingleOrDefaultAsync(r =>
            r.Name == body.Name && r.DorganizationTypeId == 2
        );
        if (matchingChapter != null)
        {
            return Conflict("Chapter already exists");
        }

        string UserName = AuthUtils.GetUsername(Request.HttpContext);
        DateTime LastModificationDate = DateTime.UtcNow;
        await _db.Database.ExecuteSqlRawAsync(
            "exec [Core].[ChapterSave] @GUID, @sUserName, @LastModificationDate, @Name, @Limited",
            new SqlParameter("@GUID", body.GUID ?? (object)DBNull.Value),
            new SqlParameter("@sUserName", UserName),
            new SqlParameter("@LastModificationDate", LastModificationDate),
            new SqlParameter("@Name", body.Name),
            new SqlParameter("@Limited", body.Limited)
        );
        return Ok();
    }

    public class ChapterDTO
    {
        public int Id { get; set; }

        public string? EmployerAssociationId { get; set; }

        public string? EmployeeAssociationId { get; set; }

        public bool Limited { get; set; }
    }

    public class CreateChapterRequestBody : OptionalGuidQuery
    {
        public bool Limited { get; set; }
        public required string Name { get; set; }
    }
}
