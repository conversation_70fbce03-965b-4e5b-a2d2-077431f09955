using System.Data;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class AgreementsController(
    ILogger<AgreementsController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<AgreementsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet("signatory")]
    [Authorize]
    public IEnumerable<Agreement> GetSignatoryAgreements([FromQuery] SignatoryAgreementQuery query)
    {
        Guid employerGuid = query.EmployerGuid.HasValue
            ? query.EmployerGuid.Value
            : AuthUtils.GetUserOrgGUID(Request.HttpContext);
        bool IncludeInactiveAgreements = query.IncludeInactiveAgreements.HasValue
            ? query.IncludeInactiveAgreements.Value
            : false;
        List<int> timeSheetSubscriberIDs = _db
            .ServiceSubscriptions.Where((s) => s.SubscriptionServiceId == 1)
            .Select((s) => s.PartyId)
            .ToList();

        return
        [
            .. _db
                .Roots.Where(r => r.Guid == employerGuid)
                .Join(
                    _db.EmployersToAgreements,
                    root => root.Id,
                    employerToAgreement => employerToAgreement.EmployerId,
                    (root, employerToAgreement) => employerToAgreement
                )
                .Join(
                    _db.Agreements,
                    employerToAgreement => employerToAgreement.AgreementId,
                    agreement => agreement.Id,
                    (employerToAgreement, agreement) => agreement
                )
                .Where(
                    (a) =>
                        timeSheetSubscriberIDs.Contains(a.ChapterId)
                        && (
                            IncludeInactiveAgreements
                            || a.EffectiveEndDate == null
                            || a.EffectiveEndDate > DateTime.Now
                        )
                ),
        ];
    }

    // While it would be more semantically consistent for this to be a GET, it's possible for the number of employer IDs being sent to be so large that
    // the query string length causes a network error
    [HttpPost("by-employer")]
    [Authorize]
    public IEnumerable<AgreementSimpleId> GetAgreementsByEmployer(
        [FromBody] AgreementsByEmployerQuery query
    )
    {
        int chapterId = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);

        var employerIdsParam = new SqlParameter("@employerIds", SqlDbType.VarChar)
        {
            Value = query.agreementXml,
        };
        var yearParam = new SqlParameter("@year", SqlDbType.Int)
        {
            Value = query.year.HasValue ? (object)query.year.Value : DBNull.Value,
        };
        var chapterIdParam = new SqlParameter("@chapterID", SqlDbType.Int) { Value = chapterId };

        return _db
            .AgreementsSimpleId.FromSqlRaw(
                "exec [Core].[AgreementsByEmployerSelect] @employerIds, @year, @chapterID",
                employerIdsParam,
                yearParam,
                chapterIdParam
            )
            .AsEnumerable()
            .OrderBy(a => a.Name);
    }

    public class AgreementsByEmployerQuery : OptionalGuidQuery
    {
        public required string agreementXml { get; set; }
        public int? year { get; set; }
    }
}
