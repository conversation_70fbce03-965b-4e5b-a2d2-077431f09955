using backend.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthCheckController : ControllerBase
    {
        private readonly KafkaHealthService _kafkaHealthService;

        public HealthCheckController(KafkaHealthService kafkaHealthService)
        {
            _kafkaHealthService = kafkaHealthService;
        }

        [HttpGet("kafka")]
        [Authorize]
        [ResponseCache(Duration = 5)] // Cache the result for 5 seconds
        public async Task<IActionResult> CheckKafkaStatus()
        {
            var isRunning = await _kafkaHealthService.IsKafkaRunningAsync();
            return Ok(isRunning);
        }
    }
}
