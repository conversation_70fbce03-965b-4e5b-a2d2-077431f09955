using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Security;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EFTPaymentMethodsController(
    ILogger<KBAController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<KBAController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public List<EftpaymentMethod> GetEFTPaymentMethods([FromQuery] OptionalGuidQuery query)
    {
        Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);
        List<EftpaymentMethod> EFTPaymentMethods = _db
            .EftpaymentMethods.Where((p) => p.AssociatedGuid == userGuid)
            .ToList();
        foreach (EftpaymentMethod eftPaymentMethod in EFTPaymentMethods)
        {
            try
            {
                string? decryptedRoutingNumber = Encryption.Decrypt(
                    eftPaymentMethod.RoutingNumber!,
                    _db
                );
                eftPaymentMethod.RoutingNumber =
                    decryptedRoutingNumber ?? eftPaymentMethod.RoutingNumber;
                string? decryptedAccountNumber = Encryption.Decrypt(
                    eftPaymentMethod.AccountNumber!,
                    _db
                );
                eftPaymentMethod.AccountNumber =
                    decryptedAccountNumber ?? eftPaymentMethod.AccountNumber;
            }
            catch (Exception)
            {
                //Routing and account numbers already decrypted, nothing to do.
            }
        }
        return EFTPaymentMethods;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> SaveEFTPaymentMethod([FromBody] EFTPaymentSaveBody body)
    {
        Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);
        await _db.Database.ExecuteSqlRawAsync(
            "exec [Core].[EFTPaymentMethodSave] @sUserName, @AssociatedGUID, @RoutingNumber, @AccountNumber",
            new SqlParameter("@sUserName", body.Username),
            new SqlParameter("@AssociatedGUID", userGuid),
            new SqlParameter("@RoutingNumber", body.RoutingNumber),
            new SqlParameter("@AccountNumber", body.AccountNumber)
        );
        return Ok();
    }

    [HttpDelete]
    [Authorize]
    public IActionResult DeleteEFTPaymentMethod([FromQuery] OptionalGuidQuery query)
    {
        Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);
        _db.Database.ExecuteSqlRaw(
            "exec [Core].[EFTPaymentMethodDelete] @sUserName, @AssociatedGUID",
            new SqlParameter("@sUserName", query.Username),
            new SqlParameter("@AssociatedGUID", userGuid)
        );
        return Ok();
    }

    public class EFTPaymentSaveBody : OptionalGuidQuery
    {
        public required string RoutingNumber { get; set; }
        public required string AccountNumber { get; set; }
    }
}
