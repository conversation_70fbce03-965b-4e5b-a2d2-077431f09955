using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class UsersController(
    ILogger<UsersController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<UsersController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpPost]
    [Authorize]
    public IActionResult SaveUser([FromBody] UserSaveBody body)
    {
        var userGuid = AuthUtils.GetUserGuid(HttpContext);

        AspnetMembership membership = _db
            .AspnetMemberships.Where((m) => m.UserId == userGuid)
            .First();
        membership.Email = body.Email;
        membership.LoweredEmail = body.Email.ToLower();
        Epruser eprUser = _db.Eprusers.Where((u) => u.AspnetUserId == userGuid).First();
        if (!string.IsNullOrEmpty(body.FirstName))
        {
            eprUser.FirstName = body.FirstName;
        }
        if (!string.IsNullOrEmpty(body.LastName))
        {
            eprUser.LastName = body.LastName;
        }
        eprUser.PhoneNumber = body.PhoneNumber;
        eprUser.PhoneNumberExtension = body.PhoneNumberExtension;
        eprUser.PhoneTypeId = body.PhoneTypeID;
        eprUser.PreferredMfamethod = body.PreferredMFAMethod;
        _db.AspnetMemberships.Update(membership);
        _db.Eprusers.Update(eprUser);
        _db.SaveChanges();
        return Ok();
    }

    public class UserSaveBody : OptionalGuidQuery
    {
        public required string Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PhoneNumberExtension { get; set; }
        public int PhoneTypeID { get; set; }
        public string? PreferredMFAMethod { get; set; }
    }
}
