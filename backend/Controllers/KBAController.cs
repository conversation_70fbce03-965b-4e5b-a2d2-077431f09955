using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore; // Add this using directive

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class KBAController(
    ILogger<KBAController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<KBAController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public ActionResult<List<Kbaquestion>> GetQuestions()
    {
        return _db.Kbaquestions.ToList();
    }

    [HttpGet("answers")]
    [Authorize]
    public ActionResult<List<Kbaanswer>> GetAnswers([FromQuery] OptionalGuidQuery query)
    {
        Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);
        return _db.Kbaanswers.Where((a) => a.UserGuid == userGuid).ToList();
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> SaveAnswers([FromBody] KBAAsnwerSaveBody body)
    {
        foreach (KBAAnswerNoID userAnswer in body.Answers)
        {
            Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);
            Kbaanswer? answer = _db
                .Kbaanswers.Where(
                    (a) => a.UserGuid == userGuid && a.Category == userAnswer.Category
                )
                .FirstOrDefault();
            if (answer == null)
            {
                answer = new Kbaanswer
                {
                    UserGuid = userGuid,
                    Category = userAnswer.Category,
                    QuestionId = userAnswer.QuestionId,
                    Answer = userAnswer.Answer,
                };
                _db.Kbaanswers.Add(answer);
            }
            else
            {
                answer.QuestionId = userAnswer.QuestionId;
                answer.Answer = userAnswer.Answer;
                _db.Kbaanswers.Update(answer);
            }
        }
        await _db.SaveChangesAsync();
        return Ok();
    }

    [HttpDelete("answers")] // Reverted back to DELETE method
    [Authorize]
    public async Task<IActionResult> RemoveAnswers([FromQuery] OptionalGuidQuery query)
    {
        Guid userGuid = AuthUtils.GetUserGuid(Request.HttpContext);

        try
        {
            var answersToRemove = await _db
                .Kbaanswers.Where(a => a.UserGuid == userGuid)
                .ToListAsync();

            if (answersToRemove.Any())
            {
                _db.Kbaanswers.RemoveRange(answersToRemove);
                await _db.SaveChangesAsync();
                _logger.LogInformation(
                    "Successfully removed KBA answers for user {@userGuid}",
                    userGuid
                );
                return Ok("Security questions removed successfully.");
            }
            else
            {
                _logger.LogInformation(
                    "No KBA answers found to remove for user {@userGuid}",
                    userGuid
                );
                return Ok("No security questions found to remove."); // Or NotFound() if preferred
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing KBA answers for query {@userGuid}", userGuid);
            // Consider returning a more specific error based on the exception if needed
            return StatusCode(
                StatusCodes.Status500InternalServerError,
                "An error occurred while removing security questions."
            );
        }
    }

    public class KBAAsnwerSaveBody : OptionalGuidQuery
    {
        public required List<KBAAnswerNoID> Answers { get; set; }
    }

    public class KBAAnswerNoID
    {
        public int Category { get; set; }
        public int QuestionId { get; set; }
        public required string Answer { get; set; }
    }
}
