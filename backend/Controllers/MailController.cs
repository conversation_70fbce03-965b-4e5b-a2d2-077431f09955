using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Services;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class MailController(
    ILogger<MailController> logger,
    EPRLiveDBContext db,
    IMailService mailService,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<MailController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IMailService _mailService = mailService;
    private readonly IWebHostEnvironment _env = env;

    [HttpPost("send")]
    [Authorize]
    public async Task<IActionResult> Send([FromBody] MailRequest mailRequest)
    {
        try
        {
            await _mailService.SendEmailAsync(mailRequest, Request);
            return Ok();
        }
        catch (Exception ex)
        {
            _db.LoggedEvents.Add(
                new LoggedEvent
                {
                    EventDateTime = DateTime.Now,
                    DeventTypeId = (int)Constants.EventTypes.DelinquencyLetterError,
                    DeventSubTypeId = 0,
                    UserName = AuthUtils.GetUsername(Request.HttpContext),
                    Target = null,
                    Notes = ex.Message,
                }
            );
            _db.SaveChanges();
            return StatusCode(500, ex.Message);
        }
    }
}
