using System.Data;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EmployeesController : ControllerBase
{
    private readonly ILogger<EmployeesController> _logger;
    private readonly EPRLiveDBContext _db;
    private readonly IWebHostEnvironment _env;

    public EmployeesController(
        ILogger<EmployeesController> logger,
        EPRLiveDBContext db,
        IWebHostEnvironment env
    )
    {
        _logger = logger;
        _db = db;
        _env = env;
    }

    [HttpGet("employer")]
    [Authorize]
    public IEnumerable<EmployeeInfo> GetEmployees([FromQuery] EmployeesQuery query)
    {
        Guid employerGuid = query.EmployerGuid.HasValue
            ? query.EmployerGuid.Value
            : AuthUtils.GetUserOrgGUID(Request.HttpContext);

        var employerGuidParam = new SqlParameter("@EmployerGuid", SqlDbType.UniqueIdentifier)
        {
            Value = employerGuid,
        };

        var result = _db.Database.SqlQueryRaw<EmployeeInfo>(
            $"EXEC Core.EmployeesSelectRoster @EmployerGuid",
            employerGuidParam
        );

        return result.ToList();
    }
}

public class EmployeeInfo
{
    public int ID { get; set; }
    public Guid GUID { get; set; }
    public string? EmployeeID { get; set; }
    public string? Name { get; set; }
    public string? FirstName { get; set; }
    public string? MiddleName { get; set; }
    public required string LastName { get; set; }
    public string? Suffix { get; set; }
    public string? SocialSecurityNumber { get; set; }
    public bool Active { get; set; }
}
