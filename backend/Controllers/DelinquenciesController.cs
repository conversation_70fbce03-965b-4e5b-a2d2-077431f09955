using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using HTMLQuestPDF.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class DelinquenciesController(
    ILogger<DelinquenciesController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<DelinquenciesController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet("payments")]
    [Authorize]
    public ActionResult<IEnumerable<DelinquencyPayment>> GetPayments(
        [FromQuery] DelinquencyPaymentsQuery query
    )
    {
        try
        {
            string IncludeSuppressedReportsString = query.IncludeSuppressedReports ? "1" : "0";
            string IncludeCABenefits = query.IncludeCABenefits ? "1" : "0";
            return _db
                .DelinquencyPayments.FromSqlRaw(
                    "Core.ReportsSelectForPaymentDelinquency @FundAdministratorGUID, @IncludeSuppressedReports, @StartDate, @EndDate, @IncludeCABenefits",
                    new SqlParameter("@FundAdministratorGUID", query.FundAdministratorGUID),
                    new SqlParameter("@IncludeSuppressedReports", IncludeSuppressedReportsString),
                    new SqlParameter("@StartDate", query.StartDate),
                    new SqlParameter("@EndDate", query.EndDate),
                    new SqlParameter("@IncludeCABenefits", IncludeCABenefits)
                )
                .ToList();
        }
        catch (Microsoft.Data.SqlClient.SqlException e)
        {
            if (e.Message.Contains("Timeout"))
            {
                return StatusCode(408);
            }
            else
            {
                throw;
            }
        }
    }

    [HttpGet("payments/letter")]
    [Authorize]
    public DelinquencyLetter? GetPaymentLetter([FromQuery] OptionalGuidQuery query)
    {
        Guid orgGuid = AuthUtils.GetOrgGUID(query, Request);
        return _db
            .DelinquencyLetters.Where((d) => d.OrgId == orgGuid && d.TypeId == 1)
            .FirstOrDefault();
    }

    [HttpPost("payments/letter")]
    [Authorize]
    public IActionResult SavePaymentLetter([FromBody] DelinquencyLetterModel model)
    {
        Guid orgGuid = AuthUtils.GetOrgGUID(model, Request);

        DelinquencyLetter? letter = _db
            .DelinquencyLetters.Where((d) => d.OrgId == orgGuid && d.TypeId == 1)
            .FirstOrDefault();
        if (letter == null)
        {
            letter = new DelinquencyLetter
            {
                Id = Guid.NewGuid(),
                OrgId = orgGuid,
                TypeId = 1,
            };
            _db.DelinquencyLetters.Add(letter);
        }
        letter.Text = model.Text;
        _db.SaveChanges();
        return Ok();
    }

    [HttpPost("payments/settings")]
    [Authorize]
    public IActionResult SavePaymentSettings([FromBody] DelinquencySettingsModel model)
    {
        Guid orgGuid = AuthUtils.GetOrgGUID(model, Request);

        DelinquencyLetter? letter = _db
            .DelinquencyLetters.Where((d) => d.OrgId == orgGuid && d.TypeId == 1)
            .FirstOrDefault();
        if (letter != null)
        {
            letter.SendToEmployer = model.SendToEmployer.HasValue ? model.SendToEmployer : false;
            letter.SendToPayrollContact = model.SendToPayrollContact.HasValue
                ? model.SendToPayrollContact
                : false;
            letter.SendToPrimaryContact = model.SendToPrimaryContact.HasValue
                ? model.SendToPrimaryContact
                : false;
            letter.Cclist = model.CCList;
            _db.SaveChanges();
        }
        return Ok();
    }

    [HttpPost("pdf")]
    [Authorize]
    public IActionResult GetLettersPdf([FromBody] DelinquencyLetterPdfModel model)
    {
        QuestPDF.Settings.License = LicenseType.Community;

        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.MarginVertical(30);
                page.MarginHorizontal(20);

                page.DefaultTextStyle(style => style.FontSize(11).FontFamily("Arial"));

                foreach (string letter in model.Letters)
                {
                    page.Content()
                        .Column(col =>
                        {
                            col.Item()
                                .HTML(handler =>
                                {
                                    handler.SetHtml(letter);

                                    handler.SetContainerStyleForHtmlElement(
                                        "table",
                                        c => c.PaddingHorizontal(6).PaddingVertical(12)
                                    );
                                    handler.SetContainerStyleForHtmlElement(
                                        "td",
                                        c => c.PaddingHorizontal(3).PaddingVertical(3)
                                    );
                                    handler.SetContainerStyleForHtmlElement(
                                        "p",
                                        c => c.PaddingHorizontal(6).PaddingVertical(8)
                                    );
                                });
                        });
                }
            });
        });

        byte[] pdfBytes;
        using (var ms = new MemoryStream())
        {
            document.GeneratePdf(ms);
            pdfBytes = ms.ToArray();
        }

        return File(pdfBytes, "application/pdf");
    }
}
