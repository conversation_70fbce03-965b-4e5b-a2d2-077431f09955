using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class TimeSheetsController : ControllerBase
{
    private readonly EPRLiveDBContext _db;

    public TimeSheetsController(EPRLiveDBContext db)
    {
        _db = db;
    }
    // Site Sponsor IDs
    private const int zumaIBEWLocal3ID = 37525251;

    // Benefit IDs
    private const int STHoursBenefitID = 63787;
    private const int OTHoursBenefitID = 67035;
    private const int DTHoursBenefitID = 67036;
    private const int HoursWorkedBenefitID = 2;
    private const int GrossWagesBenefitID = 3;
    private const int WorkingWagesBenefitID = 974114;
    private const int HourlyRateBenefitID = 18400824;
    private const int JobCodeBenefitID = 38257037;
    private const int OTGrossBenefitID = 37525442;
    private const int TotalDaysWorkedBenefitID = 37525440;
    private const int TotalGrossWagesBenefitID = 38331637;
    private const int CostCenterBenefitID = 44146546;

    [HttpGet("export/{timesheetId}")]
    [Authorize]
    public IEnumerable<TimeSheetExportLineItem> GetISolvedExport(Guid timesheetId)
    {
        var result = _db.TimeSheetExportLineItems.FromSql($"dbo.TimeSheetsExport {timesheetId}").ToList();
        return result;
    }

    [HttpGet("export/employeedeductiblebenefits/{timesheetId}")]
    [Authorize]
    public IEnumerable<TimeSheetEmployeeDeductibleBenefit> GetEmployeeDeductibleBenefits(Guid timesheetId)
    {
        return _db.TimeSheetEmployeeDeductibleBenefits.FromSql($"dbo.TimeSheetsSelectEmployeeDeductibleBenefits {timesheetId}").ToList();
    }

    [HttpGet("export/unionemployerpaidbenefits/{timesheetId}")]
    [Authorize]
    public IEnumerable<TimeSheetUnionEmployerPaidBenefit> GetUnionEmployerPaidBenefits(Guid timesheetId)
    {
        return _db.TimeSheetUnionEmployerPaidBenefits.FromSql($"dbo.TimesheetEmployerUnionPaidBenefits {timesheetId}").ToList();
    }

    [HttpGet("export/taxablebenefits/{timesheetId}")]
    [Authorize]
    public IEnumerable<TimeSheetEmployeeDeductibleBenefit> GetTaxableBenefits(Guid timesheetId)
    {
        return _db.TimeSheetEmployeeDeductibleBenefits.FromSql($"dbo.TimesheetTaxableBenefits {timesheetId}").ToList();
    }

    // First, let's define the TimeSheetToReportModel as it's not visible in the provided code
    public class TimeSheetToReportModel
    {
        // Change from Guid to int
        public int TimeSheetID { get; set; }
    }

    [HttpPost("savereports")]
    [Authorize]
    public IActionResult SaveReports([FromBody] TimeSheetToReportModel model)
    {
        List<PayStubAndDetails> payStubDetails = GetPayStubsAndDetails(model.TimeSheetID);
        CalculateDaysWorked(payStubDetails);

        List<TimeSheetToReportLineItem> timeSheetLineItems = GetLineItems(payStubDetails);
        List<EmployeeBenefitSingleLineItemMap> SingleLineItemBenefits = GetSingleLineItemBenefits(
            timeSheetLineItems
        );

        try
        {
            foreach (TimeSheetToReportLineItem lineItem in timeSheetLineItems)
            {
                Guid reportGUID = SaveReport(lineItem);

                Guid reportLineItemGUID = SaveReportLineItem(lineItem, reportGUID);

                SaveReportLineItemDetails(lineItem, reportLineItemGUID);

                SaveSingleLineItemBenefits(SingleLineItemBenefits, lineItem, reportLineItemGUID);
            }
        }
        catch (Exception e)
        {
            throw new ApplicationException(
                "Error occurred while saving timesheet lineitems.  " + e.Message
            );
        }

        return Ok();
    }

    private List<PayStubAndDetails> GetPayStubsAndDetails(int timeSheetID)
    {
        return _db.TimeSheets.Where(ts => ts.Id == timeSheetID).Join(
            _db.PayStubs,
            timeSheet => timeSheet.Id,
            payStub => payStub.TimeSheet.Id,
            (timeSheet, payStub) => new
            {
                Id = payStub.Id,
                EmployerGuid = timeSheet.EmployerGuid,
                EmployeeId = payStub.EmployeeId,
                WorkPeriod = timeSheet.PayPeriodEndDate
            }
        ).Join(
            _db.PayStubDetails,
            payStub => payStub.Id,
            payStubDetail => payStubDetail.PayStub.Id,
            (payStub, payStubDetail) => new PayStubAndDetails
            {
                PayStubID = payStub.Id,
                EmployerGUID = payStub.EmployerGuid,
                EmployeeID = payStub.EmployeeId,
                AgreementID = payStubDetail.AgreementId,
                ClassificationID = payStubDetail.ClassificationId,
                SubClassificationID = payStubDetail.SubClassificationId,
                JobCode = payStubDetail.JobCode,
                CostCenter = payStubDetail.CostCenter,
                STHours = payStubDetail.STHours,
                OTHours = payStubDetail.OTHours,
                DTHours = payStubDetail.DTHours,
                HourlyRate = payStubDetail.HourlyRate,
                TotalHours = payStubDetail.TotalHours,
                WorkPeriod = payStub.WorkPeriod.HasValue 
                    ? new DateTimeOffset(payStub.WorkPeriod.Value.ToDateTime(TimeOnly.MinValue)) 
                    : null,
                WorkDate = new DateTimeOffset(payStubDetail.WorkDate.ToDateTime(TimeOnly.MinValue)),
                ReportLineItemID = payStubDetail.ReportLineItemId,
                Bonus = payStubDetail.Bonus,
            }
        ).ToList();
    }

    private void CalculateDaysWorked(List<PayStubAndDetails> payStubDetails)
    {
        List<DaysWorkedMap> daysWorkedMaps = new List<DaysWorkedMap>();
        foreach (PayStubAndDetails payStubDetail in payStubDetails)
        {
            if (!payStubDetail.TotalHours.HasValue)
                continue;

            string uniqueID =
                payStubDetail.EmployerGUID?.ToString()
                + payStubDetail.EmployeeID.ToString()
                + payStubDetail.AgreementID?.ToString()
                + payStubDetail.ClassificationID?.ToString()
                + payStubDetail.SubClassificationID?.ToString();
            DaysWorkedMap? daysWorkedMap;
            if (
                (
                    daysWorkedMap = daysWorkedMaps
                        .Where(
                            (d) => d.UniqueID == uniqueID && d.WorkDate == payStubDetail.WorkDate
                        )
                        .FirstOrDefault()
                ) != null
            )
            {
                if (daysWorkedMap.HoursWorked < payStubDetail.TotalHours)
                {
                    daysWorkedMap.JobCode = payStubDetail.JobCode;
                    daysWorkedMap.CostCenter = payStubDetail.CostCenter;
                    daysWorkedMap.HoursWorked = payStubDetail.TotalHours.Value;
                }
            }
            else
            {
                daysWorkedMap = new DaysWorkedMap
                {
                    UniqueID = uniqueID,
                    WorkDate = payStubDetail.WorkDate,
                    HoursWorked = payStubDetail.TotalHours.Value,
                    JobCode = payStubDetail.JobCode,
                    CostCenter = payStubDetail.CostCenter,
                };
                daysWorkedMaps.Add(daysWorkedMap);
            }
        }

        foreach (PayStubAndDetails payStubDetail in payStubDetails)
        {
            string uniqueID =
                payStubDetail.EmployerGUID?.ToString()
                + payStubDetail.EmployeeID.ToString()
                + payStubDetail.AgreementID?.ToString()
                + payStubDetail.ClassificationID?.ToString()
                + payStubDetail.SubClassificationID?.ToString();
            payStubDetail.DaysWorked = daysWorkedMaps
                .Where(
                    (d) =>
                        d.UniqueID == uniqueID
                        && d.JobCode == payStubDetail.JobCode
                        && d.CostCenter == payStubDetail.CostCenter
                )
                .Count();
        }
    }

    private Guid SaveReport(TimeSheetToReportLineItem lineItem)
    {
        int EmployerID = _db.Roots.Where((r) => r.Guid == lineItem.EmployerGuid).First().Id;
        // Look for an existing report matching the criteria in the time sheet, create a new one if one doesn't exist
        Guid reportGuid = new Guid();
        Report? matchingReport = _db.Reports.Where(report => report.EmployerId == EmployerID
                                            && report.AgreementId == lineItem.AgreementId
                                            && lineItem.WorkPeriod.Date > report.PeriodStartDate.Date
                                            && lineItem.WorkPeriod.Date <= report.PeriodEndDate.Date
        ).FirstOrDefault();

        if (matchingReport != null)
        {
            reportGuid = _db.Roots.Where(r => r.Id == matchingReport.Id).First().Guid!.Value;
        }
        else
        {
            reportGuid = Guid.NewGuid();
            Guid agreementGuid = _db.Roots.Where(r => r.Id == lineItem.AgreementId).First().Guid!.Value;

            var rateScheduleGuids = _db.RateSchedules.Where(r => r.AgreementId == lineItem.AgreementId
                                                            && lineItem.WorkPeriod > r.EffectiveStartDate
                                                            && (r.EffectiveEndDate == null || lineItem.WorkPeriod < r.EffectiveEndDate!.Value.AddDays(1))
                                    ).Join(
                                        _db.Roots,
                                        rateSchedule => rateSchedule.Id,
                                        root => root.Id,
                                        (rateSchedule, root) => new
                                        {
                                            GUID = root.Guid
                                        }
                                    ).ToList();
            
            if (rateScheduleGuids.Count == 0)
            {
                throw new ApplicationException("No matching rate schedule  found for Agreement: "
                    + _db.Agreements.Where(a => a.Id == lineItem.AgreementId).First().Name);
            }
            else if (rateScheduleGuids.Count > 1)
            {
                throw new ApplicationException("Found more than one matching rate schedule for Agreement: "
                    + _db.Agreements.Where(a => a.Id == lineItem.AgreementId).First().Name + ".  Expected 1, found " + rateScheduleGuids.Count);
            }

            DateTime startDate = lineItem.WorkPeriod.AddDays(-6).DateTime.Date;
            DateTime endDate = lineItem.WorkPeriod.DateTime.Date;

            _db.Database.ExecuteSql($"exec [Core].[ReportSave] @GUID={reportGuid}, @AgreementGUID={agreementGuid}, @RateScheduleGUID={rateScheduleGuids.First().GUID}, @EmployerGUID={lineItem.EmployerGuid}, @PeriodStartDate={startDate}, @PeriodEndDate={endDate}, @WorkMonth={endDate}, @DReportStatusName='Started', @SubmissionDate=NULL, @AggregatesOnly=0, @AggregateEmployeeCount=NULL,@Notes=NULL, @ZeroHour=0");
        }
        return reportGuid;
    }

    private Guid SaveReportLineItem(TimeSheetToReportLineItem lineItem, Guid reportGUID)
    {
        Guid reportLineItemGUID = new Guid();
        int reportLineItemID = -1;
        ReportLineItem? matchingReportLineItem = null;

        int reportID = _db.Roots.Where((r) => r.Guid == reportGUID).First().Id;
        if (lineItem.ReportLineItemID.HasValue)
        {
            matchingReportLineItem = _db.ReportLineItems.Where(r => r.Id == lineItem.ReportLineItemID.Value).First();
            reportLineItemGUID = _db.Roots.Where(r => r.Id == matchingReportLineItem.Id).First().Guid!.Value;
            reportLineItemID = matchingReportLineItem.Id;
        }

        if (reportLineItemGUID == Guid.Empty)
        {
            reportLineItemGUID = Guid.NewGuid();
            Guid employeeGUID = _db.Roots.Where(r => r.Id == lineItem.EmployeeId).First().Guid!.Value;
            Guid classificationGUID = _db.Roots.Where(r => r.Id == lineItem.ClassificationId).First().Guid!.Value;
            Guid? subClassificationGUID = null;
            List<Root> subClassificationRoots = _db.Roots.Where(r => r.Id == lineItem.SubClassificationId).ToList();
            if (subClassificationRoots.Count > 0)
            {
                subClassificationGUID = subClassificationRoots[0].Guid;
            }
            if (subClassificationGUID != null)
            {
                _db.Database.ExecuteSql($"exec [Core].[ReportLineItemSave] @GUID={reportLineItemGUID}, @ReportGUID={reportGUID}, @EmployeeGUID={employeeGUID}, @ClassificationNameGUID={classificationGUID}, @SubClassificationGUID={subClassificationGUID}");
            }
            else
            {
                _db.Database.ExecuteSql($"exec [Core].[ReportLineItemSave] @GUID={reportLineItemGUID}, @ReportGUID={reportGUID}, @EmployeeGUID={employeeGUID}, @ClassificationNameGUID={classificationGUID}, @SubClassificationGUID=NULL");
            }
            reportLineItemID = _db.Roots.Where(r => r.Guid == reportLineItemGUID).First().Id;
        }

        // Update PayStubs with report line item ID
        List<PayStubDetail> matchingPayStubDetails = _db.PayStubDetails.Where(p => p.PayStubId == lineItem.PayStubID
                                                                                && p.AgreementId == lineItem.AgreementId
                                                                                && p.ClassificationId == lineItem.ClassificationId
                                                                                && p.JobCode == lineItem.JobCode
                                                                                && p.CostCenter == lineItem.CostCenter
                                                                                && p.HourlyRate == lineItem.HourlyRate
                                                                            ).ToList();
        foreach (PayStubDetail detail in matchingPayStubDetails)
        {
            PayStub? payStub = _db.PayStubs.Where(p => p.EmployeeId == lineItem.EmployeeId && p.Id == detail.PayStubId).FirstOrDefault();
            if (payStub == null) continue;
            detail.ReportLineItemId = reportLineItemID;
            _db.SaveChanges();
        }

        return reportLineItemGUID;
    }

    private void SaveReportLineItemDetails(
        TimeSheetToReportLineItem lineItem,
        Guid reportLineItemGUID
    )
    {
        int ChapterID = _db.Agreements.Where((a) => a.Id == lineItem.AgreementId).First().ChapterId;

        double? hourlyRateDouble = (double?)lineItem.HourlyRate;
        double STHours = lineItem.STHours.HasValue ? lineItem.STHours.Value : 0;
        double OTHours = lineItem.OTHours.HasValue ? lineItem.OTHours.Value : 0;
        double DTHours = lineItem.DTHours.HasValue ? lineItem.DTHours.Value : 0;

        double? grossWagesDouble =
            ((STHours) + (1.5 * OTHours) + (2 * DTHours)) * lineItem.HourlyRate;
        if (lineItem.Bonus.HasValue)
        {
            grossWagesDouble += lineItem.Bonus.Value;
        }
        double? totalGrossWagesDouble = 0;
        if (ChapterID == zumaIBEWLocal3ID)
        {
            totalGrossWagesDouble = grossWagesDouble;
            grossWagesDouble = (STHours + OTHours + DTHours) * lineItem.HourlyRate;
            if (lineItem.Bonus.HasValue)
            {
                grossWagesDouble += lineItem.Bonus.Value;
            }
        }
        double? workingWagesDouble = (STHours + OTHours + DTHours) * lineItem.HourlyRate;
        double? otGrossDouble = ((0.5 * OTHours) + DTHours) * lineItem.HourlyRate;

        ReportLineItemBenefit[] ReportLineItemBenefits =
        {
            new ReportLineItemBenefit(STHoursBenefitID, (double?)lineItem.STHours, null),
            new ReportLineItemBenefit(OTHoursBenefitID, (double?)lineItem.OTHours, null),
            new ReportLineItemBenefit(DTHoursBenefitID, (double?)lineItem.DTHours, null),
            new ReportLineItemBenefit(HoursWorkedBenefitID, (double?)lineItem.TotalHours, null),
            new ReportLineItemBenefit(GrossWagesBenefitID, grossWagesDouble, null),
            new ReportLineItemBenefit(JobCodeBenefitID, null, lineItem.JobCode),
            new ReportLineItemBenefit(HourlyRateBenefitID, hourlyRateDouble, null),
            new ReportLineItemBenefit(WorkingWagesBenefitID, workingWagesDouble, null),
            new ReportLineItemBenefit(OTGrossBenefitID, otGrossDouble, null),
            new ReportLineItemBenefit(TotalDaysWorkedBenefitID, lineItem.DaysWorked, null),
            new ReportLineItemBenefit(TotalGrossWagesBenefitID, totalGrossWagesDouble, null),
            new ReportLineItemBenefit(CostCenterBenefitID, null, lineItem.CostCenter),
        };

        foreach (ReportLineItemBenefit reportLineItemBenefit in ReportLineItemBenefits)
        {
            int benefitID = reportLineItemBenefit.benefitID;
            // Only save the detail item if the agreement has the corresponding benefit ID
            List<AgreementsToBenefit> agreementsToBenefits = _db.AgreementsToBenefits.Where(a => a.AgreementId == lineItem.AgreementId && a.BenefitId == benefitID).ToList();
            if (agreementsToBenefits.Count > 0)
            {
                Guid benefitGUID = _db.Benefits.Where(b => b.Id == benefitID).Join(
                    _db.Roots,
                    benefit => benefit.Id,
                    root => root.Id,
                    (benefit, root) => new
                    {
                        GUID = root.Guid
                    }
                ).First().GUID!.Value;
                if (reportLineItemBenefit.amount != null)
                {
                    _db.Database.ExecuteSql($"exec [Core].[ReportLineItemDetailSave] @ReportLineItemGUID={reportLineItemGUID}, @BenefitGUID={benefitGUID}, @Amount={reportLineItemBenefit.amount}, @VariantValue={reportLineItemBenefit.variantValue}");
                }
                else
                {
                    _db.Database.ExecuteSql($"exec [Core].[ReportLineItemDetailSave] @ReportLineItemGUID={reportLineItemGUID}, @BenefitGUID={benefitGUID}, @Amount=NULL, @VariantValue={reportLineItemBenefit.variantValue}");
                }
            }
        }
    }

    private List<TimeSheetToReportLineItem> GetLineItems(List<PayStubAndDetails> payStubDetails)
    {
        return payStubDetails.GroupBy(payStubDetail => new { payStubDetail.PayStubID, payStubDetail.EmployerGUID, payStubDetail.EmployeeID, payStubDetail.AgreementID, payStubDetail.ClassificationID, payStubDetail.SubClassificationID, payStubDetail.JobCode, payStubDetail.HourlyRate, payStubDetail.CostCenter, payStubDetail.ReportLineItemID })
        .Select(g => new TimeSheetToReportLineItem
        {
            PayStubID = g.Key.PayStubID,
            EmployerGuid = g.Key.EmployerGUID!.Value,
            EmployeeId = g.Key.EmployeeID,
            AgreementId = g.Key.AgreementID,
            ClassificationId = g.Key.ClassificationID,
            SubClassificationId = g.Key.SubClassificationID,
            JobCode = g.Key.JobCode,
            CostCenter = g.Key.CostCenter,
            STHours = g.Sum(o => o.STHours),
            OTHours = g.Sum(o => o.OTHours),
            DTHours = g.Sum(o => o.DTHours),
            TotalHours = g.Sum(o => o.TotalHours),
            HourlyRate = g.Key.HourlyRate,
            WorkPeriod = g.Max(o => o.WorkPeriod!.Value),
            DaysWorked = g.Max(o => o.DaysWorked),
            ReportLineItemID = g.Key.ReportLineItemID,
            Bonus = g.Sum(o => o.Bonus),
        }).Where(g => g.AgreementId != null && g.AgreementId != 0 && g.EmployeeId != null && g.ClassificationId != null).ToList();
    }

    private List<EmployeeBenefitSingleLineItemMap> GetSingleLineItemBenefits(
        List<TimeSheetToReportLineItem> timeSheetLineItems
    )
    {
        List<EmployeeBenefitSingleLineItemMap> SingleLineItemBenefits = new List<EmployeeBenefitSingleLineItemMap>();
        List<int> AgreementIDs = timeSheetLineItems.Where((i) => i.AgreementId.HasValue).Select((i) => i.AgreementId!.Value).ToList();
        List<int> SingleLineItemBenefitIDs = _db.AgreementsToBenefits.Where((a) => AgreementIDs.Contains(a.AgreementId)).Join(
            _db.Benefits,
            agreementToBenefit => agreementToBenefit.BenefitId,
            benefit => benefit.Id,
            (agreementToBenefit, benefit) => benefit
        ).Where((b) => b.OncePerEe).Select((b) => b.Id).ToList();
        foreach (int benefitID in SingleLineItemBenefitIDs)
        {
            SingleLineItemBenefits.Add(new EmployeeBenefitSingleLineItemMap(benefitID));
        }
        return SingleLineItemBenefits;
    }

    private void SaveSingleLineItemBenefits(
        List<EmployeeBenefitSingleLineItemMap> SingleLineItemBenefits,
        TimeSheetToReportLineItem lineItem,
        Guid reportLineItemGUID
    )
    {
        foreach (EmployeeBenefitSingleLineItemMap oncePerEEBenefitMap in SingleLineItemBenefits)
        {
            List<AgreementsToBenefit> oncePerEEAgreementsToBenefits = _db.AgreementsToBenefits.Where(a => a.AgreementId == lineItem.AgreementId && a.BenefitId == oncePerEEBenefitMap.BenefitID).ToList();
            if (oncePerEEAgreementsToBenefits.Count > 0)
            {
                Guid benefitGUID = _db.Benefits.Where(b => b.Id == oncePerEEBenefitMap.BenefitID).Join(
                    _db.Roots,
                    benefit => benefit.Id,
                    root => root.Id,
                    (benefit, root) => new
                    {
                        GUID = root.Guid
                    }
                ).First().GUID!.Value;

                if (!oncePerEEBenefitMap.EmployeeIDs.Contains(lineItem.EmployeeId!.Value))
                {
                    _db.Database.ExecuteSql($"exec [Core].[ReportLineItemDetailSave] @ReportLineItemGUID={reportLineItemGUID}, @BenefitGUID={benefitGUID}, @Amount=NULL");
                    oncePerEEBenefitMap.EmployeeIDs.Add(lineItem.EmployeeId!.Value);
                }
                else
                {
                    _db.Database.ExecuteSql($"exec [Core].[ReportLineItemDetailSave] @ReportLineItemGUID={reportLineItemGUID}, @BenefitGUID={benefitGUID}, @Amount=0");
                }
            }
        }
    }

    private class EmployeeBenefitSingleLineItemMap
    {
        public int BenefitID { get; set; }
        public List<int> EmployeeIDs { get; set; }

        public EmployeeBenefitSingleLineItemMap(int benefitID)
        {
            BenefitID = benefitID;
            EmployeeIDs = new List<int>();
        }
    }

    private class DaysWorkedMap
    {
        public required string UniqueID { get; set; }
        public DateTimeOffset WorkDate { get; set; }
        public string? JobCode { get; set; }
        public string? CostCenter { get; set; }
        public float HoursWorked { get; set; }
    }

    private class PayStubAndDetails
    {
        public int PayStubID { get; set; }
        public Guid? EmployerGUID { get; set; }
        public int EmployeeID { get; set; }
        public int? AgreementID { get; set; }
        public int? ClassificationID { get; set; }
        public int? SubClassificationID { get; set; }
        public string? JobCode { get; set; }
        public string? CostCenter { get; set; }
        public float? STHours { get; set; }
        public float? OTHours { get; set; }
        public float? DTHours { get; set; }
        public float? HourlyRate { get; set; }
        public float? TotalHours { get; set; }
        public DateTimeOffset? WorkPeriod { get; set; }
        public DateTimeOffset WorkDate { get; set; }
        public int DaysWorked { get; set; }

        public int? ReportLineItemID { get; set; }
        public float? Bonus { get; set; }
    }

    private class ReportLineItemBenefit
    {
        public int benefitID;
        public double? amount;
        public string? variantValue;

        public ReportLineItemBenefit(int BenefitID, double? Amount, string? VariantValue)
        {
            benefitID = BenefitID;
            amount = Amount;
            variantValue = VariantValue;
        }
    }
}
