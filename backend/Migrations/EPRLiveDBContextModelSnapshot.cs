﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using backend.Data.Models;

#nullable disable

namespace backend.Migrations
{
    [DbContext(typeof(EPRLiveDBContext))]
    partial class EPRLiveDBContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AspnetUsersInRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.<PERSON>("UserId", "RoleId")
                        .HasName("PK__aspnet_UsersInRo__045D0EA5");

                    b.HasIndex(new[] { "UserId" }, "IX_FK__aspnet_Us__UserI__055132DE");

                    b.HasIndex(new[] { "RoleId" }, "aspnet_UsersInRoles_index");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "RoleId" }, "aspnet_UsersInRoles_index"), 80);

                    b.ToTable("aspnet_UsersInRoles", (string)null);
                });

            modelBuilder.Entity("AuditAuthorization", b =>
                {
                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.HasKey("AgreementId", "OrganizationId");

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_AuditAuthorizations_Agreements");

                    b.HasIndex(new[] { "OrganizationId" }, "IX_FK_AuditAuthorizations_Organizations");

                    b.ToTable("AuditAuthorizations", "Core");
                });

            modelBuilder.Entity("RoleGroupsToAspnetRole", b =>
                {
                    b.Property<int>("RoleGroupId")
                        .HasColumnType("int")
                        .HasColumnName("RoleGroupID");

                    b.Property<Guid>("AspnetRoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ASPNetRoleID");

                    b.HasKey("RoleGroupId", "AspnetRoleId")
                        .HasName("PK__RoleGroupsToASPN__0824493F");

                    b.HasIndex(new[] { "RoleGroupId" }, "IX_fk_RoleGroupsToASPNetRoles_RoleGroups");

                    b.HasIndex(new[] { "AspnetRoleId" }, "IX_fk_RoleGroupsToASPNetRoles_aspnet_Roles");

                    b.ToTable("RoleGroupsToASPNetRoles", "Core");
                });

            modelBuilder.Entity("RoleGroupsToAspnetUser", b =>
                {
                    b.Property<int>("RoleGroupId")
                        .HasColumnType("int")
                        .HasColumnName("RoleGroupID");

                    b.Property<Guid>("AspnetUserGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ASPNetUserGUID");

                    b.HasKey("RoleGroupId", "AspnetUserGuid")
                        .HasName("PK__RoleGroupsToASPN__0BF4DA23");

                    b.HasIndex(new[] { "RoleGroupId" }, "IX_fk_RoleGroupsToASPNetUsers_RoleGroups");

                    b.HasIndex(new[] { "AspnetUserGuid" }, "IX_fk_RoleGroupsToASPNetUsers_aspnet_Users");

                    b.ToTable("RoleGroupsToASPNetUsers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Address", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("AddressLines")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("County")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("DaddressTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DAddressTypeID");

                    b.Property<Guid>("DcountryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DCountryID")
                        .HasDefaultValueSql("('c273ce18-e0ba-49ed-9f34-82263bda4cde')");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Province")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id")
                        .HasName("PK_Addresses_1");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Addresses_ContactMechanisms");

                    b.HasIndex(new[] { "DaddressTypeId" }, "IX_FK_Addresses_DAddressTypes");

                    b.HasIndex(new[] { "DcountryId" }, "IX_FK_Addresses_DCountries");

                    b.ToTable("Addresses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Agreement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<bool>("AdHoc")
                        .HasColumnType("bit");

                    b.Property<bool>("AllowCurrentMonthReporting")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))");

                    b.Property<bool>("AllowDuplicateLineItems")
                        .HasColumnType("bit");

                    b.Property<bool?>("AllowPublishing")
                        .HasColumnType("bit");

                    b.Property<string>("CertificationLanguage")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<int>("DagreementTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DAgreementTypeID");

                    b.Property<bool>("DisableEmployerAmendments")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("EffectiveEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EffectiveStartDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("RequiresSignatoryStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))");

                    b.Property<bool>("SuppressFromEmployerRoster")
                        .HasColumnType("bit");

                    b.Property<int?>("UnionContactId")
                        .HasColumnType("int")
                        .HasColumnName("UnionContactID");

                    b.Property<int>("UnionId")
                        .HasColumnType("int")
                        .HasColumnName("UnionID");

                    b.Property<bool>("Weekly")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ChapterId" }, "IX_Agreements_ChapterID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ChapterId" }, "IX_Agreements_ChapterID"), 80);

                    b.HasIndex(new[] { "EffectiveStartDate", "EffectiveEndDate" }, "IX_Agreements_EffectiveStartDateEffectiveEndDate");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EffectiveStartDate", "EffectiveEndDate" }, "IX_Agreements_EffectiveStartDateEffectiveEndDate"), 80);

                    b.HasIndex(new[] { "Name" }, "IX_Agreements_Name");

                    b.HasIndex(new[] { "ChapterId" }, "IX_ChapterID_Name");

                    b.HasIndex(new[] { "ChapterId" }, "IX_ChapterID_Name_Dates_Covering");

                    b.HasIndex(new[] { "ChapterId" }, "IX_FK_Agreements_Chapters");

                    b.HasIndex(new[] { "DagreementTypeId" }, "IX_FK_Agreements_DAgreementTypes");

                    b.HasIndex(new[] { "UnionContactId" }, "IX_FK_Agreements_Employees");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Agreements_Root");

                    b.HasIndex(new[] { "UnionId" }, "IX_FK_Agreements_Unions");

                    b.ToTable("Agreements", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementClassification", b =>
                {
                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("ClassificationNameId")
                        .HasColumnType("int")
                        .HasColumnName("ClassificationNameID");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int")
                        .HasColumnName("SubClassificationID");

                    b.HasIndex(new[] { "AgreementId", "ClassificationNameId", "SubClassificationId" }, "IX_AgreementClassifications")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "AgreementId", "ClassificationNameId", "SubClassificationId" }, "IX_AgreementClassifications"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "AgreementId", "ClassificationNameId", "SubClassificationId" }, "IX_AgreementClassifications"), 80);

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_AgreementClassifications_Agreements");

                    b.HasIndex(new[] { "ClassificationNameId" }, "IX_FK_AgreementClassifications_ClassificationNames");

                    b.HasIndex(new[] { "SubClassificationId" }, "IX_FK_AgreementClassifications_SubClassifications");

                    b.ToTable("AgreementClassifications", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementSimpleId", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("AgreementsSimpleId", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AgreementToContractFull", b =>
                {
                    b.Property<int>("AgreementId")
                        .HasColumnType("int");

                    b.Property<string>("AgreementName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ContractId")
                        .HasColumnType("int");

                    b.Property<string>("ContractName")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("AgreementsToContractsFull", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AgreementsToBenefit", b =>
                {
                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int?>("CollectingAgentId")
                        .HasColumnType("int")
                        .HasColumnName("CollectingAgentID");

                    b.Property<int?>("FundAdministratorId")
                        .HasColumnType("int")
                        .HasColumnName("FundAdministratorID");

                    b.Property<string>("RemitToInstructions")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AgreementId", "BenefitId");

                    b.HasIndex(new[] { "AgreementId", "BenefitId" }, "IX_AgreementsToBenefitsGetFAOrCAID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "AgreementId", "BenefitId" }, "IX_AgreementsToBenefitsGetFAOrCAID"), 80);

                    b.HasIndex(new[] { "CollectingAgentId" }, "IX_AgreementsToBenefits_CollectingAgent");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "CollectingAgentId" }, "IX_AgreementsToBenefits_CollectingAgent"), 80);

                    b.HasIndex(new[] { "FundAdministratorId" }, "IX_AgreementsToBenefits_FundAdministrator");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "FundAdministratorId" }, "IX_AgreementsToBenefits_FundAdministrator"), 80);

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_AgreementsToBenefits_Agreements");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_AgreementsToBenefits_Benefits");

                    b.HasIndex(new[] { "CollectingAgentId" }, "IX_FK_AgreementsToBenefits_Organizations");

                    b.HasIndex(new[] { "FundAdministratorId" }, "IX_FK_AgreementsToBenefits_Organizations1");

                    b.ToTable("AgreementsToBenefits", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementsToContract", b =>
                {
                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int?>("ContractId")
                        .HasColumnType("int")
                        .HasColumnName("ContractID");

                    b.HasKey("AgreementId");

                    b.ToTable("AgreementsToContracts", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ApplicationConfig", b =>
                {
                    b.Property<string>("ConfigCategory")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ConfigValue")
                        .IsRequired()
                        .HasMaxLength(256)
                        .IsUnicode(false)
                        .HasColumnType("varchar(256)");

                    b.HasIndex(new[] { "ConfigCategory", "ConfigKey" }, "IX_ApplicationConfig_Category_Key")
                        .IsUnique();

                    b.ToTable("ApplicationConfig", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetApplication", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<string>("ApplicationName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LoweredApplicationName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("ApplicationId")
                        .HasName("PK__aspnet_Applicati__4D0CD9BB");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("ApplicationId"), false);

                    b.HasIndex(new[] { "LoweredApplicationName" }, "UQ__aspnet_Applicati__4E00FDF4")
                        .IsUnique();

                    b.HasIndex(new[] { "ApplicationName" }, "UQ__aspnet_Applicati__4EF5222D")
                        .IsUnique();

                    b.HasIndex(new[] { "LoweredApplicationName" }, "aspnet_Applications_Index");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "LoweredApplicationName" }, "aspnet_Applications_Index"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "LoweredApplicationName" }, "aspnet_Applications_Index"), 80);

                    b.ToTable("aspnet_Applications", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetMembership", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasColumnType("ntext");

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FailedPasswordAnswerAttemptCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("FailedPasswordAnswerAttemptWindowStart")
                        .HasColumnType("datetime");

                    b.Property<int>("FailedPasswordAttemptCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("FailedPasswordAttemptWindowStart")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLockedOut")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastLockoutDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LastPasswordChangedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LoweredEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("MobilePin")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("MobilePIN");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordAnswer")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int>("PasswordFormat")
                        .HasColumnType("int");

                    b.Property<string>("PasswordQuestion")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("UserId")
                        .HasName("PK__aspnet_Membershi__6207F6A1");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("UserId"), false);

                    b.HasIndex(new[] { "ApplicationId" }, "IX_FK__aspnet_Me__Appli__62FC1ADA");

                    b.HasIndex(new[] { "UserId" }, "IX_FK__aspnet_Me__UserI__63F03F13");

                    b.HasIndex(new[] { "ApplicationId", "LoweredEmail" }, "aspnet_Membership_index");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "ApplicationId", "LoweredEmail" }, "aspnet_Membership_index"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ApplicationId", "LoweredEmail" }, "aspnet_Membership_index"), 80);

                    b.ToTable("aspnet_Membership", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPath", b =>
                {
                    b.Property<Guid>("PathId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoweredPath")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("PathId")
                        .HasName("PK__aspnet_Paths__139F5235");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("PathId"), false);

                    b.HasIndex(new[] { "ApplicationId" }, "IX_FK__aspnet_Pa__Appli__1493766E");

                    b.HasIndex(new[] { "ApplicationId", "LoweredPath" }, "aspnet_Paths_index")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "ApplicationId", "LoweredPath" }, "aspnet_Paths_index"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ApplicationId", "LoweredPath" }, "aspnet_Paths_index"), 80);

                    b.ToTable("aspnet_Paths", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPersonalizationAllUser", b =>
                {
                    b.Property<Guid>("PathId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("PageSettings")
                        .IsRequired()
                        .HasColumnType("image");

                    b.HasKey("PathId")
                        .HasName("PK__aspnet_Personali__19582B8B");

                    b.HasIndex(new[] { "PathId" }, "IX_FK__aspnet_Pe__PathI__1A4C4FC4");

                    b.ToTable("aspnet_PersonalizationAllUsers", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPersonalizationPerUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("PageSettings")
                        .IsRequired()
                        .HasColumnType("image");

                    b.Property<Guid?>("PathId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK__aspnet_Personali__1C349836");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("Id"), false);

                    b.HasIndex(new[] { "PathId" }, "IX_FK__aspnet_Pe__PathI__1E1CE0A8");

                    b.HasIndex(new[] { "UserId" }, "IX_FK__aspnet_Pe__UserI__1F1104E1");

                    b.HasIndex(new[] { "PathId", "UserId" }, "aspnet_PersonalizationPerUser_index1")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "PathId", "UserId" }, "aspnet_PersonalizationPerUser_index1"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "PathId", "UserId" }, "aspnet_PersonalizationPerUser_index1"), 80);

                    b.HasIndex(new[] { "UserId", "PathId" }, "aspnet_PersonalizationPerUser_ncindex2")
                        .IsUnique()
                        .HasFilter("[UserId] IS NOT NULL AND [PathId] IS NOT NULL");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "UserId", "PathId" }, "aspnet_PersonalizationPerUser_ncindex2"), 80);

                    b.ToTable("aspnet_PersonalizationPerUser", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetProfile", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("PropertyNames")
                        .IsRequired()
                        .HasColumnType("ntext");

                    b.Property<byte[]>("PropertyValuesBinary")
                        .IsRequired()
                        .HasColumnType("image");

                    b.Property<string>("PropertyValuesString")
                        .IsRequired()
                        .HasColumnType("ntext");

                    b.HasKey("UserId")
                        .HasName("PK__aspnet_Profile__77031387");

                    b.HasIndex(new[] { "UserId" }, "IX_FK__aspnet_Pr__UserI__77F737C0");

                    b.ToTable("aspnet_Profile", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetRole", b =>
                {
                    b.Property<Guid>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LoweredRoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("RoleId")
                        .HasName("PK__aspnet_Roles__008C7DC1");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("RoleId"), false);

                    b.HasIndex(new[] { "ApplicationId", "LoweredRoleName" }, "aspnet_Roles_index1")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "ApplicationId", "LoweredRoleName" }, "aspnet_Roles_index1"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ApplicationId", "LoweredRoleName" }, "aspnet_Roles_index1"), 80);

                    b.ToTable("aspnet_Roles", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetSchemaVersion", b =>
                {
                    b.Property<string>("Feature")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("CompatibleSchemaVersion")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<bool>("IsCurrentVersion")
                        .HasColumnType("bit");

                    b.HasKey("Feature", "CompatibleSchemaVersion")
                        .HasName("PK__aspnet_SchemaVer__578A682E");

                    b.ToTable("aspnet_SchemaVersions", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetUser", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("(newid())");

                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsAnonymous")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastActivityDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LoweredUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("MobileAlias")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("UserId")
                        .HasName("PK__aspnet_Users__51D18ED8");

                    SqlServerKeyBuilderExtensions.IsClustered(b.HasKey("UserId"), false);

                    b.HasIndex(new[] { "ApplicationId" }, "IX_FK__aspnet_Us__Appli__52C5B311");

                    b.HasIndex(new[] { "ApplicationId", "LoweredUserName" }, "aspnet_Users_Index")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "ApplicationId", "LoweredUserName" }, "aspnet_Users_Index"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ApplicationId", "LoweredUserName" }, "aspnet_Users_Index"), 80);

                    b.HasIndex(new[] { "ApplicationId", "LastActivityDate" }, "aspnet_Users_Index2");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ApplicationId", "LastActivityDate" }, "aspnet_Users_Index2"), 80);

                    b.ToTable("aspnet_Users", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspnetWebEventEvent", b =>
                {
                    b.Property<string>("EventId")
                        .HasMaxLength(32)
                        .IsUnicode(false)
                        .HasColumnType("char(32)")
                        .IsFixedLength();

                    b.Property<string>("ApplicationPath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ApplicationVirtualPath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Details")
                        .HasColumnType("ntext");

                    b.Property<int>("EventCode")
                        .HasColumnType("int");

                    b.Property<int>("EventDetailCode")
                        .HasColumnType("int");

                    b.Property<decimal>("EventOccurrence")
                        .HasColumnType("decimal(19, 0)");

                    b.Property<decimal>("EventSequence")
                        .HasColumnType("decimal(19, 0)");

                    b.Property<DateTime>("EventTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EventTimeUtc")
                        .HasColumnType("datetime");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ExceptionType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("MachineName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Message")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("RequestUrl")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("EventId")
                        .HasName("PK__aspnet_WebEvent___2E534871");

                    b.ToTable("aspnet_WebEvent_Events", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspstateTempApplication", b =>
                {
                    b.Property<int>("AppId")
                        .HasColumnType("int");

                    b.Property<string>("AppName")
                        .IsRequired()
                        .HasMaxLength(280)
                        .IsUnicode(false)
                        .HasColumnType("char(280)")
                        .IsFixedLength();

                    b.HasKey("AppId")
                        .HasName("PK__ASPState__8E2CF7F9FAD27830");

                    b.HasIndex(new[] { "AppName" }, "Index_AppName");

                    b.ToTable("ASPStateTempApplications", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.AspstateTempSession", b =>
                {
                    b.Property<string>("SessionId")
                        .HasMaxLength(88)
                        .HasColumnType("nvarchar(88)");

                    b.Property<DateTime>("Created")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getutcdate())");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime");

                    b.Property<int>("Flags")
                        .HasColumnType("int");

                    b.Property<int>("LockCookie")
                        .HasColumnType("int");

                    b.Property<DateTime>("LockDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LockDateLocal")
                        .HasColumnType("datetime");

                    b.Property<bool>("Locked")
                        .HasColumnType("bit");

                    b.Property<byte[]>("SessionItemLong")
                        .HasColumnType("image");

                    b.Property<byte[]>("SessionItemShort")
                        .HasMaxLength(7000)
                        .HasColumnType("varbinary(7000)");

                    b.Property<int>("Timeout")
                        .HasColumnType("int");

                    b.HasKey("SessionId")
                        .HasName("PK__ASPState__C9F4929071302149");

                    b.HasIndex(new[] { "Expires" }, "Index_Expires");

                    b.ToTable("ASPStateTempSessions", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Benefit", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID")
                        .HasComment("BenefitID identifies Benefits");

                    b.Property<bool>("ApplyToAssociationReport")
                        .HasColumnType("bit");

                    b.Property<bool>("ChapterAdministratorOnly")
                        .HasColumnType("bit")
                        .HasComment("Determines if a benefit can only be toggled by a chapter administrator type role. Was AdminOnly = 0x1000");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID")
                        .HasComment("If null, then this benefit is for all chapters.");

                    b.Property<bool>("DefaultOn")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DisplayOnFundingOnly")
                        .HasColumnType("bit")
                        .HasComment("Determines if this benefit is used by a collecting agent to manage internal \"accounts\" such as overages. Was FundingOnly = 1024.");

                    b.Property<bool?>("DoNotTotal")
                        .HasColumnType("bit");

                    b.Property<int>("DstatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DStatusID")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Determines if the benefit is available for new agreements.");

                    b.Property<bool>("EmployeeDeduction")
                        .HasColumnType("bit");

                    b.Property<bool>("EmployeeElectionOverridable")
                        .HasColumnType("bit")
                        .HasComment("Determines if the benefit is picked at an employee level (it shows up on the employee details page). Was EmployeeOption = 0x20");

                    b.Property<bool>("EmployeeRateOverridable")
                        .HasColumnType("bit")
                        .HasComment("Determines if the employee can have a custom rate.");

                    b.Property<bool>("EmployerElectionOverridable")
                        .HasColumnType("bit")
                        .HasComment("Determines if the benefit is also shown on supplemental reports. Was AvailableOnSupplementalReports = 0x40.");

                    b.Property<bool>("EmployerRateOverridable")
                        .HasColumnType("bit");

                    b.Property<bool>("HourComponent")
                        .HasColumnType("bit");

                    b.Property<bool>("InformationalOnly")
                        .HasColumnType("bit")
                        .HasComment("Determines if the benefit is for reporting purposes only and is excluded from calculations and funding. New.");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("The name of the benefit");

                    b.Property<bool>("OncePerEe")
                        .HasColumnType("bit")
                        .HasColumnName("OncePerEE");

                    b.Property<bool>("Selectable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Determines if the benefit can be manually selected for inclusion into an agreement.");

                    b.Property<bool>("Taxable")
                        .HasColumnType("bit");

                    b.Property<string>("VariantType")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_Benefits");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id" }, "IX_Benefits"), 80);

                    b.HasIndex(new[] { "ChapterId" }, "IX_FK_Benefits_Chapters");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Benefits_Root");

                    b.HasIndex(new[] { "ChapterId", "Name" }, "IX_Name_ChapterID");

                    b.ToTable("Benefits", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.BenefitOverride", b =>
                {
                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<bool?>("Election")
                        .HasColumnType("bit");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("PartyId")
                        .HasColumnType("int")
                        .HasColumnName("PartyID");

                    b.Property<decimal?>("Value")
                        .HasColumnType("decimal(28, 10)");

                    b.HasIndex(new[] { "BenefitId", "ChapterId" }, "IX_BenefitOverrides_BenefitID_ChapterID_Covering");

                    b.HasIndex(new[] { "ChapterId" }, "IX_BenefitOverrides_ChapterID_Covering");

                    b.HasIndex(new[] { "PartyId" }, "IX_FK_BenefitOverrides_Parties");

                    b.HasIndex(new[] { "Id" }, "IX_FK_BenefitOverrides_Root");

                    b.HasIndex(new[] { "PartyId", "BenefitId", "ChapterId" }, "IX_Party_Benefit_Chapter");

                    b.ToTable("BenefitOverrides", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.BenefitOverridesOrig", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<bool?>("Election")
                        .HasColumnType("bit");

                    b.Property<int>("PartyId")
                        .HasColumnType("int")
                        .HasColumnName("PartyID");

                    b.Property<decimal?>("Value")
                        .HasColumnType("decimal(18, 4)");

                    b.HasKey("Id")
                        .HasName("PK_BenefitOverrides");

                    b.HasIndex(new[] { "BenefitId", "ChapterId" }, "IX_BenefitOverrides_BenefitID_ChapterID_Covering");

                    b.HasIndex(new[] { "ChapterId" }, "IX_BenefitOverrides_ChapterID_Covering");

                    b.HasIndex(new[] { "PartyId" }, "IX_FK_BenefitOverrides_Parties");

                    b.HasIndex(new[] { "Id" }, "IX_FK_BenefitOverrides_Root");

                    b.HasIndex(new[] { "PartyId", "BenefitId", "ChapterId" }, "IX_Party_Benefit_Chapter");

                    b.ToTable("BenefitOverridesOrig", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.BenefitSimpleId", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("BenefitsSimpleId", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.BenefitToContract", b =>
                {
                    b.Property<string>("Benefit")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("BenefitID")
                        .HasColumnType("int");

                    b.Property<string>("Contract")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ContractID")
                        .HasColumnType("int");

                    b.ToTable("BenefitToContracts", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Chapter", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("EmployeeAssociationId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("EmployeeAssociationID");

                    b.Property<string>("EmployerAssociationId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("EmployerAssociationID");

                    b.Property<bool>("Limited")
                        .HasColumnType("bit")
                        .HasComment("Indicates that this chapter has access to a reduced functionality set.");

                    b.HasKey("Id")
                        .HasName("PK_Carriers_1");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Carriers_Organizations");

                    b.ToTable("Chapters", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ChapterToEmployerRelationship", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("AssociationId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AssociationID");

                    b.Property<DateTime?>("DatePriviledgedAuthorized")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsAssociationMember")
                        .HasColumnType("bit");

                    b.Property<string>("PriviledgedAuthorizedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_ChapterToEmployerRelationships_1");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ChapterToEmployerRelationships_Root");

                    b.ToTable("ChapterToEmployerRelationships", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ChaptersTradeAdministrator", b =>
                {
                    b.Property<Guid>("Guid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("GUID");

                    b.HasKey("Guid")
                        .HasName("PK__ChaptersTradeAdm__60221034");

                    b.ToTable("ChaptersTradeAdministrator", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ClassificationName", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<int>("DclassificationCodeId")
                        .HasColumnType("int")
                        .HasColumnName("DClassificationCodeID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DstatusId")
                        .HasColumnType("int")
                        .HasColumnName("DStatusID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id")
                        .HasName("PK_JobClassificationAliases");

                    b.HasIndex(new[] { "ChapterId", "Name" }, "IX_ChapterID_Name");

                    b.HasIndex(new[] { "ChapterId" }, "IX_FK_JobClassificationAliases_Chapters");

                    b.HasIndex(new[] { "DclassificationCodeId" }, "IX_FK_JobClassificationAliases_DJobClassifications");

                    b.HasIndex(new[] { "DstatusId" }, "IX_FK_JobClassificationAliases_DStatuses");

                    b.HasIndex(new[] { "Id" }, "IX_FK_JobClassificationAliases_Root");

                    b.ToTable("ClassificationNames", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ClassificationSimpleId", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("ClassificationsSimpleId", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.ClassificationXref", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int>("ClassificationNameId")
                        .HasColumnType("int")
                        .HasColumnName("ClassificationNameID");

                    b.Property<int>("FundAdminId")
                        .HasColumnType("int")
                        .HasColumnName("FundAdminID");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int")
                        .HasColumnName("SubClassificationID");

                    b.Property<int>("UnionId")
                        .HasColumnType("int")
                        .HasColumnName("UnionID");

                    b.Property<string>("Xrefvalue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id")
                        .HasName("PK_Core.ClassificationXref");

                    b.ToTable("ClassificationXref", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ContactMechanism", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DcontactMechanismTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DContactMechanismTypeID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DcontactMechanismTypeId" }, "IX_FK_ContactMechanisms_DContactMechanismTypes");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ContactMechanisms_Root");

                    b.ToTable("ContactMechanisms", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Contract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AmfbenefitId")
                        .HasColumnType("int")
                        .HasColumnName("AMFBenefitID");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NecabenefitId")
                        .HasColumnType("int")
                        .HasColumnName("NECABenefitID");

                    b.HasKey("Id")
                        .HasName("PK__Contract__3214EC278A3CF31F");

                    b.ToTable("Contracts", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Control", b =>
                {
                    b.Property<string>("Ssn")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("SSN");

                    b.HasKey("Ssn");

                    b.ToTable("Control", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.CostCode", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int?>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int?>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("EmployerId")
                        .HasColumnType("int")
                        .HasColumnName("EmployerID");

                    b.HasKey("Id")
                        .HasName("PK__CostCode__3214EC27CB2926EA");

                    b.HasIndex("AgreementId");

                    b.HasIndex("BenefitId");

                    b.HasIndex("EmployerId");

                    b.ToTable("CostCodes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.CreateRecurringPaymentProfileResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Ack")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ACK");

                    b.Property<string>("Build")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("BUILD");

                    b.Property<string>("Correlationid")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("CORRELATIONID");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Errorcode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ERRORCODE");

                    b.Property<string>("Longmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LONGMESSAGE");

                    b.Property<int?>("OrderDetailId")
                        .HasColumnType("int")
                        .HasColumnName("OrderDetailID");

                    b.Property<string>("Profileid")
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)")
                        .HasColumnName("PROFILEID");

                    b.Property<string>("Profilestatus")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PROFILESTATUS");

                    b.Property<string>("SessionId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("SessionID");

                    b.Property<string>("Severritycode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SEVERRITYCODE");

                    b.Property<string>("Shortmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SHORTMESSAGE");

                    b.Property<string>("Token")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("TOKEN");

                    b.Property<string>("Version")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("VERSION");

                    b.HasKey("Id")
                        .HasName("PK_PayPal.CreateRecurringPaymentProfileResponse");

                    b.ToTable("CreateRecurringPaymentProfileResponse", "PayPal");
                });

            modelBuilder.Entity("backend.Data.Models.CreditCardPaymentMethod", b =>
                {
                    b.Property<Guid>("AssociatedGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AssociatedGUID");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Expiration")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AssociatedGuid");

                    b.HasIndex(new[] { "AssociatedGuid" }, "IX_FK_CreditCardPaymentMethods_PaymentMethods");

                    b.ToTable("CreditCardPaymentMethods", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("NewApp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("(CONVERT([bit],(0),0))");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("OptimisticLockField")
                        .HasColumnType("int");

                    b.Property<string>("ReportPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ReportTypeId")
                        .HasColumnType("int")
                        .HasColumnName("ReportTypeID");

                    b.Property<string>("SuppressDuplicatesThroughColumnName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("UsesGuidSubstitution")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("CustomReports", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportPivot", b =>
                {
                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<string>("NameColumn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Sort")
                        .HasColumnType("bit");

                    b.Property<string>("ValueColumn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ReportId");

                    b.ToTable("CustomReportPivots", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("CustomReportId")
                        .HasColumnType("int")
                        .HasColumnName("CustomReportID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductID");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex(new[] { "CustomReportId", "ProductId" }, "Unique_CustomReportProduct")
                        .IsUnique();

                    b.ToTable("CustomReportProduct", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_CustomReportProduct_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportSubtotal", b =>
                {
                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<string>("SubtotalColumnName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TriggerColumnName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasIndex("ReportId");

                    b.ToTable("CustomReportSubtotals", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.DaddressType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DAddressTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DagreementType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Changing the names of the agreement types can affect code.");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id", "Name" }, "IX_DAgreementTypes")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id", "Name" }, "IX_DAgreementTypes"), 80);

                    b.ToTable("DAgreementTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DamendmentAction", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DAmendmentActions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DatabaseMigration", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<DateTime>("MigrationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.HasKey("Id")
                        .HasName("PK__Database__3214EC278CE884C9");

                    b.ToTable("DatabaseMigrations", null, t =>
                        {
                            t.HasComment("This is an ad-hoc database versioning scheme to support the migrations found in EPR Database/Migrations. It should be removed when we have a proper migration solution.");
                        });
                });

            modelBuilder.Entity("backend.Data.Models.DatabaseSpace", b =>
                {
                    b.Property<int>("DatabaseSpaceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DatabaseSpaceID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DatabaseSpaceId"));

                    b.Property<string>("DatabaseName")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("FileSizeMb")
                        .HasColumnType("int")
                        .HasColumnName("FileSizeMB");

                    b.Property<int?>("FreeSpaceMb")
                        .HasColumnType("int")
                        .HasColumnName("FreeSpaceMB");

                    b.Property<int?>("FreeSpacePages")
                        .HasColumnType("int");

                    b.Property<string>("FreeSpacePct")
                        .HasMaxLength(7)
                        .IsUnicode(false)
                        .HasColumnType("varchar(7)");

                    b.Property<string>("LogicalFileName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("PhysicalFileName")
                        .HasMaxLength(520)
                        .HasColumnType("nvarchar(520)");

                    b.Property<DateTime?>("PollDate")
                        .HasColumnType("datetime");

                    b.Property<string>("RecoveryMode")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ServerName")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Updateability")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("DatabaseSpaceId");

                    b.ToTable("DatabaseSpace", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.DcalculationMethod", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID")
                        .HasComment("Primary Key");

                    b.Property<string>("FormatString")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("a formatting string suitable for String.Format used when displaying the string.");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("A human-consummable description of the method");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Uisymbol")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nchar(1)")
                        .HasColumnName("UISymbol")
                        .IsFixedLength();

                    b.HasKey("Id");

                    b.ToTable("DCalculationMethods", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DcalculationModifier", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DCalculationModifiers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Dcategory", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<bool>("Active")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("LargeImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("MediumImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ParentCategory")
                        .HasColumnType("int");

                    b.Property<string>("SmallImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id")
                        .HasName("PK_Category");

                    b.ToTable("DCategory", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_DCategory_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.DclassificationCode", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ClassName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("the \"full\" classification name");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ExemptEmployee")
                        .HasColumnType("bit");

                    b.HasKey("Id")
                        .HasName("PK_DJobClassifications");

                    b.ToTable("DClassificationCodes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DcontactMechanismType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DContactMechanismTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Dcountry", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("NumericCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nchar(3)")
                        .IsFixedLength();

                    b.Property<string>("ThreeLetterCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nchar(3)")
                        .IsFixedLength();

                    b.Property<string>("TwoLetterCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nchar(2)")
                        .IsFixedLength();

                    b.HasKey("Id");

                    b.ToTable("DCountries", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DcustomReportType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_DCustomReportType");

                    b.ToTable("DCustomReportTypes", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.DelectronicPaymentOption", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DElectronicPaymentOptions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DelinquencyLetter", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("Cclist")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CCList");

                    b.Property<Guid>("OrgId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("OrgID");

                    b.Property<bool?>("SendToEmployer")
                        .HasColumnType("bit");

                    b.Property<bool?>("SendToPayrollContact")
                        .HasColumnType("bit");

                    b.Property<bool?>("SendToPrimaryContact")
                        .HasColumnType("bit");

                    b.Property<string>("Text")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeId")
                        .HasColumnType("int")
                        .HasColumnName("TypeID");

                    b.HasKey("Id")
                        .HasName("PK_DL");

                    b.ToTable("DelinquencyLetters", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DelinquencyPayment", b =>
                {
                    b.Property<string>("Address1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Agreement")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AgreementID")
                        .HasColumnType("int");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Benefit")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("BenefitID")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Employer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("EmployerGUID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("EmployerID")
                        .HasColumnType("int");

                    b.Property<decimal?>("FundedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ReportID")
                        .HasColumnType("int");

                    b.Property<string>("State")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubmissionDate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Submission Date");

                    b.Property<string>("Union")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Weekly")
                        .HasColumnType("bit");

                    b.Property<string>("WorkMonth")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Work Month");

                    b.Property<string>("ZipCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Zip Code");

                    b.ToTable("DelinquencyPayments", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.DemailAddressType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DEmailAddressTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DentityType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_DEntities");

                    b.ToTable("DEntityTypes", "Core", t =>
                        {
                            t.HasComment("Identifies the underlying type of entity represented by this record. These values are mirrored, as applicable, in DOrganizationTypes and DPersonTypes.");
                        });
                });

            modelBuilder.Entity("backend.Data.Models.DeventSubType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DEventSubTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DeventType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DEventTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Dgender", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DGenders", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DguidSubstitutionType", b =>
                {
                    b.Property<int>("SubstitutionTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("SubstitutionTypeId")
                        .HasName("PK__DGuidSubstitutio__620A58A6");

                    b.ToTable("DGuidSubstitutionType", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DoExpressCheckoutPaymentResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Ack")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ACK");

                    b.Property<string>("Build")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("BUILD");

                    b.Property<string>("Correlationid")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("CORRELATIONID");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Errorcode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ERRORCODE");

                    b.Property<string>("Longmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LONGMESSAGE");

                    b.Property<int?>("OrderId")
                        .HasColumnType("int")
                        .HasColumnName("OrderID");

                    b.Property<string>("Paymenrequest0Ack")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENREQUEST_0_ACK");

                    b.Property<string>("Paymenrequest0Errorcode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENREQUEST_0_ERRORCODE");

                    b.Property<string>("Paymenrequest0Longmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENREQUEST_0_LONGMESSAGE");

                    b.Property<string>("Paymenrequest0Severitycode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENREQUEST_0_SEVERITYCODE");

                    b.Property<string>("Paymentinfo0Ack")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENTINFO_0_ACK");

                    b.Property<decimal?>("Paymentinfo0Amt")
                        .HasColumnType("decimal(6, 2)")
                        .HasColumnName("PAYMENTINFO_0_AMT");

                    b.Property<string>("Paymentinfo0Currencycode")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("PAYMENTINFO_0_CURRENCYCODE");

                    b.Property<string>("Paymentinfo0Errorcode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENTINFO_0_ERRORCODE");

                    b.Property<decimal?>("Paymentinfo0Feeamt")
                        .HasColumnType("decimal(6, 2)")
                        .HasColumnName("PAYMENTINFO_0_FEEAMT");

                    b.Property<DateTime?>("Paymentinfo0Ordertime")
                        .HasColumnType("datetime")
                        .HasColumnName("PAYMENTINFO_0_ORDERTIME");

                    b.Property<string>("Paymentinfo0Paymentstatus")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_PAYMENTSTATUS");

                    b.Property<string>("Paymentinfo0Paymenttype")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_PAYMENTTYPE");

                    b.Property<string>("Paymentinfo0Pendingreason")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_PENDINGREASON");

                    b.Property<string>("Paymentinfo0Reasoncode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_REASONCODE");

                    b.Property<string>("Paymentinfo0Securemerchantaccountid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_SECUREMERCHANTACCOUNTID");

                    b.Property<decimal?>("Paymentinfo0Taxamt")
                        .HasColumnType("decimal(6, 2)")
                        .HasColumnName("PAYMENTINFO_0_TAXAMT");

                    b.Property<string>("Paymentinfo0Transactionid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYMENTINFO_0_TRANSACTIONID");

                    b.Property<string>("Paymentinfo0Transactiontype")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("PAYMENTINFO_0_TRANSACTIONTYPE");

                    b.Property<string>("Paymentrequest0Shortmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PAYMENTREQUEST_0_SHORTMESSAGE");

                    b.Property<string>("SessionId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("SessionID");

                    b.Property<string>("Severritycode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SEVERRITYCODE");

                    b.Property<string>("Shortmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SHORTMESSAGE");

                    b.Property<string>("Token")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("TOKEN");

                    b.Property<string>("Version")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("VERSION");

                    b.HasKey("Id")
                        .HasName("PK_PayPal.DoExpressCheckoutPaymentResponse");

                    b.ToTable("DoExpressCheckoutPaymentResponse", "PayPal");
                });

            modelBuilder.Entity("backend.Data.Models.DorderStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DOrderStatus", "ShoppingCart");
                });

            modelBuilder.Entity("backend.Data.Models.DorganizationType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DOrganizationTypes", "Core", t =>
                        {
                            t.HasComment("Identifies the underlying type of entity represented by this record. These values mirror, as applicable, the values in DEntityTypes.");
                        });
                });

            modelBuilder.Entity("backend.Data.Models.DownloadContribution", b =>
                {
                    b.Property<string>("Agreement")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Calculation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Classification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CollectingAgentID")
                        .HasColumnType("int");

                    b.Property<decimal?>("DTHours")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("EmployeeCount")
                        .HasColumnType("int");

                    b.Property<int>("EmployerId")
                        .HasColumnType("int");

                    b.Property<string>("EmployerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FEINSSN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fund")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("FundAdministratorID")
                        .HasColumnType("int");

                    b.Property<DateOnly>("FundingDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("GrossWages")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("HoursWorked")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("OTHours")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Payment")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("PayrollFromDate")
                        .HasColumnType("date");

                    b.Property<DateOnly>("PayrollThruDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("Rate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("RecordType")
                        .HasColumnType("int");

                    b.Property<int>("ReportId")
                        .HasColumnType("int");

                    b.Property<decimal?>("STHours")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("SubmissionDate")
                        .HasColumnType("date");

                    b.Property<string>("Union")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkMonth")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable((string)null);

                    b.ToView("DownloadContributionsView", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DpartyType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_dEntityTypes");

                    b.ToTable("DPartyTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DpaymentMethodType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DPaymentMethodTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DpaymentType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DPaymentTypes", "ShoppingCart");
                });

            modelBuilder.Entity("backend.Data.Models.DpersonType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DPersonTypes", "Core", t =>
                        {
                            t.HasComment("Identifies the underlying type of entity represented by this record. These values mirror, as applicable, the values in DEntityTypes.");
                        });
                });

            modelBuilder.Entity("backend.Data.Models.DphoneNumberType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DPhoneNumberTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Dprovince", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("Abbrieviation")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nchar(2)")
                        .IsFixedLength();

                    b.Property<Guid>("DcountryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DCountryID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DProvinces", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_DStatuses");

                    b.ToTable("DRelationshipStatuses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipSubType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DrelationshipTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DRelationshipTypeID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id", "DrelationshipTypeId")
                        .HasName("PK_DEmploymentStatuses");

                    b.ToTable("DRelationshipSubTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<bool>("IsOneToOne")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Most relationships between parties are a one-to-many (e.g., Employer to Employees). Once this type of relationship is created, it is never updated--only its status. \r\n\r\nHowever, parties can have multiple relationships between them, and some of these are a one-to-one type of relationship that can be changed over time without needing to be temporally aware (e.g., the primary contact for an employer).\r\n\r\nWhen retrieving employees for an entity, only the one-to-many relationships should be used, otherwise employees with a second relationship would be duplicated.");

                    b.Property<int>("LeftDentityTypeId")
                        .HasColumnType("int")
                        .HasColumnName("LeftDEntityTypeID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RightDentityTypeId")
                        .HasColumnType("int")
                        .HasColumnName("RightDEntityTypeID");

                    b.HasKey("Id")
                        .HasName("PK__tdPartyRelations__683FF2E4");

                    b.ToTable("DRelationshipTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DreportStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("DReportStatuses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DreportSubscriptionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("OptimisticLockField")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DReportSubscriptionTypes", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.DsettingsType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowOverride")
                        .HasColumnType("bit");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValidValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValueType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK__DSetting__3214EC273357A0F0");

                    b.ToTable("DSettingsTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Dstatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_DStatuses_1");

                    b.ToTable("DStatuses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DtaMv0", b =>
                {
                    b.Property<int>("Col1")
                        .HasColumnType("int")
                        .HasColumnName("_col_1");

                    b.Property<DateTime>("Col10")
                        .HasColumnType("datetime")
                        .HasColumnName("_col_10");

                    b.Property<long?>("Col11")
                        .HasColumnType("bigint")
                        .HasColumnName("_col_11");

                    b.Property<int>("Col2")
                        .HasColumnType("int")
                        .HasColumnName("_col_2");

                    b.Property<int>("Col3")
                        .HasColumnType("int")
                        .HasColumnName("_col_3");

                    b.Property<int>("Col4")
                        .HasColumnType("int")
                        .HasColumnName("_col_4");

                    b.Property<DateTime>("Col5")
                        .HasColumnType("datetime")
                        .HasColumnName("_col_5");

                    b.Property<int>("Col6")
                        .HasColumnType("int")
                        .HasColumnName("_col_6");

                    b.Property<decimal>("Col7")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("_col_7");

                    b.Property<string>("Col8")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("_col_8");

                    b.Property<string>("Col9")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("_col_9");

                    b.ToTable((string)null);

                    b.ToView("_dta_mv_0", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.DwebsiteType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DWebsiteTypes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Eftpayment", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutingNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_EFTPayments_ElectronicPayments");

                    b.ToTable("EFTPayments", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.EftpaymentMethod", b =>
                {
                    b.Property<Guid>("AssociatedGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AssociatedGUID");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutingNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AssociatedGuid");

                    b.HasIndex(new[] { "AssociatedGuid" }, "IX_FK_EFTPaymentMethods_PaymentMethods");

                    b.ToTable("EFTPaymentMethods", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicBatch", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<DateTime>("BatchDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CollectingAgentId")
                        .HasColumnType("int")
                        .HasColumnName("CollectingAgentID");

                    b.Property<DateTime>("LastDownloaded")
                        .HasColumnType("datetime");

                    b.HasKey("Id")
                        .HasName("PK_EFTBatches");

                    b.HasIndex(new[] { "Id" }, "IX_FK_EFTBatches_Root");

                    b.ToTable("ElectronicBatches", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicPayment", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int>("DpaymentMethodTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DPaymentMethodTypeID");

                    b.Property<int?>("ElectronicBatchId")
                        .HasColumnType("int")
                        .HasColumnName("ElectronicBatchID");

                    b.Property<int?>("PaymentsId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentsID");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<bool>("Suppress")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_ElectronicPayments_Benefits");

                    b.HasIndex(new[] { "DpaymentMethodTypeId" }, "IX_FK_ElectronicPayments_DPaymentMethodTypes");

                    b.HasIndex(new[] { "ElectronicBatchId" }, "IX_FK_ElectronicPayments_ElectronicBatches");

                    b.HasIndex(new[] { "PaymentsId" }, "IX_FK_ElectronicPayments_Payments");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_ElectronicPayments_Reports");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ElectronicPayments_Root");

                    b.HasIndex(new[] { "ReportId", "ElectronicBatchId" }, "IX_ReportID_ElectronicBatchID");

                    b.ToTable("ElectronicPayments", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicPaymentConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("DelectronicPaymentOptionId")
                        .HasColumnType("int")
                        .HasColumnName("DElectronicPaymentOptionID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<bool>("Mandatory")
                        .HasColumnType("bit");

                    b.Property<int?>("NachaconfigurationId")
                        .HasColumnType("int")
                        .HasColumnName("NACHAConfigurationID");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.Property<int>("UnionId")
                        .HasColumnType("int")
                        .HasColumnName("UnionID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "OrganizationId", "BenefitId", "UnionId" }, "IX_ElectronicPaymentConfigurations_OrgBeneUnion")
                        .IsUnique();

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_ElectronicPaymentConfigurations_Benefits");

                    b.HasIndex(new[] { "ChapterId" }, "IX_FK_ElectronicPaymentConfigurations_ChapterOrganizations");

                    b.HasIndex(new[] { "DelectronicPaymentOptionId" }, "IX_FK_ElectronicPaymentConfigurations_DElectronicPaymentOptions");

                    b.HasIndex(new[] { "NachaconfigurationId" }, "IX_FK_ElectronicPaymentConfigurations_NACHAConfigurations");

                    b.HasIndex(new[] { "OrganizationId" }, "IX_FK_ElectronicPaymentConfigurations_Organizations");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ElectronicPaymentConfigurations_Root");

                    b.ToTable("ElectronicPaymentConfigurations", "Core", t =>
                        {
                            t.HasTrigger("Trigger_ElectronicPaymentConfigurations_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.EmailAddress", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DemailAddressTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DEmailAddressTypeID");

                    b.Property<string>("EmailAddress1")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("EmailAddress");

                    b.HasKey("Id")
                        .HasName("PK_EmailAddress");

                    b.HasIndex(new[] { "Id" }, "IX_FK_EmailAddresses_ContactMechanisms");

                    b.HasIndex(new[] { "DemailAddressTypeId" }, "IX_FK_EmailAddresses_DEmailAddressTypes");

                    b.ToTable("EmailAddresses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Employee", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateOfHire")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DateOfTermination")
                        .HasColumnType("datetime");

                    b.Property<string>("ExternalEmployeeId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("EmployeeID");

                    b.Property<int?>("HomeLocalId")
                        .HasColumnType("int")
                        .HasColumnName("HomeLocalID");

                    b.Property<byte[]>("SearchSsn")
                        .HasMaxLength(20)
                        .HasColumnType("varbinary(20)")
                        .HasColumnName("SearchSSN");

                    b.Property<byte[]>("Ssn")
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("SSN");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Employees_Persons");

                    b.HasIndex(new[] { "SearchSsn" }, "IX_SearchSSN");

                    b.ToTable("Employees", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.EmployeeImport", b =>
                {
                    b.Property<string>("Agreement")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AGREEMENT");

                    b.Property<string>("Classification")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("CLASSIFICATION");

                    b.Property<int>("EmployeeNumber")
                        .HasColumnType("int")
                        .HasColumnName("Employee_Number");

                    b.Property<int?>("Eprid")
                        .HasColumnType("int")
                        .HasColumnName("EPRID");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("FIRST_NAME");

                    b.Property<decimal>("HourlyRate")
                        .HasColumnType("decimal(18, 10)")
                        .HasColumnName("Hourly_Rate");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("LAST_NAME");

                    b.Property<string>("Ssn")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("SSN");

                    b.ToTable("EmployeeImport", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Employer", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("AssociationId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AssociationID");

                    b.Property<string>("BusinessDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Dba")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DBA");

                    b.Property<string>("FEIN")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("EmployerIdentificationNumber");

                    b.Property<bool>("IsAssociationMember")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "FEIN" }, "IX_EmployerIdentificationNumber");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Employers_Organizations");

                    b.ToTable("Employers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.EmployerRosterView", b =>
                {
                    b.Property<string>("AssociationId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ChapterId")
                        .HasColumnType("int");

                    b.Property<string>("Dba")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FEIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("GUID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastReportedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayrollContactEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayrollContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayrollContactLastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayrollContactPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RelationshipStatusId")
                        .HasColumnType("int");

                    b.ToTable((string)null);

                    b.ToView("EmployerRosterView", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.EmployerSimpleId", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("EmployersSimpleId", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.EmployersToAgreement", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("EmployerId")
                        .HasColumnType("int")
                        .HasColumnName("EmployerID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "EmployerId", "AgreementId", "Id" }, "IX_EmployersToAgreements_Covering");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EmployerId", "AgreementId", "Id" }, "IX_EmployersToAgreements_Covering"), 80);

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_EmployersToAgreements_Agreements");

                    b.HasIndex(new[] { "EmployerId" }, "IX_FK_EmployersToAgreements_Employers");

                    b.ToTable("EmployersToAgreements", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Epruser", b =>
                {
                    b.Property<Guid>("AspnetUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ASPNetUserID");

                    b.Property<int?>("BannerImageId")
                        .HasColumnType("int")
                        .HasColumnName("BannerImageID");

                    b.Property<string>("CurrentSessionId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("CurrentSessionID");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)");

                    b.Property<string>("PhoneNumberExtension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PhoneTypeId")
                        .HasColumnType("int");

                    b.Property<string>("PreferredMfamethod")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("PreferredMFAMethod");

                    b.HasKey("AspnetUserId");

                    b.HasIndex(new[] { "BannerImageId" }, "IX_FK_EPRUsers_Images");

                    b.HasIndex(new[] { "OrganizationId" }, "IX_FK_EPRUsers_Organizations");

                    b.HasIndex(new[] { "AspnetUserId" }, "IX_FK_EPRUsers_aspnet_Users");

                    b.ToTable("EPRUsers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.FundAdministrator", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.HasKey("Id")
                        .HasName("PK_Families_1");

                    b.HasIndex(new[] { "Id" }, "IX_FK_FundAdministrators_Organizations");

                    b.ToTable("FundAdministrators", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.FundingComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("EmployerId")
                        .HasColumnType("int")
                        .HasColumnName("EmployerID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<DateTime>("WorkMonth")
                        .HasColumnType("datetime");

                    b.HasKey("Id")
                        .HasName("PK_Core.FundingComments");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_Core.FundingComments_Core.Reports");

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_FundingComments_Agreements");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_FundingComments_Benefits");

                    b.HasIndex(new[] { "EmployerId" }, "IX_FK_FundingComments_Employers");

                    b.HasIndex(new[] { "ReportId", "BenefitId" }, "IX_FundingComments")
                        .IsUnique();

                    b.ToTable("FundingComments", "Core", t =>
                        {
                            t.HasTrigger("Trigger_FundingComments_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.FundingCommentDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("FundingCommentsId")
                        .HasColumnType("int")
                        .HasColumnName("FundingCommentsID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "OrganizationId" }, "IX_FK_FundingCommentDetails_Organizations");

                    b.HasIndex(new[] { "Id" }, "IX_FundingCommentDetails");

                    b.ToTable("FundingCommentDetails", "Core", t =>
                        {
                            t.HasTrigger("Trigger_FundingCommentDetails_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.GetExpressCheckoutDetailsResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Ack")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ACK");

                    b.Property<string>("Billingagreementacceptedstatus")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BILLINGAGREEMENTACCEPTEDSTATUS");

                    b.Property<string>("Build")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("BUILD");

                    b.Property<string>("Checkoutstatus")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CHECKOUTSTATUS");

                    b.Property<string>("Correlationid")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("CORRELATIONID");

                    b.Property<string>("Countrycode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)")
                        .HasColumnName("COUNTRYCODE");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("EMAIL");

                    b.Property<string>("Errorcode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ERRORCODE");

                    b.Property<string>("Firstname")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("FIRSTNAME");

                    b.Property<string>("Lastname")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("LASTNAME");

                    b.Property<string>("Longmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LONGMESSAGE");

                    b.Property<int?>("OrderId")
                        .HasColumnType("int")
                        .HasColumnName("OrderID");

                    b.Property<string>("Payerid")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)")
                        .HasColumnName("PAYERID");

                    b.Property<string>("Payerstatus")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PAYERSTATUS");

                    b.Property<string>("Phonenum")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PHONENUM");

                    b.Property<string>("SessionId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("SessionID");

                    b.Property<string>("Severritycode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SEVERRITYCODE");

                    b.Property<string>("Shortmessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SHORTMESSAGE");

                    b.Property<DateTime?>("Timestamp")
                        .HasColumnType("datetime")
                        .HasColumnName("TIMESTAMP");

                    b.Property<string>("Token")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("TOKEN");

                    b.Property<string>("Version")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasColumnName("VERSION");

                    b.HasKey("Id")
                        .HasName("PK_PayPal.GetExpressCheckoutDetailsResponse");

                    b.ToTable("GetExpressCheckoutDetailsResponse", "PayPal");
                });

            modelBuilder.Entity("backend.Data.Models.GuidSubstitution", b =>
                {
                    b.Property<Guid>("Guid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("GUID");

                    b.Property<int>("SubstitutionTypeId")
                        .HasColumnType("int");

                    b.Property<Guid>("SubstitutionGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("SubstitutionGUID");

                    b.HasKey("Guid", "SubstitutionTypeId");

                    b.HasIndex(new[] { "SubstitutionTypeId" }, "IX_FK_GuidSubstitution_DGuidSubstitutionType");

                    b.ToTable("GuidSubstitution", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Image", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilePath")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Images");

                    b.ToTable("Images", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Kbaanswer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Answer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Category")
                        .HasColumnType("int");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int")
                        .HasColumnName("QuestionID");

                    b.Property<Guid>("UserGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("UserGUID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "QuestionId" }, "IX_KBAAnswers_QuestionID");

                    b.HasIndex(new[] { "UserGuid" }, "IX_KBAAnswers_UserGUID");

                    b.ToTable("KBAAnswers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Kbaquestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Category")
                        .HasColumnType("int");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("KBAQuestions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.LoggedEvent", b =>
                {
                    b.Property<DateTime>("EventDateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DeventSubTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DEventSubTypeID");

                    b.Property<int>("DeventTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DEventTypeID");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SessionId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SessionID");

                    b.Property<string>("Target")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("EventDateTime");

                    b.HasIndex(new[] { "DeventSubTypeId" }, "IX_FK_LoggedEvents_DEventSubTypes");

                    b.HasIndex(new[] { "DeventTypeId" }, "IX_FK_LoggedEvents_DEventTypes");

                    b.HasIndex(new[] { "EventDateTime" }, "IX_LoggedEvents_EventDateTime");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "EventDateTime" }, "IX_LoggedEvents_EventDateTime"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EventDateTime" }, "IX_LoggedEvents_EventDateTime"), 80);

                    b.HasIndex(new[] { "EventDateTime", "DeventTypeId" }, "IX_LoggedEvents_EventDateTime_DEventTypeID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EventDateTime", "DeventTypeId" }, "IX_LoggedEvents_EventDateTime_DEventTypeID"), 80);

                    b.HasIndex(new[] { "EventDateTime", "UserName" }, "IX_LoggedEvents_EventDateTime_UserName");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EventDateTime", "UserName" }, "IX_LoggedEvents_EventDateTime_UserName"), 80);

                    b.ToTable("LoggedEvents", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ManhoursReportRow", b =>
                {
                    b.Property<string>("Apr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Aug")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Dec")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Employer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Feb")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Jan")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Jul")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Jun")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mar")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("May")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NECA")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nov")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Oct")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Relationship")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sep")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("YearToDate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Year to Date");

                    b.ToTable("ManhoursReportRows", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.MemberDiscountsForAssociationReport", b =>
                {
                    b.Property<string>("Necaid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NECAID");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 10)");

                    b.HasKey("Necaid")
                        .HasName("PK_MemberDiscountsForAssociationReport031422");

                    b.ToTable("MemberDiscountsForAssociationReport", "NECA");
                });

            modelBuilder.Entity("backend.Data.Models.MemberDiscountsForAssociationReportBackup", b =>
                {
                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 10)");

                    b.Property<string>("Necaid")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NECAID");

                    b.ToTable("MemberDiscountsForAssociationReportBackup", "NECA");
                });

            modelBuilder.Entity("backend.Data.Models.MfaRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id")
                        .HasName("PK__MfaRecor__3214EC27F19CCED5");

                    b.ToTable("MfaRecords", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Mm8", b =>
                {
                    b.Property<int>("ConstitId")
                        .HasColumnType("int")
                        .HasColumnName("constit_id");

                    b.Property<string>("Discount")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("discount");

                    b.ToTable("mm8", "NECA");
                });

            modelBuilder.Entity("backend.Data.Models.Mm9", b =>
                {
                    b.Property<string>("ConstitId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("constit_id");

                    b.Property<string>("Discount")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("discount");

                    b.ToTable("mm9", "NECA");
                });

            modelBuilder.Entity("backend.Data.Models.Nachaconfiguration", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("CollectingAgentId")
                        .HasColumnType("int")
                        .HasColumnName("CollectingAgentID");

                    b.Property<string>("CompanyDescriptiveDate")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<string>("CompanyDiscretionaryData")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CompanyIdentification")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(16)
                        .IsUnicode(false)
                        .HasColumnType("varchar(16)");

                    b.Property<string>("ImmediateDestinationName")
                        .HasMaxLength(23)
                        .IsUnicode(false)
                        .HasColumnType("varchar(23)");

                    b.Property<string>("ImmediateOrigin")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("ImmediateOriginName")
                        .HasMaxLength(23)
                        .IsUnicode(false)
                        .HasColumnType("varchar(23)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OffsetAccountNumber")
                        .HasMaxLength(17)
                        .IsUnicode(false)
                        .HasColumnType("varchar(17)");

                    b.Property<string>("OffsetDescription")
                        .HasMaxLength(22)
                        .IsUnicode(false)
                        .HasColumnType("varchar(22)");

                    b.Property<bool>("OffsetEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("OffsetRoutingNumber")
                        .HasMaxLength(9)
                        .IsUnicode(false)
                        .HasColumnType("varchar(9)");

                    b.Property<string>("ReferenceCode")
                        .HasMaxLength(8)
                        .IsUnicode(false)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("RoutingNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK_NATCHAConfigurations");

                    b.ToTable("NACHAConfigurations", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.NecaAmfRate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int>("CalculationId")
                        .HasColumnType("int")
                        .HasColumnName("CalculationID");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("CategoryID");

                    b.Property<int>("RateScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("RateScheduleID");

                    b.HasKey("Id")
                        .HasName("PK__NecaAmfR__3214EC27CB276B63");

                    b.ToTable("NecaAmfRates", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.NecaAmfRateSchedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<DateTime?>("EffectiveEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EffectiveStartDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id")
                        .HasName("PK__NecaAmfR__3214EC27FA7E15EA");

                    b.ToTable("NecaAmfRateSchedules", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.NecaAmfStep", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Maximum")
                        .HasColumnType("decimal(28, 10)");

                    b.Property<decimal?>("Minimum")
                        .HasColumnType("decimal(28, 10)");

                    b.Property<int>("RateId")
                        .HasColumnType("int")
                        .HasColumnName("RateID");

                    b.Property<decimal?>("Value")
                        .HasColumnType("decimal(28, 10)");

                    b.HasKey("Id")
                        .HasName("PK__NecaAmfS__3214EC2743D5B285");

                    b.ToTable("NecaAmfSteps", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.NewsItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ImageURL");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("NavigationUrl")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NavigationURL");

                    b.Property<DateTime?>("PublishDate")
                        .HasColumnType("datetime");

                    b.Property<int>("RoleGroup")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK__NewsItems__41D2933E");

                    b.HasIndex(new[] { "RoleGroup" }, "IX_FK_NewsItems_ToRoleGroups");

                    b.ToTable("NewsItems", "Core", t =>
                        {
                            t.HasTrigger("Trigger_NewsItems_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.Newtable", b =>
                {
                    b.Property<string>("Discount")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Necaid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NECAID");

                    b.ToTable("newtable", "NECA");
                });

            modelBuilder.Entity("backend.Data.Models.Note", b =>
                {
                    b.Property<int>("RootId")
                        .HasColumnType("int")
                        .HasColumnName("RootID")
                        .HasComment("The ID of the root object with which this note is associated.");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Body")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("RootId", "Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Notes_Root");

                    b.HasIndex(new[] { "RootId" }, "IX_FK_Notes_RootOwner");

                    b.ToTable("Notes", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConfirmationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.Property<int>("StatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StatusID")
                        .HasDefaultValueSql("((1))");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("StatusId");

                    b.ToTable("Orders", "ShoppingCart");
                });

            modelBuilder.Entity("backend.Data.Models.OrderDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("OrderId")
                        .HasColumnType("int")
                        .HasColumnName("OrderID");

                    b.Property<int>("PaymentTypeId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentTypeID");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(6, 2)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductID");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("PaymentTypeId");

                    b.HasIndex("ProductId");

                    b.ToTable("OrderDetails", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_OrderDetails_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.OregonPacificReportRow", b =>
                {
                    b.Property<string>("Agreement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssociationMember")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Association Member");

                    b.Property<string>("BenefitName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("BenefitTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Benefit Total");

                    b.Property<string>("Classification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("EEClassificationCount")
                        .HasColumnType("int")
                        .HasColumnName("EE Classification Count");

                    b.Property<string>("Employer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalHoursReported")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Total Hours Reported");

                    b.Property<decimal?>("TotalPayrollReported")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Total Payroll Reported");

                    b.Property<DateTime?>("WorkMonth")
                        .HasColumnType("datetime2")
                        .HasColumnName("Work Month");

                    b.ToTable("OregonPacificReportRows", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Organization", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DorganizationTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DOrganizationTypeID")
                        .HasComment("This should correspond to a value in Core.DEntityTypes");

                    b.Property<string>("LogoFilePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int?>("OptimisticLockField")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DorganizationTypeId" }, "IX_FK_Organizations_DOrganizationTypes");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Organizations_Parties");

                    b.HasIndex(new[] { "Id", "DorganizationTypeId" }, "IX_Organizations_Covering");

                    b.HasIndex(new[] { "Name" }, "IX_Organizations_Name");

                    b.ToTable("Organizations", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PaperProcessingAuthorization", b =>
                {
                    b.Property<int>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<int>("PartyId")
                        .HasColumnType("int")
                        .HasColumnName("PartyID");

                    b.HasKey("ChapterId", "PartyId");

                    b.ToTable("PaperProcessingAuthorizations", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PartiesToContactMechanism", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("ContactMechanismId")
                        .HasColumnType("int")
                        .HasColumnName("ContactMechanismID");

                    b.Property<int>("PartyId")
                        .HasColumnType("int")
                        .HasColumnName("PartyID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ContactMechanismId" }, "IX_FK_PartiesToContactMechanisms_ContactMechanisms");

                    b.HasIndex(new[] { "PartyId" }, "IX_FK_PartiesToContactMechanisms_Parties");

                    b.HasIndex(new[] { "Id" }, "IX_FK_PartiesToContactMechanisms_Root");

                    b.HasIndex(new[] { "PartyId", "ContactMechanismId" }, "IX_PartiesToContactMechanisms");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "PartyId", "ContactMechanismId" }, "IX_PartiesToContactMechanisms"), 80);

                    b.ToTable("PartiesToContactMechanisms", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Party", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DpartyTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DPartyTypeID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DpartyTypeId" }, "IX_FK_Parties_DPartyTypes");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Parties_Root");

                    b.HasIndex(new[] { "Id", "DpartyTypeId" }, "IX_Parties");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id", "DpartyTypeId" }, "IX_Parties"), 80);

                    b.ToTable("Parties", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PasswordResetAuthorization", b =>
                {
                    b.Property<Guid>("AspnetUserGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ASPNetUserGUID");

                    b.Property<DateTime>("CreationTimestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<Guid>("PublicGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("PublicGUID");

                    b.Property<bool>("Used")
                        .HasColumnType("bit");

                    b.ToTable("PasswordResetAuthorizations", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PayStub", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OldId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TimeSheetId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex(new[] { "TimeSheetId" }, "IX_PayStubs_TimeSheetId");

                    b.ToTable("PayStubs", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.PayStubDetail", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int?>("AgreementId")
                        .HasColumnType("int");

                    b.Property<float?>("Bonus")
                        .HasColumnType("real");

                    b.Property<int?>("ClassificationId")
                        .HasColumnType("int");

                    b.Property<string>("CostCenter")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("DTHours")
                        .HasColumnType("real")
                        .HasColumnName("DTHours");

                    b.Property<string>("EarningsCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("Expenses")
                        .HasColumnType("real");

                    b.Property<float?>("HourlyRate")
                        .HasColumnType("real");

                    b.Property<string>("JobCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("OTHours")
                        .HasColumnType("real")
                        .HasColumnName("OTHours");

                    b.Property<Guid?>("OldId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("PayStubId")
                        .HasColumnType("int");

                    b.Property<int?>("ReportLineItemId")
                        .HasColumnType("int");

                    b.Property<float?>("STHours")
                        .HasColumnType("real")
                        .HasColumnName("STHours");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int");

                    b.Property<DateOnly>("WorkDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.HasIndex("PayStubId");

                    b.HasIndex("ReportLineItemId");

                    b.ToTable("PayStubDetails", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Payment", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("CollectingAgentId")
                        .HasColumnType("int")
                        .HasColumnName("CollectingAgentID")
                        .HasComment("The ID of the collecting agent's organization that created the payment. This ensures that the overpayments are always tied to the organization.");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime");

                    b.Property<string>("PaymentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_Payments_Reports");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Payments_Root");

                    b.HasIndex(new[] { "CollectingAgentId" }, "IX_Payments_CollectingAgentID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "CollectingAgentId" }, "IX_Payments_CollectingAgentID"), 80);

                    b.HasIndex(new[] { "ReportId" }, "IX_Payments_ReportID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ReportId" }, "IX_Payments_ReportID"), 80);

                    b.ToTable("Payments", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PaymentDetail", b =>
                {
                    b.Property<int>("PaymentId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID")
                        .HasComment("If this is null, then the amount represents money that went to no benefit (i.e., an overpayment)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("PaymentId", "BenefitId");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_PaymentDetails_Benefits");

                    b.HasIndex(new[] { "PaymentId" }, "IX_FK_PaymentDetails_Payments");

                    b.ToTable("PaymentDetails", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PaymentMethod", b =>
                {
                    b.Property<Guid>("AssociatedGuid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AssociatedGUID")
                        .HasComment("Either a default payment method for an employer or an elected payment method for a given payment.");

                    b.Property<int>("DpaymentMethodTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DPaymentMethodTypeID");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.HasKey("AssociatedGuid")
                        .HasName("PK_PaymentMethods_1");

                    b.HasIndex(new[] { "DpaymentMethodTypeId" }, "IX_FK_PaymentMethods_DPaymentMethodType");

                    b.HasIndex(new[] { "Id" }, "IX_FK_PaymentMethods_Root");

                    b.ToTable("PaymentMethods", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Person", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DgenderId")
                        .HasColumnType("int")
                        .HasColumnName("DGenderID");

                    b.Property<int>("DpersonTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DPersonTypeID");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Suffix")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Title")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DgenderId" }, "IX_FK_Persons_DGenders");

                    b.HasIndex(new[] { "DpersonTypeId" }, "IX_FK_Persons_DPersonTypes");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Persons_Parties");

                    b.HasIndex(new[] { "LastName" }, "IX_LastNameCovering");

                    b.ToTable("Persons", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.PhoneNumber", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DphoneNumberTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DPhoneNumberTypeID");

                    b.Property<string>("Extension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PhoneNumber");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_PhoneNumbers_ContactMechanisms");

                    b.HasIndex(new[] { "DphoneNumberTypeId" }, "IX_FK_PhoneNumbers_DPhoneNumberTypes");

                    b.ToTable("PhoneNumbers", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Features")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id")
                        .HasName("PK__Products__478B6C94");

                    b.ToTable("Products", "Core", t =>
                        {
                            t.HasTrigger("Trigger_Products_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.Product1", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("LargeImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("MediumImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("PaymentTypeId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentTypeID");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(10, 2)");

                    b.Property<string>("ShortDescription")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("SmallImagePath")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTypeId");

                    b.ToTable("Products", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_Products_LastModifiedDate")
                                .HasDatabaseName("Trigger_Products_LastModifiedDate1");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.ProductCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("DcategoryId")
                        .HasColumnType("int")
                        .HasColumnName("DCategoryID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductID");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex(new[] { "DcategoryId", "ProductId" }, "Unique_ProductCategory")
                        .IsUnique();

                    b.ToTable("ProductCategory", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_ProductCategory_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.ProductsToOrganizationType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("OrganizationTypeId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationTypeID");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductID");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex(new[] { "OrganizationTypeId", "ProductId" }, "Unique_ProductsToOrganizationTypes")
                        .IsUnique();

                    b.ToTable("ProductsToOrganizationTypes", "ShoppingCart", t =>
                        {
                            t.HasTrigger("Trigger_ProductsToOrganizationTypes_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.Rate", b =>
                {
                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int>("ClassificationNameId")
                        .HasColumnType("int")
                        .HasColumnName("ClassificationNameID");

                    b.Property<string>("CustomCalculation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DcalculationMethodId")
                        .HasColumnType("int")
                        .HasColumnName("DCalculationMethodID");

                    b.Property<int>("DcalculationModifierId")
                        .HasColumnType("int")
                        .HasColumnName("DCalculationModifierID");

                    b.Property<decimal?>("MaximumBound")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MinimumBound")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("RateScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("RateScheduleID");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int")
                        .HasColumnName("SubClassificationID");

                    b.Property<decimal?>("Value")
                        .HasColumnType("decimal(28, 10)")
                        .HasComment("Value can be null if the calculation method is \"Not Applicable\"");

                    b.HasIndex(new[] { "DcalculationMethodId", "BenefitId" }, "IX_DCalculationMethodID_BenefitID");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_Rates_Benefits");

                    b.HasIndex(new[] { "ClassificationNameId" }, "IX_FK_Rates_ClassificationNames");

                    b.HasIndex(new[] { "DcalculationMethodId" }, "IX_FK_Rates_DCalculationMethods");

                    b.HasIndex(new[] { "DcalculationModifierId" }, "IX_FK_Rates_DCalculationModifiers");

                    b.HasIndex(new[] { "RateScheduleId" }, "IX_FK_Rates_RateSchedules");

                    b.HasIndex(new[] { "SubClassificationId" }, "IX_FK_Rates_SubClassifications");

                    b.HasIndex(new[] { "RateScheduleId", "BenefitId", "ClassificationNameId", "SubClassificationId" }, "IX_Rates")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex(new[] { "RateScheduleId", "BenefitId", "ClassificationNameId", "SubClassificationId" }, "IX_Rates"));
                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "RateScheduleId", "BenefitId", "ClassificationNameId", "SubClassificationId" }, "IX_Rates"), 80);

                    b.ToTable("Rates", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.RateSchedule", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<DateTime?>("EffectiveEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EffectiveStartDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "AgreementId" }, "IX_AgreementID");

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_RateSchedules_Agreements");

                    b.HasIndex(new[] { "Id" }, "IX_FK_RateSchedules_Root");

                    b.ToTable("RateSchedules", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Relationship", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int?>("DrelationshipSubTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DRelationshipSubTypeID");

                    b.Property<int>("DrelationshipTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DRelationshipTypeID");

                    b.Property<int>("LeftPartyId")
                        .HasColumnType("int")
                        .HasColumnName("LeftPartyID");

                    b.Property<int?>("RightPartyId")
                        .HasColumnType("int")
                        .HasColumnName("RightPartyID")
                        .HasComment("The RightPartyID should only be null for one-to-one type relationships.");

                    b.HasKey("Id");

                    b.HasIndex("DrelationshipSubTypeId", "DrelationshipTypeId");

                    b.HasIndex(new[] { "DrelationshipTypeId" }, "IX_FK_Relationships_DRelationshipSubTypes");

                    b.HasIndex(new[] { "DrelationshipTypeId" }, "IX_FK_Relationships_DRelationshipTypes");

                    b.HasIndex(new[] { "LeftPartyId" }, "IX_FK_Relationships_LeftParties");

                    b.HasIndex(new[] { "RightPartyId" }, "IX_FK_Relationships_RightParties");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Relationships_Root");

                    b.HasIndex(new[] { "LeftPartyId", "RightPartyId", "DrelationshipTypeId", "DrelationshipSubTypeId", "Id" }, "IX_Relationships_Covering");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "LeftPartyId", "RightPartyId", "DrelationshipTypeId", "DrelationshipSubTypeId", "Id" }, "IX_Relationships_Covering"), 80);

                    b.HasIndex(new[] { "DrelationshipTypeId", "RightPartyId", "LeftPartyId" }, "IX_Relationships_DRelationshipTypeIDRightPartyIDLeftPartyID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "DrelationshipTypeId", "RightPartyId", "LeftPartyId" }, "IX_Relationships_DRelationshipTypeIDRightPartyIDLeftPartyID"), 80);

                    b.HasIndex(new[] { "DrelationshipTypeId" }, "IX_Relationships_DRelationshipTypeID_Covering");

                    b.HasIndex(new[] { "RightPartyId" }, "IX_RightPartyIDCovering");

                    b.ToTable("Relationships", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.RelationshipStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DrelationshipStatusId")
                        .HasColumnType("int")
                        .HasColumnName("DRelationshipStatusID");

                    b.Property<int>("RelationshipId")
                        .HasColumnType("int")
                        .HasColumnName("RelationshipID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DrelationshipStatusId" }, "IX_FK_RelationshipStatuses_DRelationshipStatuses");

                    b.HasIndex(new[] { "RelationshipId" }, "IX_FK_RelationshipStatuses_Relationships");

                    b.HasIndex(new[] { "Id" }, "IX_FK_RelationshipStatuses_Root");

                    b.HasIndex(new[] { "RelationshipId", "DrelationshipStatusId", "Id" }, "IX_RelationshipStatuses_Covering");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "RelationshipId", "DrelationshipStatusId", "Id" }, "IX_RelationshipStatuses_Covering"), 80);

                    b.ToTable("RelationshipStatuses", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Report", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int?>("AggregateEmployeeCount")
                        .HasColumnType("int");

                    b.Property<bool>("AggregatesOnly")
                        .HasColumnType("bit");

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<int?>("AmendedReportId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("AmendedReportID")
                        .HasDefaultValueSql("((0))");

                    b.Property<int>("DreportStatusId")
                        .HasColumnType("int")
                        .HasColumnName("DReportStatusID");

                    b.Property<int>("EmployerId")
                        .HasColumnType("int")
                        .HasColumnName("EmployerID");

                    b.Property<bool?>("FullyPaid")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("PeriodStartDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("RateScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("RateScheduleID");

                    b.Property<DateTime?>("SubmissionDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("WorkMonth")
                        .HasColumnType("datetime")
                        .HasComment("This should always be the first of the month with no time elements (e.g., 1/1/2008 00:00:00.000)");

                    b.Property<bool>("ZeroHour")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "AgreementId" }, "IX_FK_Reports_Agreements");

                    b.HasIndex(new[] { "DreportStatusId" }, "IX_FK_Reports_DReportStatuses");

                    b.HasIndex(new[] { "EmployerId" }, "IX_FK_Reports_Employers");

                    b.HasIndex(new[] { "RateScheduleId" }, "IX_FK_Reports_RateSchedules");

                    b.HasIndex(new[] { "AmendedReportId" }, "IX_FK_Reports_Reports");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Reports_Root");

                    b.HasIndex(new[] { "AgreementId" }, "IX_Reports_AgreementID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "AgreementId" }, "IX_Reports_AgreementID"), 80);

                    b.HasIndex(new[] { "EmployerId" }, "IX_Reports_EmployerID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EmployerId" }, "IX_Reports_EmployerID"), 80);

                    b.HasIndex(new[] { "EmployerId", "AgreementId", "WorkMonth" }, "IX_Reports_EmployerIDAgreementIDWorkMonth");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EmployerId", "AgreementId", "WorkMonth" }, "IX_Reports_EmployerIDAgreementIDWorkMonth"), 80);

                    b.HasIndex(new[] { "Id", "AmendedReportId", "DreportStatusId" }, "IX_Reports_IDAmendedReportIDDReportStatusID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id", "AmendedReportId", "DreportStatusId" }, "IX_Reports_IDAmendedReportIDDReportStatusID"), 80);

                    b.HasIndex(new[] { "Id", "ZeroHour", "WorkMonth", "AgreementId", "EmployerId" }, "_dta_index_Reports_11_453120905__K1_K14_K7_K2_K4_9");

                    b.ToTable("Reports", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ReportLineItem", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID")
                        .HasComment("");

                    b.Property<int?>("AmendedLineItemId")
                        .HasColumnType("int")
                        .HasColumnName("AmendedLineItemID");

                    b.Property<int?>("ClassificationNameId")
                        .HasColumnType("int")
                        .HasColumnName("ClassificationNameID");

                    b.Property<int?>("DamendmentActionId")
                        .HasColumnType("int")
                        .HasColumnName("DAmendmentActionID");

                    b.Property<int?>("EmployeeId")
                        .HasColumnType("int")
                        .HasColumnName("EmployeeID");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int")
                        .HasColumnName("SubClassificationID");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ClassificationNameId" }, "IX_FK_ReportLineItems_ClassificationNames");

                    b.HasIndex(new[] { "DamendmentActionId" }, "IX_FK_ReportLineItems_DAmendmentActions");

                    b.HasIndex(new[] { "EmployeeId" }, "IX_FK_ReportLineItems_Employees");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_ReportLineItems_Reports");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ReportLineItems_Root");

                    b.HasIndex(new[] { "SubClassificationId" }, "IX_FK_ReportLineItems_SubClassifications");

                    b.HasIndex(new[] { "EmployeeId" }, "IX_ReportLineItems_EmployeeID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "EmployeeId" }, "IX_ReportLineItems_EmployeeID"), 80);

                    b.HasIndex(new[] { "ReportId" }, "IX_ReportLineItems_ReportID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ReportId" }, "IX_ReportLineItems_ReportID"), 80);

                    b.HasIndex(new[] { "ReportId", "AmendedLineItemId" }, "IX_ReportLineItems_ReportIDAmendedLineItemID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ReportId", "AmendedLineItemId" }, "IX_ReportLineItems_ReportIDAmendedLineItemID"), 80);

                    b.ToTable("ReportLineItems", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ReportLineItemDetail", b =>
                {
                    b.Property<int>("ReportLineItemId")
                        .HasColumnType("int")
                        .HasColumnName("ReportLineItemID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(28, 10)");

                    b.Property<object>("VariantValue")
                        .HasColumnType("sql_variant");

                    b.HasKey("ReportLineItemId", "BenefitId");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_ReportLineItemDetails_Benefits");

                    b.HasIndex(new[] { "ReportLineItemId" }, "IX_FK_ReportLineItemDetails_ReportLineItems");

                    b.ToTable("ReportLineItemDetails", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ReportSubscription", b =>
                {
                    b.Property<int>("OrganizationId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationID");

                    b.Property<int>("CustomReportId")
                        .HasColumnType("int")
                        .HasColumnName("CustomReportID");

                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("OptimisticLockField")
                        .HasColumnType("int");

                    b.Property<int>("ReportSubscriptionTypeId")
                        .HasColumnType("int")
                        .HasColumnName("ReportSubscriptionTypeID");

                    b.HasKey("OrganizationId", "CustomReportId");

                    b.HasIndex("ReportSubscriptionTypeId");

                    b.ToTable("ReportSubscriptions", "Custom");
                });

            modelBuilder.Entity("backend.Data.Models.ReportSuppression", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("CollectingAgentId")
                        .HasColumnType("int")
                        .HasColumnName("CollectingAgentID");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<bool>("Suppress")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_ReportSuppressions_Reports");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ReportSuppressions_Root");

                    b.HasIndex(new[] { "CollectingAgentId", "ReportId" }, "IX_ReportSuppressions_Report_CollectingAgent");

                    b.ToTable("ReportSuppressions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ReportedBenefitReleaseAuthorization", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.HasKey("Id")
                        .HasName("PK_ReportedBenefitReleaseAuthentications");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ReportReleaseAuthentications_Root");

                    b.HasIndex(new[] { "BenefitId" }, "IX_FK_ReportedBenefitReleaseAuthentications_Benefits");

                    b.HasIndex(new[] { "ReportId" }, "IX_FK_ReportedBenefitReleaseAuthorizations_Reports");

                    b.HasIndex(new[] { "ReportId", "BenefitId" }, "IX_ReportedBenefitReleaseAuthorizations")
                        .IsUnique();

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "ReportId", "BenefitId" }, "IX_ReportedBenefitReleaseAuthorizations"), 80);

                    b.ToTable("ReportedBenefitReleaseAuthorizations", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.RoleGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.HasKey("Id")
                        .HasName("PK__RoleGroups__063C00CD");

                    b.ToTable("RoleGroups", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Root", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<Guid?>("Guid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("GUID");

                    b.Property<DateTime>("LastModificationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_Root");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id" }, "IX_Root"), 80);

                    b.HasIndex(new[] { "Guid" }, "IX_RootGUID");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Guid" }, "IX_RootGUID"), 80);

                    b.HasIndex(new[] { "Id" }, "_dta_index_Root_11_1306695953__K1_5");

                    b.ToTable("Root", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.ServiceSubscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("PartyId")
                        .HasColumnType("int")
                        .HasColumnName("PartyID");

                    b.Property<int>("SubscriptionServiceId")
                        .HasColumnType("int")
                        .HasColumnName("SubscriptionServiceID");

                    b.HasKey("Id")
                        .HasName("PK__ServiceS__3214EC276BF44AF0");

                    b.HasIndex("PartyId");

                    b.HasIndex("SubscriptionServiceId");

                    b.ToTable("ServiceSubscriptions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DSettingsTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DSettingsTypeID");

                    b.Property<DateTime?>("LastModificationDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("LastModifiedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifiedBy");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("OwnerID");

                    b.Property<string>("OwnerType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK__Settings__3214EC270CAC4B1B");

                    b.HasIndex(new[] { "DSettingsTypeId" }, "IX_Settings_DSettingsTypeID");

                    b.ToTable("Settings", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.SpExecutionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime");

                    b.Property<Guid>("Guid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("GUID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RowCount")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("SpExecutionLog", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.SubClassification", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int?>("ChapterId")
                        .HasColumnType("int")
                        .HasColumnName("ChapterID");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DstatusId")
                        .HasColumnType("int")
                        .HasColumnName("DStatusID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id")
                        .HasName("PK_JobClassificationSubTypes");

                    b.HasIndex(new[] { "ChapterId" }, "IX_FK_JobClassificationSubTypes_Chapters");

                    b.HasIndex(new[] { "DstatusId" }, "IX_FK_JobClassificationSubTypes_DStatuses");

                    b.HasIndex(new[] { "Id" }, "IX_FK_JobClassificationSubTypes_Root");

                    b.ToTable("SubClassifications", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.SubscriptionService", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id")
                        .HasName("PK__Subscrip__3214EC27F4F9295F");

                    b.ToTable("SubscriptionServices", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.SystemPassword", b =>
                {
                    b.Property<string>("password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("SystemPasswords", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.TfEmail", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("EmailAddressTypeID")
                        .HasColumnType("int");

                    b.Property<string>("EmailAddressTypeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("GUID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("LastModificationDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PartyGUID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("PartyID")
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.ToTable("TfEmails", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.ThirdPartyEmployerId", b =>
                {
                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreationDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<int>("EmployerId")
                        .HasColumnType("int")
                        .HasColumnName("EmployerID");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("ThirdPartyEmployerId1")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ThirdPartyEmployerID");

                    b.Property<int>("ThirdPartyId")
                        .HasColumnType("int")
                        .HasColumnName("ThirdPartyID");

                    b.HasIndex(new[] { "EmployerId" }, "IX_FK_ThirdPartyEmployerIDs_Employers");

                    b.HasIndex(new[] { "Id" }, "IX_FK_ThirdPartyEmployerIDs_Root");

                    b.HasIndex(new[] { "ThirdPartyId" }, "IX_FK_ThirdPartyEmployerIDs_ThirdPartyID");

                    b.HasIndex(new[] { "EmployerId", "ThirdPartyId" }, "IX_ThirdPartyEmployerIDs")
                        .IsUnique();

                    b.ToTable("ThirdPartyEmployerIDs", "Core", t =>
                        {
                            t.HasTrigger("Trigger_ThirdPartyEmployerIDs_LastModifiedDate");
                        });

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("backend.Data.Models.TimeSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedByUserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("CreationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("EmployerGuid")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ModificationDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedByUserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OldId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateOnly?>("PayPeriodEndDate")
                        .HasColumnType("date");

                    b.Property<bool?>("ShowBonusColumn")
                        .HasColumnType("bit");

                    b.Property<bool?>("ShowCostCenterColumn")
                        .HasColumnType("bit");

                    b.Property<bool?>("ShowDTHoursColumn")
                        .HasColumnType("bit")
                        .HasColumnName("ShowDTHoursColumn");

                    b.Property<bool?>("ShowEarningsCodesColumn")
                        .HasColumnType("bit");

                    b.Property<bool?>("ShowExpensesColumn")
                        .HasColumnType("bit");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TimeSheets", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.TimeSheetEmployeeDeductibleBenefit", b =>
                {
                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ReportLineItemID")
                        .HasColumnType("int");

                    b.ToTable((string)null);

                    b.ToView("TimeSheetEmployeeDeductibleBenefits", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.TimeSheetExportLineItem", b =>
                {
                    b.Property<float?>("Bonus")
                        .HasColumnType("real");

                    b.Property<string>("CostCenter")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("DTHours")
                        .HasColumnType("real");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<float?>("Expenses")
                        .HasColumnType("real");

                    b.Property<float?>("HourlyRate")
                        .HasColumnType("real");

                    b.Property<Guid>("ID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("JobCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("OTHours")
                        .HasColumnType("real");

                    b.Property<int?>("ReportLineItemID")
                        .HasColumnType("int");

                    b.Property<string>("SSN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("STHours")
                        .HasColumnType("real");

                    b.Property<string>("UnionClassificationCode")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable((string)null);

                    b.ToView("TimeSheetExportLineItems", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.TimeSheetUnionEmployerPaidBenefit", b =>
                {
                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ChapterID")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ReportLineItemID")
                        .HasColumnType("int");

                    b.ToTable((string)null);

                    b.ToView("TimeSheetUnionEmployerPaidBenefits", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Timeline", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("EffectiveEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EffectiveStartDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOverridden")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Timelines_Root");

                    b.HasIndex(new[] { "IsOverridden" }, "IX_TimeLines_IsOverridden_Covering");

                    b.HasIndex(new[] { "Id", "EffectiveStartDate", "EffectiveEndDate", "IsOverridden" }, "IX_Timelines_Covering");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id", "EffectiveStartDate", "EffectiveEndDate", "IsOverridden" }, "IX_Timelines_Covering"), 80);

                    b.ToTable("Timelines", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.TimelinesOrig", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<DateTime?>("EffectiveEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EffectiveStartDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<bool>("IsOverridden")
                        .HasColumnType("bit");

                    b.HasKey("Id")
                        .HasName("PK_Timelines");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Timelines_Root");

                    b.HasIndex(new[] { "IsOverridden" }, "IX_TimeLines_IsOverridden_Covering");

                    b.HasIndex(new[] { "Id", "EffectiveStartDate", "EffectiveEndDate", "IsOverridden" }, "IX_Timelines_Covering");

                    SqlServerIndexBuilderExtensions.HasFillFactor(b.HasIndex(new[] { "Id", "EffectiveStartDate", "EffectiveEndDate", "IsOverridden" }, "IX_Timelines_Covering"), 80);

                    b.ToTable("TimelinesOrig", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Trade", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.HasKey("Id")
                        .HasName("PK_Affinities");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Affinities_Organizations");

                    b.ToTable("Trades", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.Union", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DefaultDelinquentDay")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValueSql("((20))")
                        .HasComment("Determines the default day of the month that payroll reports are due on. If a payroll report is submitted after this date, it is delinquent.");

                    b.HasKey("Id")
                        .HasName("PK_Brokerages_1");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Brokerages_Organizations");

                    b.ToTable("Unions", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.VClassificationXref", b =>
                {
                    b.Property<string>("Agreement")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("AgreementEndDate")
                        .HasColumnType("datetime");

                    b.Property<int>("AgreementId")
                        .HasColumnType("int")
                        .HasColumnName("AgreementID");

                    b.Property<string>("Benefit")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<string>("Classification")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ClassificationNameId")
                        .HasColumnType("int")
                        .HasColumnName("ClassificationNameID");

                    b.Property<int?>("FundAdministratorId")
                        .HasColumnType("int")
                        .HasColumnName("FundAdministratorID");

                    b.Property<string>("Id")
                        .HasMaxLength(64)
                        .IsUnicode(false)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("SubClassification")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("SubClassificationId")
                        .HasColumnType("int")
                        .HasColumnName("SubClassificationID");

                    b.Property<int>("UnionId")
                        .HasColumnType("int")
                        .HasColumnName("UnionID");

                    b.Property<string>("UnionName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("XrefId")
                        .HasColumnType("int")
                        .HasColumnName("XrefID");

                    b.Property<string>("Xrefvalue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.ToTable((string)null);

                    b.ToView("vClassificationXRefs", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.VCurrentRelationship", b =>
                {
                    b.Property<bool?>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("LeftPartyId")
                        .HasColumnType("int")
                        .HasColumnName("LeftPartyID");

                    b.Property<string>("LeftPartyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Left Party Name");

                    b.Property<string>("RelationshipTypeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RightPartyId")
                        .HasColumnType("int")
                        .HasColumnName("RightPartyID");

                    b.Property<string>("RightPartyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Right Party Name");

                    b.ToTable((string)null);

                    b.ToView("vCurrentRelationships", "Dev");
                });

            modelBuilder.Entity("backend.Data.Models.VEffectiveUserRole", b =>
                {
                    b.Property<string>("LoweredRoleGroupName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("LoweredRoleName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LoweredUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("RoleGroupId")
                        .HasColumnType("int")
                        .HasColumnName("RoleGroupID");

                    b.Property<string>("RoleGroupName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vEffectiveUserRoles", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.VOrganization", b =>
                {
                    b.Property<Guid?>("Guid")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("GUID");

                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable((string)null);

                    b.ToView("vOrganizations", "Dev");
                });

            modelBuilder.Entity("backend.Data.Models.VPayment", b =>
                {
                    b.Property<string>("Agreement")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Benefit")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("BenefitAmount")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("Benefit Amount");

                    b.Property<int>("BenefitId")
                        .HasColumnType("int")
                        .HasColumnName("BenefitID");

                    b.Property<string>("Chapter")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Employer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastModifiedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<decimal>("Payment")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("PaymentId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentID");

                    b.Property<int>("ReportId")
                        .HasColumnType("int")
                        .HasColumnName("ReportID");

                    b.Property<DateTime>("WorkMonth")
                        .HasColumnType("datetime");

                    b.ToTable((string)null);

                    b.ToView("vPayments", "Dev");
                });

            modelBuilder.Entity("backend.Data.Models.VRoleGroupsAndRole", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("RoleDescription")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("RoleGroupDescription")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vRoleGroupsAndRoles", "Core");
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetApplication", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LoweredApplicationName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_Applications", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetMembershipUser", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasColumnType("ntext");

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FailedPasswordAnswerAttemptCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("FailedPasswordAnswerAttemptWindowStart")
                        .HasColumnType("datetime");

                    b.Property<int>("FailedPasswordAttemptCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("FailedPasswordAttemptWindowStart")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsAnonymous")
                        .HasColumnType("bit");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLockedOut")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastActivityDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LastLockoutDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("LastPasswordChangedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LoweredEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("MobileAlias")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("MobilePin")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("MobilePIN");

                    b.Property<string>("PasswordAnswer")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int>("PasswordFormat")
                        .HasColumnType("int");

                    b.Property<string>("PasswordQuestion")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_MembershipUsers", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetProfile", b =>
                {
                    b.Property<int?>("DataSize")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_Profiles", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetRole", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LoweredRoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_Roles", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetUser", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsAnonymous")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastActivityDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LoweredUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("MobileAlias")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_Users", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetUsersInRole", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_UsersInRoles", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetWebPartStatePath", b =>
                {
                    b.Property<Guid>("ApplicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoweredPath")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid>("PathId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_WebPartState_Paths", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetWebPartStateShared", b =>
                {
                    b.Property<int?>("DataSize")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<Guid>("PathId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_WebPartState_Shared", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.VwAspnetWebPartStateUser", b =>
                {
                    b.Property<int?>("DataSize")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("PathId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable((string)null);

                    b.ToView("vw_aspnet_WebPartState_User", (string)null);
                });

            modelBuilder.Entity("backend.Data.Models.Website", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    b.Property<int>("DwebsiteTypeId")
                        .HasColumnType("int")
                        .HasColumnName("DWebsiteTypeID");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("URL");

                    b.HasKey("Id")
                        .HasName("PK_Website");

                    b.HasIndex(new[] { "Id" }, "IX_FK_Websites_ContactMechanisms");

                    b.HasIndex(new[] { "DwebsiteTypeId" }, "IX_FK_Websites_DWebsiteTypes");

                    b.ToTable("Websites", "Core");
                });

            modelBuilder.Entity("AspnetUsersInRole", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Us__RoleI__06455717");

                    b.HasOne("backend.Data.Models.AspnetUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Us__UserI__055132DE");
                });

            modelBuilder.Entity("AuditAuthorization", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", null)
                        .WithMany()
                        .HasForeignKey("AgreementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AuditAuthorizations_Agreements");

                    b.HasOne("backend.Data.Models.Organization", null)
                        .WithMany()
                        .HasForeignKey("OrganizationId")
                        .IsRequired()
                        .HasConstraintName("FK_AuditAuthorizations_Organizations");
                });

            modelBuilder.Entity("RoleGroupsToAspnetRole", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetRole", null)
                        .WithMany()
                        .HasForeignKey("AspnetRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_RoleGroupsToASPNetRoles_aspnet_Roles");

                    b.HasOne("backend.Data.Models.RoleGroup", null)
                        .WithMany()
                        .HasForeignKey("RoleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_RoleGroupsToASPNetRoles_RoleGroups");
                });

            modelBuilder.Entity("RoleGroupsToAspnetUser", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetUser", null)
                        .WithMany()
                        .HasForeignKey("AspnetUserGuid")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_RoleGroupsToASPNetUsers_aspnet_Users");

                    b.HasOne("backend.Data.Models.RoleGroup", null)
                        .WithMany()
                        .HasForeignKey("RoleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_RoleGroupsToASPNetUsers_RoleGroups");
                });

            modelBuilder.Entity("backend.Data.Models.Address", b =>
                {
                    b.HasOne("backend.Data.Models.DaddressType", "DaddressType")
                        .WithMany("Addresses")
                        .HasForeignKey("DaddressTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Addresses_DAddressTypes");

                    b.HasOne("backend.Data.Models.Dcountry", "Dcountry")
                        .WithMany("Addresses")
                        .HasForeignKey("DcountryId")
                        .IsRequired()
                        .HasConstraintName("FK_Addresses_DCountries");

                    b.HasOne("backend.Data.Models.ContactMechanism", "IdNavigation")
                        .WithOne("Address")
                        .HasForeignKey("backend.Data.Models.Address", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Addresses_ContactMechanisms");

                    b.Navigation("DaddressType");

                    b.Navigation("Dcountry");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Agreement", b =>
                {
                    b.HasOne("backend.Data.Models.Chapter", "Chapter")
                        .WithMany("Agreements")
                        .HasForeignKey("ChapterId")
                        .IsRequired()
                        .HasConstraintName("FK_Agreements_Chapters");

                    b.HasOne("backend.Data.Models.DagreementType", "DagreementType")
                        .WithMany("Agreements")
                        .HasForeignKey("DagreementTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Agreements_DAgreementTypes");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Agreement")
                        .HasForeignKey("backend.Data.Models.Agreement", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Agreements_Root");

                    b.HasOne("backend.Data.Models.Employee", "UnionContact")
                        .WithMany("Agreements")
                        .HasForeignKey("UnionContactId")
                        .HasConstraintName("FK_Agreements_Employees");

                    b.HasOne("backend.Data.Models.Union", "Union")
                        .WithMany("Agreements")
                        .HasForeignKey("UnionId")
                        .IsRequired()
                        .HasConstraintName("FK_Agreements_Unions");

                    b.Navigation("Chapter");

                    b.Navigation("DagreementType");

                    b.Navigation("IdNavigation");

                    b.Navigation("Union");

                    b.Navigation("UnionContact");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementClassification", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany()
                        .HasForeignKey("AgreementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AgreementClassifications_Agreements");

                    b.HasOne("backend.Data.Models.ClassificationName", "ClassificationName")
                        .WithMany()
                        .HasForeignKey("ClassificationNameId")
                        .IsRequired()
                        .HasConstraintName("FK_AgreementClassifications_ClassificationNames");

                    b.HasOne("backend.Data.Models.SubClassification", "SubClassification")
                        .WithMany()
                        .HasForeignKey("SubClassificationId")
                        .HasConstraintName("FK_AgreementClassifications_SubClassifications");

                    b.Navigation("Agreement");

                    b.Navigation("ClassificationName");

                    b.Navigation("SubClassification");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementsToBenefit", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("AgreementsToBenefits")
                        .HasForeignKey("AgreementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AgreementsToBenefits_Agreements");

                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("AgreementsToBenefits")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_AgreementsToBenefits_Benefits");

                    b.HasOne("backend.Data.Models.Organization", "CollectingAgent")
                        .WithMany("AgreementsToBenefitCollectingAgents")
                        .HasForeignKey("CollectingAgentId")
                        .HasConstraintName("FK_AgreementsToBenefits_Organizations");

                    b.HasOne("backend.Data.Models.Organization", "FundAdministrator")
                        .WithMany("AgreementsToBenefitFundAdministrators")
                        .HasForeignKey("FundAdministratorId")
                        .HasConstraintName("FK_AgreementsToBenefits_Organizations1");

                    b.Navigation("Agreement");

                    b.Navigation("Benefit");

                    b.Navigation("CollectingAgent");

                    b.Navigation("FundAdministrator");
                });

            modelBuilder.Entity("backend.Data.Models.AgreementsToContract", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany()
                        .HasForeignKey("AgreementId")
                        .IsRequired()
                        .HasConstraintName("FK__Agreement__Agree__264A745A");

                    b.Navigation("Agreement");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetMembership", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetApplication", "Application")
                        .WithMany("AspnetMemberships")
                        .HasForeignKey("ApplicationId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Me__Appli__62FC1ADA");

                    b.HasOne("backend.Data.Models.AspnetUser", "User")
                        .WithOne("AspnetMembership")
                        .HasForeignKey("backend.Data.Models.AspnetMembership", "UserId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Me__UserI__63F03F13");

                    b.Navigation("Application");

                    b.Navigation("User");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPath", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetApplication", "Application")
                        .WithMany("AspnetPaths")
                        .HasForeignKey("ApplicationId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Pa__Appli__1493766E");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPersonalizationAllUser", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetPath", "Path")
                        .WithOne("AspnetPersonalizationAllUser")
                        .HasForeignKey("backend.Data.Models.AspnetPersonalizationAllUser", "PathId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Pe__PathI__1A4C4FC4");

                    b.Navigation("Path");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPersonalizationPerUser", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetPath", "Path")
                        .WithMany("AspnetPersonalizationPerUsers")
                        .HasForeignKey("PathId")
                        .HasConstraintName("FK__aspnet_Pe__PathI__1E1CE0A8");

                    b.HasOne("backend.Data.Models.AspnetUser", "User")
                        .WithMany("AspnetPersonalizationPerUsers")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK__aspnet_Pe__UserI__1F1104E1");

                    b.Navigation("Path");

                    b.Navigation("User");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetProfile", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetUser", "User")
                        .WithOne("AspnetProfile")
                        .HasForeignKey("backend.Data.Models.AspnetProfile", "UserId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Pr__UserI__77F737C0");

                    b.Navigation("User");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetRole", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetApplication", "Application")
                        .WithMany("AspnetRoles")
                        .HasForeignKey("ApplicationId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Ro__Appli__0180A1FA");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetUser", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetApplication", "Application")
                        .WithMany("AspnetUsers")
                        .HasForeignKey("ApplicationId")
                        .IsRequired()
                        .HasConstraintName("FK__aspnet_Us__Appli__52C5B311");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("backend.Data.Models.Benefit", b =>
                {
                    b.HasOne("backend.Data.Models.Chapter", "Chapter")
                        .WithMany("Benefits")
                        .HasForeignKey("ChapterId")
                        .HasConstraintName("FK_Benefits_Chapters");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Benefit")
                        .HasForeignKey("backend.Data.Models.Benefit", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Benefits_Root");

                    b.Navigation("Chapter");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.BenefitOverridesOrig", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("BenefitOverridesOrig")
                        .HasForeignKey("backend.Data.Models.BenefitOverridesOrig", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_BenefitOverrides_Root");

                    b.HasOne("backend.Data.Models.Party", "Party")
                        .WithMany("BenefitOverridesOrigs")
                        .HasForeignKey("PartyId")
                        .IsRequired()
                        .HasConstraintName("FK_BenefitOverrides_Parties");

                    b.Navigation("IdNavigation");

                    b.Navigation("Party");
                });

            modelBuilder.Entity("backend.Data.Models.Chapter", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "IdNavigation")
                        .WithOne("Chapter")
                        .HasForeignKey("backend.Data.Models.Chapter", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Carriers_Organizations");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.ChapterToEmployerRelationship", b =>
                {
                    b.HasOne("backend.Data.Models.Relationship", "Relationship")
                        .WithOne("ChapterToEmployerRelationship")
                        .HasForeignKey("backend.Data.Models.ChapterToEmployerRelationship", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ChapterToEmployerRelationships_Root");

                    b.Navigation("Relationship");
                });

            modelBuilder.Entity("backend.Data.Models.ClassificationName", b =>
                {
                    b.HasOne("backend.Data.Models.Chapter", "Chapter")
                        .WithMany("ClassificationNames")
                        .HasForeignKey("ChapterId")
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationAliases_Chapters");

                    b.HasOne("backend.Data.Models.DclassificationCode", "DclassificationCode")
                        .WithMany("ClassificationNames")
                        .HasForeignKey("DclassificationCodeId")
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationAliases_DJobClassifications");

                    b.HasOne("backend.Data.Models.Dstatus", "Dstatus")
                        .WithMany("ClassificationNames")
                        .HasForeignKey("DstatusId")
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationAliases_DStatuses");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ClassificationName")
                        .HasForeignKey("backend.Data.Models.ClassificationName", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationAliases_Root");

                    b.Navigation("Chapter");

                    b.Navigation("DclassificationCode");

                    b.Navigation("Dstatus");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.ContactMechanism", b =>
                {
                    b.HasOne("backend.Data.Models.DcontactMechanismType", "DcontactMechanismType")
                        .WithMany("ContactMechanisms")
                        .HasForeignKey("DcontactMechanismTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ContactMechanisms_DContactMechanismTypes");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ContactMechanism")
                        .HasForeignKey("backend.Data.Models.ContactMechanism", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ContactMechanisms_Root");

                    b.Navigation("DcontactMechanismType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.CostCode", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("CostCodes")
                        .HasForeignKey("AgreementId")
                        .HasConstraintName("FK__CostCodes__Agree__6A008029");

                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("CostCodes")
                        .HasForeignKey("BenefitId")
                        .HasConstraintName("FK__CostCodes__Benef__6AF4A462");

                    b.HasOne("backend.Data.Models.Employer", "Employer")
                        .WithMany("CostCodes")
                        .HasForeignKey("EmployerId")
                        .HasConstraintName("FK__CostCodes__Emplo__690C5BF0");

                    b.Navigation("Agreement");

                    b.Navigation("Benefit");

                    b.Navigation("Employer");
                });

            modelBuilder.Entity("backend.Data.Models.CreditCardPaymentMethod", b =>
                {
                    b.HasOne("backend.Data.Models.PaymentMethod", "Associated")
                        .WithOne("CreditCardPaymentMethod")
                        .HasForeignKey("backend.Data.Models.CreditCardPaymentMethod", "AssociatedGuid")
                        .IsRequired()
                        .HasConstraintName("FK_CreditCardPaymentMethods_PaymentMethods");

                    b.Navigation("Associated");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportPivot", b =>
                {
                    b.HasOne("backend.Data.Models.CustomReport", "Report")
                        .WithOne("CustomReportPivot")
                        .HasForeignKey("backend.Data.Models.CustomReportPivot", "ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_CustomReportPivots_CustomReports");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportProduct", b =>
                {
                    b.HasOne("backend.Data.Models.CustomReport", "CustomReport")
                        .WithMany("CustomReportProducts")
                        .HasForeignKey("CustomReportId")
                        .IsRequired()
                        .HasConstraintName("FK_CustomReportProduct_CustomReport");

                    b.HasOne("backend.Data.Models.Product1", "Product")
                        .WithMany("CustomReportProducts")
                        .HasForeignKey("ProductId")
                        .IsRequired()
                        .HasConstraintName("FK_CustomReportProduct_Product");

                    b.Navigation("CustomReport");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReportSubtotal", b =>
                {
                    b.HasOne("backend.Data.Models.CustomReport", "Report")
                        .WithMany()
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CustomReportSubtotals_CustomReports");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.Eftpayment", b =>
                {
                    b.HasOne("backend.Data.Models.ElectronicPayment", "IdNavigation")
                        .WithOne("Eftpayment")
                        .HasForeignKey("backend.Data.Models.Eftpayment", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EFTPayments_ElectronicPayments");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.EftpaymentMethod", b =>
                {
                    b.HasOne("backend.Data.Models.PaymentMethod", "Associated")
                        .WithOne("EftpaymentMethod")
                        .HasForeignKey("backend.Data.Models.EftpaymentMethod", "AssociatedGuid")
                        .IsRequired()
                        .HasConstraintName("FK_EFTPaymentMethods_PaymentMethods");

                    b.Navigation("Associated");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicBatch", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ElectronicBatch")
                        .HasForeignKey("backend.Data.Models.ElectronicBatch", "Id")
                        .IsRequired()
                        .HasConstraintName("FK_EFTBatches_Root");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicPayment", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("ElectronicPayments")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPayments_Benefits");

                    b.HasOne("backend.Data.Models.DpaymentMethodType", "DpaymentMethodType")
                        .WithMany("ElectronicPayments")
                        .HasForeignKey("DpaymentMethodTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPayments_DPaymentMethodTypes");

                    b.HasOne("backend.Data.Models.ElectronicBatch", "ElectronicBatch")
                        .WithMany("ElectronicPayments")
                        .HasForeignKey("ElectronicBatchId")
                        .HasConstraintName("FK_ElectronicPayments_ElectronicBatches");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ElectronicPayment")
                        .HasForeignKey("backend.Data.Models.ElectronicPayment", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPayments_Root");

                    b.HasOne("backend.Data.Models.Payment", "Payments")
                        .WithMany("ElectronicPayments")
                        .HasForeignKey("PaymentsId")
                        .HasConstraintName("FK_ElectronicPayments_Payments");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("ElectronicPayments")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPayments_Reports");

                    b.Navigation("Benefit");

                    b.Navigation("DpaymentMethodType");

                    b.Navigation("ElectronicBatch");

                    b.Navigation("IdNavigation");

                    b.Navigation("Payments");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicPaymentConfiguration", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("ElectronicPaymentConfigurations")
                        .HasForeignKey("BenefitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_Benefits");

                    b.HasOne("backend.Data.Models.Organization", "Chapter")
                        .WithMany("ElectronicPaymentConfigurationChapters")
                        .HasForeignKey("ChapterId")
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_ChapterOrganizations");

                    b.HasOne("backend.Data.Models.DelectronicPaymentOption", "DelectronicPaymentOption")
                        .WithMany("ElectronicPaymentConfigurations")
                        .HasForeignKey("DelectronicPaymentOptionId")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_DElectronicPaymentOptions");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ElectronicPaymentConfiguration")
                        .HasForeignKey("backend.Data.Models.ElectronicPaymentConfiguration", "Id")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_Root");

                    b.HasOne("backend.Data.Models.Nachaconfiguration", "Nachaconfiguration")
                        .WithMany("ElectronicPaymentConfigurations")
                        .HasForeignKey("NachaconfigurationId")
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_NACHAConfigurations");

                    b.HasOne("backend.Data.Models.Organization", "Organization")
                        .WithMany("ElectronicPaymentConfigurationOrganizations")
                        .HasForeignKey("OrganizationId")
                        .IsRequired()
                        .HasConstraintName("FK_ElectronicPaymentConfigurations_Organizations");

                    b.Navigation("Benefit");

                    b.Navigation("Chapter");

                    b.Navigation("DelectronicPaymentOption");

                    b.Navigation("IdNavigation");

                    b.Navigation("Nachaconfiguration");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("backend.Data.Models.EmailAddress", b =>
                {
                    b.HasOne("backend.Data.Models.DemailAddressType", "DemailAddressType")
                        .WithMany("EmailAddresses")
                        .HasForeignKey("DemailAddressTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_EmailAddresses_DEmailAddressTypes");

                    b.HasOne("backend.Data.Models.ContactMechanism", "IdNavigation")
                        .WithOne("EmailAddress")
                        .HasForeignKey("backend.Data.Models.EmailAddress", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EmailAddresses_ContactMechanisms");

                    b.Navigation("DemailAddressType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Employee", b =>
                {
                    b.HasOne("backend.Data.Models.Person", "IdNavigation")
                        .WithOne("Employee")
                        .HasForeignKey("backend.Data.Models.Employee", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Employees_Persons");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Employer", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "Organization")
                        .WithOne("Employer")
                        .HasForeignKey("backend.Data.Models.Employer", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Employers_Organizations");

                    b.HasOne("backend.Data.Models.Root", "Root")
                        .WithMany()
                        .HasForeignKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("Root");
                });

            modelBuilder.Entity("backend.Data.Models.EmployersToAgreement", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("EmployersToAgreements")
                        .HasForeignKey("AgreementId")
                        .IsRequired()
                        .HasConstraintName("FK_EmployersToAgreements_Agreements");

                    b.HasOne("backend.Data.Models.Employer", "Employer")
                        .WithMany("EmployersToAgreements")
                        .HasForeignKey("EmployerId")
                        .IsRequired()
                        .HasConstraintName("FK_EmployersToAgreements_Employers");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("EmployersToAgreement")
                        .HasForeignKey("backend.Data.Models.EmployersToAgreement", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EmployersToAgreements_Root");

                    b.Navigation("Agreement");

                    b.Navigation("Employer");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Epruser", b =>
                {
                    b.HasOne("backend.Data.Models.AspnetUser", "AspnetUser")
                        .WithOne("Epruser")
                        .HasForeignKey("backend.Data.Models.Epruser", "AspnetUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EPRUsers_aspnet_Users");

                    b.HasOne("backend.Data.Models.Image", "BannerImage")
                        .WithMany("Eprusers")
                        .HasForeignKey("BannerImageId")
                        .HasConstraintName("FK_EPRUsers_Images");

                    b.HasOne("backend.Data.Models.Organization", "Organization")
                        .WithMany("Eprusers")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EPRUsers_Organizations");

                    b.Navigation("AspnetUser");

                    b.Navigation("BannerImage");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("backend.Data.Models.FundAdministrator", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "IdNavigation")
                        .WithOne("FundAdministrator")
                        .HasForeignKey("backend.Data.Models.FundAdministrator", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_FundAdministrators_Organizations");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.FundingComment", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("FundingComments")
                        .HasForeignKey("AgreementId")
                        .IsRequired()
                        .HasConstraintName("FK_FundingComments_Agreements");

                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("FundingComments")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_FundingComments_Benefits");

                    b.HasOne("backend.Data.Models.Employer", "Employer")
                        .WithMany("FundingComments")
                        .HasForeignKey("EmployerId")
                        .IsRequired()
                        .HasConstraintName("FK_FundingComments_Employers");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("FundingComments")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_Core.FundingComments_Core.Reports");

                    b.Navigation("Agreement");

                    b.Navigation("Benefit");

                    b.Navigation("Employer");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.FundingCommentDetail", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "Organization")
                        .WithMany("FundingCommentDetails")
                        .HasForeignKey("OrganizationId")
                        .IsRequired()
                        .HasConstraintName("FK_FundingCommentDetails_Organizations");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("backend.Data.Models.GuidSubstitution", b =>
                {
                    b.HasOne("backend.Data.Models.DguidSubstitutionType", "SubstitutionType")
                        .WithMany("GuidSubstitutions")
                        .HasForeignKey("SubstitutionTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_GuidSubstitution_DGuidSubstitutionType");

                    b.Navigation("SubstitutionType");
                });

            modelBuilder.Entity("backend.Data.Models.Image", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Image")
                        .HasForeignKey("backend.Data.Models.Image", "Id")
                        .IsRequired()
                        .HasConstraintName("FK_Images");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Kbaanswer", b =>
                {
                    b.HasOne("backend.Data.Models.Kbaquestion", "Question")
                        .WithMany("Kbaanswers")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("backend.Data.Models.AspnetUser", "User")
                        .WithMany("Kbaanswers")
                        .HasForeignKey("UserGuid")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Question");

                    b.Navigation("User");
                });

            modelBuilder.Entity("backend.Data.Models.LoggedEvent", b =>
                {
                    b.HasOne("backend.Data.Models.DeventSubType", "DeventSubType")
                        .WithMany()
                        .HasForeignKey("DeventSubTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_LoggedEvents_DEventSubTypes");

                    b.HasOne("backend.Data.Models.DeventType", "DeventType")
                        .WithMany()
                        .HasForeignKey("DeventTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_LoggedEvents_DEventTypes");

                    b.Navigation("DeventSubType");

                    b.Navigation("DeventType");
                });

            modelBuilder.Entity("backend.Data.Models.NewsItem", b =>
                {
                    b.HasOne("backend.Data.Models.RoleGroup", "RoleGroupNavigation")
                        .WithMany("NewsItems")
                        .HasForeignKey("RoleGroup")
                        .IsRequired()
                        .HasConstraintName("FK_NewsItems_ToRoleGroups");

                    b.Navigation("RoleGroupNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Note", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithMany("NoteIdNavigations")
                        .HasForeignKey("Id")
                        .IsRequired()
                        .HasConstraintName("FK_Notes_Root");

                    b.HasOne("backend.Data.Models.Root", "Root")
                        .WithMany("NoteRoots")
                        .HasForeignKey("RootId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Notes_RootOwner");

                    b.Navigation("IdNavigation");

                    b.Navigation("Root");
                });

            modelBuilder.Entity("backend.Data.Models.Order", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "Organization")
                        .WithMany("Orders")
                        .HasForeignKey("OrganizationId")
                        .IsRequired()
                        .HasConstraintName("FK_Orders_Organizations");

                    b.HasOne("backend.Data.Models.DorderStatus", "Status")
                        .WithMany("Orders")
                        .HasForeignKey("StatusId")
                        .IsRequired()
                        .HasConstraintName("FK_Orders_DOrderStatus");

                    b.Navigation("Organization");

                    b.Navigation("Status");
                });

            modelBuilder.Entity("backend.Data.Models.OrderDetail", b =>
                {
                    b.HasOne("backend.Data.Models.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .IsRequired()
                        .HasConstraintName("FK_OrderDetails_Order");

                    b.HasOne("backend.Data.Models.DpaymentType", "PaymentType")
                        .WithMany("OrderDetails")
                        .HasForeignKey("PaymentTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_OrderDetails_PaymentType");

                    b.HasOne("backend.Data.Models.Product1", "Product")
                        .WithMany("OrderDetails")
                        .HasForeignKey("ProductId")
                        .IsRequired()
                        .HasConstraintName("FK_OrderDetails_Products");

                    b.Navigation("Order");

                    b.Navigation("PaymentType");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("backend.Data.Models.Organization", b =>
                {
                    b.HasOne("backend.Data.Models.DorganizationType", "DorganizationType")
                        .WithMany("Organizations")
                        .HasForeignKey("DorganizationTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Organizations_DOrganizationTypes");

                    b.HasOne("backend.Data.Models.Party", "IdNavigation")
                        .WithOne("Organization")
                        .HasForeignKey("backend.Data.Models.Organization", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Organizations_Parties");

                    b.Navigation("DorganizationType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.PartiesToContactMechanism", b =>
                {
                    b.HasOne("backend.Data.Models.ContactMechanism", "ContactMechanism")
                        .WithMany("PartiesToContactMechanisms")
                        .HasForeignKey("ContactMechanismId")
                        .IsRequired()
                        .HasConstraintName("FK_PartiesToContactMechanisms_ContactMechanisms");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("PartiesToContactMechanism")
                        .HasForeignKey("backend.Data.Models.PartiesToContactMechanism", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PartiesToContactMechanisms_Root");

                    b.HasOne("backend.Data.Models.Party", "Party")
                        .WithMany("PartiesToContactMechanisms")
                        .HasForeignKey("PartyId")
                        .IsRequired()
                        .HasConstraintName("FK_PartiesToContactMechanisms_Parties");

                    b.Navigation("ContactMechanism");

                    b.Navigation("IdNavigation");

                    b.Navigation("Party");
                });

            modelBuilder.Entity("backend.Data.Models.Party", b =>
                {
                    b.HasOne("backend.Data.Models.DpartyType", "DpartyType")
                        .WithMany("Parties")
                        .HasForeignKey("DpartyTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Parties_DPartyTypes");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Party")
                        .HasForeignKey("backend.Data.Models.Party", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Parties_Root");

                    b.Navigation("DpartyType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.PayStub", b =>
                {
                    b.HasOne("backend.Data.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("backend.Data.Models.TimeSheet", "TimeSheet")
                        .WithMany("PayStubs")
                        .HasForeignKey("TimeSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("TimeSheet");
                });

            modelBuilder.Entity("backend.Data.Models.PayStubDetail", b =>
                {
                    b.HasOne("backend.Data.Models.PayStub", "PayStub")
                        .WithMany("Details")
                        .HasForeignKey("PayStubId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("backend.Data.Models.ReportLineItem", "ReportLineItem")
                        .WithMany("PayStubDetails")
                        .HasForeignKey("ReportLineItemId");

                    b.Navigation("PayStub");

                    b.Navigation("ReportLineItem");
                });

            modelBuilder.Entity("backend.Data.Models.Payment", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Payment")
                        .HasForeignKey("backend.Data.Models.Payment", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Payments_Root");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("Payments")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_Payments_Reports");

                    b.Navigation("IdNavigation");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.PaymentDetail", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("PaymentDetails")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_PaymentDetails_Benefits");

                    b.HasOne("backend.Data.Models.Payment", "Payment")
                        .WithMany("PaymentDetails")
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentDetails_Payments");

                    b.Navigation("Benefit");

                    b.Navigation("Payment");
                });

            modelBuilder.Entity("backend.Data.Models.PaymentMethod", b =>
                {
                    b.HasOne("backend.Data.Models.DpaymentMethodType", "DpaymentMethodType")
                        .WithMany("PaymentMethods")
                        .HasForeignKey("DpaymentMethodTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_PaymentMethods_DPaymentMethodType");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithMany("PaymentMethods")
                        .HasForeignKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentMethods_Root");

                    b.Navigation("DpaymentMethodType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Person", b =>
                {
                    b.HasOne("backend.Data.Models.Dgender", "Dgender")
                        .WithMany("People")
                        .HasForeignKey("DgenderId")
                        .IsRequired()
                        .HasConstraintName("FK_Persons_DGenders");

                    b.HasOne("backend.Data.Models.DpersonType", "DpersonType")
                        .WithMany("People")
                        .HasForeignKey("DpersonTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Persons_DPersonTypes");

                    b.HasOne("backend.Data.Models.Party", "IdNavigation")
                        .WithOne("Person")
                        .HasForeignKey("backend.Data.Models.Person", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Persons_Parties");

                    b.Navigation("Dgender");

                    b.Navigation("DpersonType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.PhoneNumber", b =>
                {
                    b.HasOne("backend.Data.Models.DphoneNumberType", "DphoneNumberType")
                        .WithMany("PhoneNumbers")
                        .HasForeignKey("DphoneNumberTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_PhoneNumbers_DPhoneNumberTypes");

                    b.HasOne("backend.Data.Models.ContactMechanism", "IdNavigation")
                        .WithOne("PhoneNumber")
                        .HasForeignKey("backend.Data.Models.PhoneNumber", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PhoneNumbers_ContactMechanisms");

                    b.Navigation("DphoneNumberType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Product1", b =>
                {
                    b.HasOne("backend.Data.Models.DpaymentType", "PaymentType")
                        .WithMany("Product1s")
                        .HasForeignKey("PaymentTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Products_PaymentType");

                    b.Navigation("PaymentType");
                });

            modelBuilder.Entity("backend.Data.Models.ProductCategory", b =>
                {
                    b.HasOne("backend.Data.Models.Dcategory", "Dcategory")
                        .WithMany("ProductCategories")
                        .HasForeignKey("DcategoryId")
                        .IsRequired()
                        .HasConstraintName("FK_ProductCategory_DCategory");

                    b.HasOne("backend.Data.Models.Product1", "Product")
                        .WithMany("ProductCategories")
                        .HasForeignKey("ProductId")
                        .IsRequired()
                        .HasConstraintName("FK_ProductCategory_Product");

                    b.Navigation("Dcategory");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("backend.Data.Models.ProductsToOrganizationType", b =>
                {
                    b.HasOne("backend.Data.Models.DorganizationType", "OrganizationType")
                        .WithMany("ProductsToOrganizationTypes")
                        .HasForeignKey("OrganizationTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ProductsToOrganizationTypes_OrganizationType");

                    b.HasOne("backend.Data.Models.Product1", "Product")
                        .WithMany("ProductsToOrganizationTypes")
                        .HasForeignKey("ProductId")
                        .IsRequired()
                        .HasConstraintName("FK_ProductsToOrganizationTypes_Product");

                    b.Navigation("OrganizationType");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("backend.Data.Models.Rate", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany()
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_Rates_Benefits");

                    b.HasOne("backend.Data.Models.ClassificationName", "ClassificationName")
                        .WithMany()
                        .HasForeignKey("ClassificationNameId")
                        .IsRequired()
                        .HasConstraintName("FK_Rates_ClassificationNames");

                    b.HasOne("backend.Data.Models.DcalculationMethod", "DcalculationMethod")
                        .WithMany()
                        .HasForeignKey("DcalculationMethodId")
                        .IsRequired()
                        .HasConstraintName("FK_Rates_DCalculationMethods");

                    b.HasOne("backend.Data.Models.DcalculationModifier", "DcalculationModifier")
                        .WithMany()
                        .HasForeignKey("DcalculationModifierId")
                        .IsRequired()
                        .HasConstraintName("FK_Rates_DCalculationModifiers");

                    b.HasOne("backend.Data.Models.RateSchedule", "RateSchedule")
                        .WithMany()
                        .HasForeignKey("RateScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Rates_RateSchedules");

                    b.HasOne("backend.Data.Models.SubClassification", "SubClassification")
                        .WithMany()
                        .HasForeignKey("SubClassificationId")
                        .HasConstraintName("FK_Rates_SubClassifications");

                    b.Navigation("Benefit");

                    b.Navigation("ClassificationName");

                    b.Navigation("DcalculationMethod");

                    b.Navigation("DcalculationModifier");

                    b.Navigation("RateSchedule");

                    b.Navigation("SubClassification");
                });

            modelBuilder.Entity("backend.Data.Models.RateSchedule", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("RateSchedules")
                        .HasForeignKey("AgreementId")
                        .IsRequired()
                        .HasConstraintName("FK_RateSchedules_Agreements");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("RateSchedule")
                        .HasForeignKey("backend.Data.Models.RateSchedule", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RateSchedules_Root");

                    b.Navigation("Agreement");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Relationship", b =>
                {
                    b.HasOne("backend.Data.Models.DrelationshipType", "DrelationshipType")
                        .WithMany("Relationships")
                        .HasForeignKey("DrelationshipTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Relationships_DRelationshipTypes");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Relationship")
                        .HasForeignKey("backend.Data.Models.Relationship", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Relationships_Root");

                    b.HasOne("backend.Data.Models.Party", "LeftParty")
                        .WithMany("RelationshipLeftParties")
                        .HasForeignKey("LeftPartyId")
                        .IsRequired()
                        .HasConstraintName("FK_Relationships_LeftParties");

                    b.HasOne("backend.Data.Models.Party", "RightParty")
                        .WithMany("RelationshipRightParties")
                        .HasForeignKey("RightPartyId")
                        .HasConstraintName("FK_Relationships_RightParties");

                    b.HasOne("backend.Data.Models.DrelationshipSubType", "Drelationship")
                        .WithMany("Relationships")
                        .HasForeignKey("DrelationshipSubTypeId", "DrelationshipTypeId")
                        .HasConstraintName("FK_Relationships_DRelationshipSubTypes");

                    b.Navigation("Drelationship");

                    b.Navigation("DrelationshipType");

                    b.Navigation("IdNavigation");

                    b.Navigation("LeftParty");

                    b.Navigation("RightParty");
                });

            modelBuilder.Entity("backend.Data.Models.RelationshipStatus", b =>
                {
                    b.HasOne("backend.Data.Models.DrelationshipStatus", "DrelationshipStatus")
                        .WithMany("RelationshipStatuses")
                        .HasForeignKey("DrelationshipStatusId")
                        .IsRequired()
                        .HasConstraintName("FK_RelationshipStatuses_DRelationshipStatuses");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("RelationshipStatus")
                        .HasForeignKey("backend.Data.Models.RelationshipStatus", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RelationshipStatuses_Root");

                    b.HasOne("backend.Data.Models.Relationship", "Relationship")
                        .WithMany("RelationshipStatuses")
                        .HasForeignKey("RelationshipId")
                        .IsRequired()
                        .HasConstraintName("FK_RelationshipStatuses_Relationships");

                    b.Navigation("DrelationshipStatus");

                    b.Navigation("IdNavigation");

                    b.Navigation("Relationship");
                });

            modelBuilder.Entity("backend.Data.Models.Report", b =>
                {
                    b.HasOne("backend.Data.Models.Agreement", "Agreement")
                        .WithMany("Reports")
                        .HasForeignKey("AgreementId")
                        .IsRequired()
                        .HasConstraintName("FK_Reports_Agreements");

                    b.HasOne("backend.Data.Models.Report", "AmendedReport")
                        .WithMany("InverseAmendedReport")
                        .HasForeignKey("AmendedReportId")
                        .HasConstraintName("FK_Reports_Reports");

                    b.HasOne("backend.Data.Models.DreportStatus", "DreportStatus")
                        .WithMany("Reports")
                        .HasForeignKey("DreportStatusId")
                        .IsRequired()
                        .HasConstraintName("FK_Reports_DReportStatuses");

                    b.HasOne("backend.Data.Models.Employer", "Employer")
                        .WithMany("Reports")
                        .HasForeignKey("EmployerId")
                        .IsRequired()
                        .HasConstraintName("FK_Reports_Employers");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("Report")
                        .HasForeignKey("backend.Data.Models.Report", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Reports_Root");

                    b.HasOne("backend.Data.Models.RateSchedule", "RateSchedule")
                        .WithMany("Reports")
                        .HasForeignKey("RateScheduleId")
                        .HasConstraintName("FK_Reports_RateSchedules");

                    b.Navigation("Agreement");

                    b.Navigation("AmendedReport");

                    b.Navigation("DreportStatus");

                    b.Navigation("Employer");

                    b.Navigation("IdNavigation");

                    b.Navigation("RateSchedule");
                });

            modelBuilder.Entity("backend.Data.Models.ReportLineItem", b =>
                {
                    b.HasOne("backend.Data.Models.ClassificationName", "ClassificationName")
                        .WithMany("ReportLineItems")
                        .HasForeignKey("ClassificationNameId")
                        .HasConstraintName("FK_ReportLineItems_ClassificationNames");

                    b.HasOne("backend.Data.Models.DamendmentAction", "DamendmentAction")
                        .WithMany("ReportLineItems")
                        .HasForeignKey("DamendmentActionId")
                        .HasConstraintName("FK_ReportLineItems_DAmendmentActions");

                    b.HasOne("backend.Data.Models.Employee", "Employee")
                        .WithMany("ReportLineItems")
                        .HasForeignKey("EmployeeId")
                        .HasConstraintName("FK_ReportLineItems_Employees");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ReportLineItem")
                        .HasForeignKey("backend.Data.Models.ReportLineItem", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ReportLineItems_Root");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("ReportLineItems")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportLineItems_Reports");

                    b.HasOne("backend.Data.Models.SubClassification", "SubClassification")
                        .WithMany("ReportLineItems")
                        .HasForeignKey("SubClassificationId")
                        .HasConstraintName("FK_ReportLineItems_SubClassifications");

                    b.Navigation("ClassificationName");

                    b.Navigation("DamendmentAction");

                    b.Navigation("Employee");

                    b.Navigation("IdNavigation");

                    b.Navigation("Report");

                    b.Navigation("SubClassification");
                });

            modelBuilder.Entity("backend.Data.Models.ReportLineItemDetail", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("ReportLineItemDetails")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportLineItemDetails_Benefits");

                    b.HasOne("backend.Data.Models.ReportLineItem", "ReportLineItem")
                        .WithMany("ReportLineItemDetails")
                        .HasForeignKey("ReportLineItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ReportLineItemDetails_ReportLineItems");

                    b.Navigation("Benefit");

                    b.Navigation("ReportLineItem");
                });

            modelBuilder.Entity("backend.Data.Models.ReportSubscription", b =>
                {
                    b.HasOne("backend.Data.Models.DreportSubscriptionType", "ReportSubscriptionType")
                        .WithMany("ReportSubscriptions")
                        .HasForeignKey("ReportSubscriptionTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportSubscriptions_ReportSubscriptionType");

                    b.Navigation("ReportSubscriptionType");
                });

            modelBuilder.Entity("backend.Data.Models.ReportSuppression", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ReportSuppression")
                        .HasForeignKey("backend.Data.Models.ReportSuppression", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ReportSuppressions_Root");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("ReportSuppressions")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportSuppressions_Reports");

                    b.Navigation("IdNavigation");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.ReportedBenefitReleaseAuthorization", b =>
                {
                    b.HasOne("backend.Data.Models.Benefit", "Benefit")
                        .WithMany("ReportedBenefitReleaseAuthorizations")
                        .HasForeignKey("BenefitId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportedBenefitReleaseAuthentications_Benefits");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("ReportedBenefitReleaseAuthorization")
                        .HasForeignKey("backend.Data.Models.ReportedBenefitReleaseAuthorization", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ReportReleaseAuthentications_Root");

                    b.HasOne("backend.Data.Models.Report", "Report")
                        .WithMany("ReportedBenefitReleaseAuthorizations")
                        .HasForeignKey("ReportId")
                        .IsRequired()
                        .HasConstraintName("FK_ReportedBenefitReleaseAuthorizations_Reports");

                    b.Navigation("Benefit");

                    b.Navigation("IdNavigation");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("backend.Data.Models.ServiceSubscription", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "Party")
                        .WithMany("ServiceSubscriptions")
                        .HasForeignKey("PartyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK__ServiceSu__Party__7C1F3064");

                    b.HasOne("backend.Data.Models.SubscriptionService", "SubscriptionService")
                        .WithMany("ServiceSubscriptions")
                        .HasForeignKey("SubscriptionServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK__ServiceSu__Subsc__7D13549D");

                    b.Navigation("Party");

                    b.Navigation("SubscriptionService");
                });

            modelBuilder.Entity("backend.Data.Models.Setting", b =>
                {
                    b.HasOne("backend.Data.Models.DsettingsType", "SettingsType")
                        .WithMany("Settings")
                        .HasForeignKey("DSettingsTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Settings_DSettingsTypes");

                    b.Navigation("SettingsType");
                });

            modelBuilder.Entity("backend.Data.Models.SubClassification", b =>
                {
                    b.HasOne("backend.Data.Models.Chapter", "Chapter")
                        .WithMany("SubClassifications")
                        .HasForeignKey("ChapterId")
                        .HasConstraintName("FK_JobClassificationSubTypes_Chapters");

                    b.HasOne("backend.Data.Models.Dstatus", "Dstatus")
                        .WithMany("SubClassifications")
                        .HasForeignKey("DstatusId")
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationSubTypes_DStatuses");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("SubClassification")
                        .HasForeignKey("backend.Data.Models.SubClassification", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_JobClassificationSubTypes_Root");

                    b.Navigation("Chapter");

                    b.Navigation("Dstatus");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.ThirdPartyEmployerId", b =>
                {
                    b.HasOne("backend.Data.Models.Employer", "Employer")
                        .WithMany()
                        .HasForeignKey("EmployerId")
                        .IsRequired()
                        .HasConstraintName("FK_ThirdPartyEmployerIDs_Employers");

                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithMany()
                        .HasForeignKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ThirdPartyEmployerIDs_Root");

                    b.HasOne("backend.Data.Models.Organization", "ThirdParty")
                        .WithMany()
                        .HasForeignKey("ThirdPartyId")
                        .IsRequired()
                        .HasConstraintName("FK_ThirdPartyEmployerIDs_ThirdPartyID");

                    b.Navigation("Employer");

                    b.Navigation("IdNavigation");

                    b.Navigation("ThirdParty");
                });

            modelBuilder.Entity("backend.Data.Models.TimelinesOrig", b =>
                {
                    b.HasOne("backend.Data.Models.Root", "IdNavigation")
                        .WithOne("TimelinesOrig")
                        .HasForeignKey("backend.Data.Models.TimelinesOrig", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Timelines_Root");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Trade", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "IdNavigation")
                        .WithOne("Trade")
                        .HasForeignKey("backend.Data.Models.Trade", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Affinities_Organizations");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Union", b =>
                {
                    b.HasOne("backend.Data.Models.Organization", "IdNavigation")
                        .WithOne("Union")
                        .HasForeignKey("backend.Data.Models.Union", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Brokerages_Organizations");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Website", b =>
                {
                    b.HasOne("backend.Data.Models.DwebsiteType", "DwebsiteType")
                        .WithMany("Websites")
                        .HasForeignKey("DwebsiteTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_Websites_DWebsiteTypes");

                    b.HasOne("backend.Data.Models.ContactMechanism", "IdNavigation")
                        .WithOne("Website")
                        .HasForeignKey("backend.Data.Models.Website", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Websites_ContactMechanisms");

                    b.Navigation("DwebsiteType");

                    b.Navigation("IdNavigation");
                });

            modelBuilder.Entity("backend.Data.Models.Agreement", b =>
                {
                    b.Navigation("AgreementsToBenefits");

                    b.Navigation("CostCodes");

                    b.Navigation("EmployersToAgreements");

                    b.Navigation("FundingComments");

                    b.Navigation("RateSchedules");

                    b.Navigation("Reports");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetApplication", b =>
                {
                    b.Navigation("AspnetMemberships");

                    b.Navigation("AspnetPaths");

                    b.Navigation("AspnetRoles");

                    b.Navigation("AspnetUsers");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetPath", b =>
                {
                    b.Navigation("AspnetPersonalizationAllUser");

                    b.Navigation("AspnetPersonalizationPerUsers");
                });

            modelBuilder.Entity("backend.Data.Models.AspnetUser", b =>
                {
                    b.Navigation("AspnetMembership");

                    b.Navigation("AspnetPersonalizationPerUsers");

                    b.Navigation("AspnetProfile");

                    b.Navigation("Epruser");

                    b.Navigation("Kbaanswers");
                });

            modelBuilder.Entity("backend.Data.Models.Benefit", b =>
                {
                    b.Navigation("AgreementsToBenefits");

                    b.Navigation("CostCodes");

                    b.Navigation("ElectronicPaymentConfigurations");

                    b.Navigation("ElectronicPayments");

                    b.Navigation("FundingComments");

                    b.Navigation("PaymentDetails");

                    b.Navigation("ReportLineItemDetails");

                    b.Navigation("ReportedBenefitReleaseAuthorizations");
                });

            modelBuilder.Entity("backend.Data.Models.Chapter", b =>
                {
                    b.Navigation("Agreements");

                    b.Navigation("Benefits");

                    b.Navigation("ClassificationNames");

                    b.Navigation("SubClassifications");
                });

            modelBuilder.Entity("backend.Data.Models.ClassificationName", b =>
                {
                    b.Navigation("ReportLineItems");
                });

            modelBuilder.Entity("backend.Data.Models.ContactMechanism", b =>
                {
                    b.Navigation("Address");

                    b.Navigation("EmailAddress");

                    b.Navigation("PartiesToContactMechanisms");

                    b.Navigation("PhoneNumber");

                    b.Navigation("Website");
                });

            modelBuilder.Entity("backend.Data.Models.CustomReport", b =>
                {
                    b.Navigation("CustomReportPivot");

                    b.Navigation("CustomReportProducts");
                });

            modelBuilder.Entity("backend.Data.Models.DaddressType", b =>
                {
                    b.Navigation("Addresses");
                });

            modelBuilder.Entity("backend.Data.Models.DagreementType", b =>
                {
                    b.Navigation("Agreements");
                });

            modelBuilder.Entity("backend.Data.Models.DamendmentAction", b =>
                {
                    b.Navigation("ReportLineItems");
                });

            modelBuilder.Entity("backend.Data.Models.Dcategory", b =>
                {
                    b.Navigation("ProductCategories");
                });

            modelBuilder.Entity("backend.Data.Models.DclassificationCode", b =>
                {
                    b.Navigation("ClassificationNames");
                });

            modelBuilder.Entity("backend.Data.Models.DcontactMechanismType", b =>
                {
                    b.Navigation("ContactMechanisms");
                });

            modelBuilder.Entity("backend.Data.Models.Dcountry", b =>
                {
                    b.Navigation("Addresses");
                });

            modelBuilder.Entity("backend.Data.Models.DelectronicPaymentOption", b =>
                {
                    b.Navigation("ElectronicPaymentConfigurations");
                });

            modelBuilder.Entity("backend.Data.Models.DemailAddressType", b =>
                {
                    b.Navigation("EmailAddresses");
                });

            modelBuilder.Entity("backend.Data.Models.Dgender", b =>
                {
                    b.Navigation("People");
                });

            modelBuilder.Entity("backend.Data.Models.DguidSubstitutionType", b =>
                {
                    b.Navigation("GuidSubstitutions");
                });

            modelBuilder.Entity("backend.Data.Models.DorderStatus", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("backend.Data.Models.DorganizationType", b =>
                {
                    b.Navigation("Organizations");

                    b.Navigation("ProductsToOrganizationTypes");
                });

            modelBuilder.Entity("backend.Data.Models.DpartyType", b =>
                {
                    b.Navigation("Parties");
                });

            modelBuilder.Entity("backend.Data.Models.DpaymentMethodType", b =>
                {
                    b.Navigation("ElectronicPayments");

                    b.Navigation("PaymentMethods");
                });

            modelBuilder.Entity("backend.Data.Models.DpaymentType", b =>
                {
                    b.Navigation("OrderDetails");

                    b.Navigation("Product1s");
                });

            modelBuilder.Entity("backend.Data.Models.DpersonType", b =>
                {
                    b.Navigation("People");
                });

            modelBuilder.Entity("backend.Data.Models.DphoneNumberType", b =>
                {
                    b.Navigation("PhoneNumbers");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipStatus", b =>
                {
                    b.Navigation("RelationshipStatuses");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipSubType", b =>
                {
                    b.Navigation("Relationships");
                });

            modelBuilder.Entity("backend.Data.Models.DrelationshipType", b =>
                {
                    b.Navigation("Relationships");
                });

            modelBuilder.Entity("backend.Data.Models.DreportStatus", b =>
                {
                    b.Navigation("Reports");
                });

            modelBuilder.Entity("backend.Data.Models.DreportSubscriptionType", b =>
                {
                    b.Navigation("ReportSubscriptions");
                });

            modelBuilder.Entity("backend.Data.Models.DsettingsType", b =>
                {
                    b.Navigation("Settings");
                });

            modelBuilder.Entity("backend.Data.Models.Dstatus", b =>
                {
                    b.Navigation("ClassificationNames");

                    b.Navigation("SubClassifications");
                });

            modelBuilder.Entity("backend.Data.Models.DwebsiteType", b =>
                {
                    b.Navigation("Websites");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicBatch", b =>
                {
                    b.Navigation("ElectronicPayments");
                });

            modelBuilder.Entity("backend.Data.Models.ElectronicPayment", b =>
                {
                    b.Navigation("Eftpayment");
                });

            modelBuilder.Entity("backend.Data.Models.Employee", b =>
                {
                    b.Navigation("Agreements");

                    b.Navigation("ReportLineItems");
                });

            modelBuilder.Entity("backend.Data.Models.Employer", b =>
                {
                    b.Navigation("CostCodes");

                    b.Navigation("EmployersToAgreements");

                    b.Navigation("FundingComments");

                    b.Navigation("Reports");
                });

            modelBuilder.Entity("backend.Data.Models.Image", b =>
                {
                    b.Navigation("Eprusers");
                });

            modelBuilder.Entity("backend.Data.Models.Kbaquestion", b =>
                {
                    b.Navigation("Kbaanswers");
                });

            modelBuilder.Entity("backend.Data.Models.Nachaconfiguration", b =>
                {
                    b.Navigation("ElectronicPaymentConfigurations");
                });

            modelBuilder.Entity("backend.Data.Models.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("backend.Data.Models.Organization", b =>
                {
                    b.Navigation("AgreementsToBenefitCollectingAgents");

                    b.Navigation("AgreementsToBenefitFundAdministrators");

                    b.Navigation("Chapter");

                    b.Navigation("ElectronicPaymentConfigurationChapters");

                    b.Navigation("ElectronicPaymentConfigurationOrganizations");

                    b.Navigation("Employer");

                    b.Navigation("Eprusers");

                    b.Navigation("FundAdministrator");

                    b.Navigation("FundingCommentDetails");

                    b.Navigation("Orders");

                    b.Navigation("ServiceSubscriptions");

                    b.Navigation("Trade");

                    b.Navigation("Union");
                });

            modelBuilder.Entity("backend.Data.Models.Party", b =>
                {
                    b.Navigation("BenefitOverridesOrigs");

                    b.Navigation("Organization");

                    b.Navigation("PartiesToContactMechanisms");

                    b.Navigation("Person");

                    b.Navigation("RelationshipLeftParties");

                    b.Navigation("RelationshipRightParties");
                });

            modelBuilder.Entity("backend.Data.Models.PayStub", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("backend.Data.Models.Payment", b =>
                {
                    b.Navigation("ElectronicPayments");

                    b.Navigation("PaymentDetails");
                });

            modelBuilder.Entity("backend.Data.Models.PaymentMethod", b =>
                {
                    b.Navigation("CreditCardPaymentMethod");

                    b.Navigation("EftpaymentMethod");
                });

            modelBuilder.Entity("backend.Data.Models.Person", b =>
                {
                    b.Navigation("Employee");
                });

            modelBuilder.Entity("backend.Data.Models.Product1", b =>
                {
                    b.Navigation("CustomReportProducts");

                    b.Navigation("OrderDetails");

                    b.Navigation("ProductCategories");

                    b.Navigation("ProductsToOrganizationTypes");
                });

            modelBuilder.Entity("backend.Data.Models.RateSchedule", b =>
                {
                    b.Navigation("Reports");
                });

            modelBuilder.Entity("backend.Data.Models.Relationship", b =>
                {
                    b.Navigation("ChapterToEmployerRelationship");

                    b.Navigation("RelationshipStatuses");
                });

            modelBuilder.Entity("backend.Data.Models.Report", b =>
                {
                    b.Navigation("ElectronicPayments");

                    b.Navigation("FundingComments");

                    b.Navigation("InverseAmendedReport");

                    b.Navigation("Payments");

                    b.Navigation("ReportLineItems");

                    b.Navigation("ReportSuppressions");

                    b.Navigation("ReportedBenefitReleaseAuthorizations");
                });

            modelBuilder.Entity("backend.Data.Models.ReportLineItem", b =>
                {
                    b.Navigation("PayStubDetails");

                    b.Navigation("ReportLineItemDetails");
                });

            modelBuilder.Entity("backend.Data.Models.RoleGroup", b =>
                {
                    b.Navigation("NewsItems");
                });

            modelBuilder.Entity("backend.Data.Models.Root", b =>
                {
                    b.Navigation("Agreement");

                    b.Navigation("Benefit");

                    b.Navigation("BenefitOverridesOrig");

                    b.Navigation("ClassificationName");

                    b.Navigation("ContactMechanism");

                    b.Navigation("ElectronicBatch");

                    b.Navigation("ElectronicPayment");

                    b.Navigation("ElectronicPaymentConfiguration");

                    b.Navigation("EmployersToAgreement");

                    b.Navigation("Image");

                    b.Navigation("NoteIdNavigations");

                    b.Navigation("NoteRoots");

                    b.Navigation("PartiesToContactMechanism");

                    b.Navigation("Party");

                    b.Navigation("Payment");

                    b.Navigation("PaymentMethods");

                    b.Navigation("RateSchedule");

                    b.Navigation("Relationship");

                    b.Navigation("RelationshipStatus");

                    b.Navigation("Report");

                    b.Navigation("ReportLineItem");

                    b.Navigation("ReportSuppression");

                    b.Navigation("ReportedBenefitReleaseAuthorization");

                    b.Navigation("SubClassification");

                    b.Navigation("TimelinesOrig");
                });

            modelBuilder.Entity("backend.Data.Models.SubClassification", b =>
                {
                    b.Navigation("ReportLineItems");
                });

            modelBuilder.Entity("backend.Data.Models.SubscriptionService", b =>
                {
                    b.Navigation("ServiceSubscriptions");
                });

            modelBuilder.Entity("backend.Data.Models.TimeSheet", b =>
                {
                    b.Navigation("PayStubs");
                });

            modelBuilder.Entity("backend.Data.Models.Union", b =>
                {
                    b.Navigation("Agreements");
                });
#pragma warning restore 612, 618
        }
    }
}
