﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class ConvertPayStubGuidsToInts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var sql =
                @"
-- ========================================================================
-- Pre-Migration Validation and Setup
-- ========================================================================

DECLARE @PayStubCount INT, @PayStubDetailCount INT, @ErrorMsg NVARCHAR(4000)

-- Validate that PayStubs table exists and has data to migrate
SELECT @PayStubCount = COUNT(*) FROM PayStubs WHERE Id IS NOT NULL
SELECT @PayStubDetailCount = COUNT(*) FROM PayStubDetails WHERE Id IS NOT NULL

-- Check if migration has already been completed (presence of OldId column indicates it's already migrated)
IF COL_LENGTH('PayStubs', 'OldId') IS NOT NULL
BEGIN
    PRINT 'Migration already completed: PayStubs table already has INT primary keys'
    RETURN
END

-- Validate data integrity before proceeding
IF @PayStubCount = 0
BEGIN
    PRINT 'Warning: No PayStubs found to migrate. Proceeding with schema changes only.'
END

IF EXISTS (SELECT 1 FROM PayStubDetails psd LEFT JOIN PayStubs ps ON psd.PayStubId = ps.Id WHERE ps.Id IS NULL)
BEGIN
    SET @ErrorMsg = 'Data integrity error: PayStubDetails exist with invalid PayStub references'
    RAISERROR(@ErrorMsg, 16, 1)
    RETURN
END

PRINT 'Starting PayStub GUID to INT migration...'
PRINT 'PayStubs to migrate: ' + CAST(@PayStubCount AS NVARCHAR(10))
PRINT 'PayStubDetails to migrate: ' + CAST(@PayStubDetailCount AS NVARCHAR(10))

-- ========================================================================
-- Begin Migration Transaction
-- ========================================================================

BEGIN TRANSACTION PayStubGuidToIntMigration

BEGIN TRY

    -- ========================================================================
    -- Phase 0: Drop existing FK & index that reference PayStubs
    -- ========================================================================

    PRINT 'Phase 0: Dropping foreign key constraints and indexes...'

    -- Check if foreign key exists before dropping
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_PayStubDetails_PayStubs_PayStubId')
    BEGIN
        ALTER TABLE PayStubDetails DROP CONSTRAINT FK_PayStubDetails_PayStubs_PayStubId
        PRINT '  - Dropped FK_PayStubDetails_PayStubs_PayStubId'
    END

    -- Check if index exists before dropping
    IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_PayStubDetails_PayStubId' AND object_id = OBJECT_ID('PayStubDetails'))
    BEGIN
        DROP INDEX IX_PayStubDetails_PayStubId ON PayStubDetails
        PRINT '  - Dropped IX_PayStubDetails_PayStubId'
    END

    -- ========================================================================
    -- Phase 1: Migrate PayStubs Primary Key from GUID to INT IDENTITY
    -- ========================================================================

    PRINT 'Phase 1: Migrating PayStubs primary key...'

    -- Step 1: Create temp table with new INT IDs using ROW_NUMBER()
    IF OBJECT_ID('tempdb..#TempPayStubs') IS NOT NULL
        DROP TABLE #TempPayStubs

    SELECT Id AS OldId, TimeSheetId, EmployeeId, Name,
           ROW_NUMBER() OVER (ORDER BY Id) AS NewId
    INTO #TempPayStubs
    FROM PayStubs

    -- Validate temp table creation
    DECLARE @TempPayStubCount INT
    SELECT @TempPayStubCount = COUNT(*) FROM #TempPayStubs

    IF @TempPayStubCount != @PayStubCount
    BEGIN
        SET @ErrorMsg = 'Error: Temp table row count mismatch. Expected: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', Got: ' + CAST(@TempPayStubCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    PRINT '  - Created temporary table with ' + CAST(@TempPayStubCount AS NVARCHAR(10)) + ' records'

    -- Step 2: Create new PayStubs table with INT IDENTITY
    CREATE TABLE PayStubs_New (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OldId UNIQUEIDENTIFIER NULL,
        TimeSheetId INT NOT NULL,
        EmployeeId INT NOT NULL,
        Name NVARCHAR(MAX) NULL
    )

    PRINT '  - Created new PayStubs table with INT identity'

    -- Step 3: Set identity seed using DBCC CHECKIDENT (following TimeSheet migration pattern)
    DECLARE @MaxPayStubId INT
    SELECT @MaxPayStubId = ISNULL(MAX(NewId), 0) FROM #TempPayStubs
    DBCC CHECKIDENT ('PayStubs_New', RESEED, @MaxPayStubId)
    PRINT '  - Set PayStubs identity seed to ' + CAST(@MaxPayStubId AS NVARCHAR(10))

    -- Step 4: Insert data preserving the ROW_NUMBER() assigned IDs
    SET IDENTITY_INSERT PayStubs_New ON

    INSERT INTO PayStubs_New (Id, OldId, TimeSheetId, EmployeeId, Name)
    SELECT NewId, OldId, TimeSheetId, EmployeeId, Name
    FROM #TempPayStubs
    ORDER BY NewId

    SET IDENTITY_INSERT PayStubs_New OFF

    -- Validate data insertion
    DECLARE @NewPayStubCount INT
    SELECT @NewPayStubCount = COUNT(*) FROM PayStubs_New

    IF @NewPayStubCount != @PayStubCount
    BEGIN
        SET @ErrorMsg = 'Error: New PayStubs table row count mismatch. Expected: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', Got: ' + CAST(@NewPayStubCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    PRINT '  - Inserted ' + CAST(@NewPayStubCount AS NVARCHAR(10)) + ' records into new table'

    -- Step 4: Drop original PayStubs table and rename new one
    DROP TABLE PayStubs
    EXEC sp_rename 'PayStubs_New', 'PayStubs'

    PRINT '  - Replaced original PayStubs table'

    -- ========================================================================
    -- Phase 2: Update PayStubDetails.PayStubId to reference new INT IDs
    -- ========================================================================

    PRINT 'Phase 2: Updating PayStubDetails foreign keys...'

    -- Step 1: Add new PayStubId_New column
    ALTER TABLE PayStubDetails ADD PayStubId_New INT NULL
    PRINT '  - Added PayStubId_New column'

    -- Step 2: Update PayStubId_New with corresponding INT values
    UPDATE psd
    SET PayStubId_New = t.NewId
    FROM PayStubDetails psd
    INNER JOIN #TempPayStubs t ON psd.PayStubId = t.OldId

    -- Validate foreign key updates
    DECLARE @UpdatedDetailCount INT, @NullDetailCount INT
    SELECT @UpdatedDetailCount = COUNT(*) FROM PayStubDetails WHERE PayStubId_New IS NOT NULL
    SELECT @NullDetailCount = COUNT(*) FROM PayStubDetails WHERE PayStubId_New IS NULL

    IF @NullDetailCount > 0
    BEGIN
        SET @ErrorMsg = 'Error: ' + CAST(@NullDetailCount AS NVARCHAR(10)) + ' PayStubDetails have NULL PayStubId_New values'
        RAISERROR(@ErrorMsg, 16, 1)
    END

    PRINT '  - Updated ' + CAST(@UpdatedDetailCount AS NVARCHAR(10)) + ' PayStubDetail foreign keys'

    -- Step 3: Drop old PayStubId column and rename new one
    ALTER TABLE PayStubDetails DROP COLUMN PayStubId
    EXEC sp_rename 'PayStubDetails.PayStubId_New', 'PayStubId', 'COLUMN'
    ALTER TABLE PayStubDetails ALTER COLUMN PayStubId INT NOT NULL

    PRINT '  - Replaced PayStubId column with INT type'

    -- ========================================================================
    -- Phase 3: Migrate PayStubDetails Primary Key from GUID to INT IDENTITY
    -- ========================================================================

    PRINT 'Phase 3: Migrating PayStubDetails primary key...'

    -- Step 1: Create temp table with new INT IDs using ROW_NUMBER()
    IF OBJECT_ID('tempdb..#TempPayStubDetails') IS NOT NULL
        DROP TABLE #TempPayStubDetails

    SELECT Id AS OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
           JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
           Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode,
           ROW_NUMBER() OVER (ORDER BY Id) AS NewId
    INTO #TempPayStubDetails
    FROM PayStubDetails

    -- Validate temp table creation
    DECLARE @TempDetailCount INT
    SELECT @TempDetailCount = COUNT(*) FROM #TempPayStubDetails

    IF @TempDetailCount != @PayStubDetailCount
    BEGIN
        SET @ErrorMsg = 'Error: Temp PayStubDetails table row count mismatch. Expected: ' + CAST(@PayStubDetailCount AS NVARCHAR(10)) + ', Got: ' + CAST(@TempDetailCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    PRINT '  - Created temporary table with ' + CAST(@TempDetailCount AS NVARCHAR(10)) + ' records'

    -- Step 2: Create new PayStubDetails table with INT IDENTITY
    CREATE TABLE PayStubDetails_New (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OldId UNIQUEIDENTIFIER NULL,
        PayStubId INT NOT NULL,
        Name NVARCHAR(MAX) NULL,
        WorkDate DATE NOT NULL,
        STHours REAL NULL,
        OTHours REAL NULL,
        DTHours REAL NULL,
        JobCode NVARCHAR(MAX) NULL,
        AgreementId INT NULL,
        ClassificationId INT NULL,
        CostCenter NVARCHAR(MAX) NULL,
        HourlyRate REAL NULL,
        Bonus REAL NULL,
        Expenses REAL NULL,
        ReportLineItemId INT NULL,
        SubClassificationId INT NULL,
        EarningsCode NVARCHAR(MAX) NULL
    )

    PRINT '  - Created new PayStubDetails table with INT identity'

    -- Step 3: Set identity seed using DBCC CHECKIDENT (following TimeSheet migration pattern)
    DECLARE @MaxPayStubDetailId INT
    SELECT @MaxPayStubDetailId = ISNULL(MAX(NewId), 0) FROM #TempPayStubDetails
    DBCC CHECKIDENT ('PayStubDetails_New', RESEED, @MaxPayStubDetailId)
    PRINT '  - Set PayStubDetails identity seed to ' + CAST(@MaxPayStubDetailId AS NVARCHAR(10))

    -- Step 4: Insert data preserving the ROW_NUMBER() assigned IDs
    SET IDENTITY_INSERT PayStubDetails_New ON

    INSERT INTO PayStubDetails_New (Id, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                                    JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                                    Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode)
    SELECT NewId, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
           JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
           Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
    FROM #TempPayStubDetails
    ORDER BY NewId

    SET IDENTITY_INSERT PayStubDetails_New OFF

    -- Validate data insertion
    DECLARE @NewDetailCount INT
    SELECT @NewDetailCount = COUNT(*) FROM PayStubDetails_New

    IF @NewDetailCount != @PayStubDetailCount
    BEGIN
        SET @ErrorMsg = 'Error: New PayStubDetails table row count mismatch. Expected: ' + CAST(@PayStubDetailCount AS NVARCHAR(10)) + ', Got: ' + CAST(@NewDetailCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    PRINT '  - Inserted ' + CAST(@NewDetailCount AS NVARCHAR(10)) + ' records into new table'

    -- Step 4: Drop original PayStubDetails table and rename new one
    DROP TABLE PayStubDetails
    EXEC sp_rename 'PayStubDetails_New', 'PayStubDetails'

    PRINT '  - Replaced original PayStubDetails table'

    -- ========================================================================
    -- Phase 4: Restore Foreign Key Constraints and Indexes
    -- ========================================================================

    PRINT 'Phase 4: Restoring foreign key constraints and indexes...'

    -- Restore PayStubs constraints
    ALTER TABLE PayStubs
    ADD CONSTRAINT FK_PayStubs_TimeSheets_TimeSheetId
    FOREIGN KEY (TimeSheetId) REFERENCES TimeSheets(Id) ON DELETE CASCADE

    PRINT '  - Restored FK_PayStubs_TimeSheets_TimeSheetId'

    -- Note: FK_PayStubs_Employees_EmployeeId will be added via EF migration methods
    -- to ensure proper schema registration

    -- Restore PayStubDetails constraints
    ALTER TABLE PayStubDetails
    ADD CONSTRAINT FK_PayStubDetails_PayStubs_PayStubId
    FOREIGN KEY (PayStubId) REFERENCES PayStubs(Id) ON DELETE CASCADE

    PRINT '  - Restored FK_PayStubDetails_PayStubs_PayStubId'

    -- Note: FK_PayStubDetails_ReportLineItems_ReportLineItemId will be added via EF migration methods

    -- Restore Indexes
    CREATE INDEX IX_PayStubs_TimeSheetId ON PayStubs(TimeSheetId)
    CREATE INDEX IX_PayStubs_EmployeeId ON PayStubs(EmployeeId)
    CREATE INDEX IX_PayStubDetails_PayStubId ON PayStubDetails(PayStubId)
    CREATE INDEX IX_PayStubDetails_ReportLineItemId ON PayStubDetails(ReportLineItemId)

    PRINT '  - Restored all indexes'

    -- ========================================================================
    -- Phase 5: Final Data Integrity Verification
    -- ========================================================================

    PRINT 'Phase 5: Final data integrity verification...'

    -- Verify final record counts
    DECLARE @FinalPayStubCount INT, @FinalDetailCount INT
    SELECT @FinalPayStubCount = COUNT(*) FROM PayStubs
    SELECT @FinalDetailCount = COUNT(*) FROM PayStubDetails

    IF @FinalPayStubCount != @PayStubCount
    BEGIN
        SET @ErrorMsg = 'Final verification failed: PayStubs count mismatch. Expected: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', Got: ' + CAST(@FinalPayStubCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    IF @FinalDetailCount != @PayStubDetailCount
    BEGIN
        SET @ErrorMsg = 'Final verification failed: PayStubDetails count mismatch. Expected: ' + CAST(@PayStubDetailCount AS NVARCHAR(10)) + ', Got: ' + CAST(@FinalDetailCount AS NVARCHAR(10))
        RAISERROR(@ErrorMsg, 16, 1)
    END

    -- Verify foreign key integrity
    DECLARE @OrphanedDetails INT
    SELECT @OrphanedDetails = COUNT(*)
    FROM PayStubDetails psd
    LEFT JOIN PayStubs ps ON psd.PayStubId = ps.Id
    WHERE ps.Id IS NULL

    IF @OrphanedDetails > 0
    BEGIN
        SET @ErrorMsg = 'Final verification failed: ' + CAST(@OrphanedDetails AS NVARCHAR(10)) + ' orphaned PayStubDetails found'
        RAISERROR(@ErrorMsg, 16, 1)
    END

    -- Verify identity sequences are properly set with DBCC CHECKIDENT
    DECLARE @MaxPayStubId INT, @MaxDetailId INT
    SELECT @MaxPayStubId = ISNULL(MAX(Id), 0) FROM PayStubs
    SELECT @MaxDetailId = ISNULL(MAX(Id), 0) FROM PayStubDetails

    -- Final identity seed verification and reset for future inserts
    DBCC CHECKIDENT ('PayStubs', RESEED, @MaxPayStubId)
    DBCC CHECKIDENT ('PayStubDetails', RESEED, @MaxDetailId)

    PRINT '  - PayStubs: ' + CAST(@FinalPayStubCount AS NVARCHAR(10)) + ' records, max ID: ' + CAST(@MaxPayStubId AS NVARCHAR(10)) + ', identity seed set'
    PRINT '  - PayStubDetails: ' + CAST(@FinalDetailCount AS NVARCHAR(10)) + ' records, max ID: ' + CAST(@MaxDetailId AS NVARCHAR(10)) + ', identity seed set'
    PRINT '  - No orphaned records found'
    PRINT '  - Identity sequences properly seeded for future insertions'

    -- ========================================================================
    -- Cleanup and Commit
    -- ========================================================================

    -- Clean up temporary tables
    IF OBJECT_ID('tempdb..#TempPayStubs') IS NOT NULL
    BEGIN
        DROP TABLE #TempPayStubs
        PRINT 'Cleaned up #TempPayStubs'
    END

    IF OBJECT_ID('tempdb..#TempPayStubDetails') IS NOT NULL
    BEGIN
        DROP TABLE #TempPayStubDetails
        PRINT 'Cleaned up #TempPayStubDetails'
    END

    COMMIT TRANSACTION PayStubGuidToIntMigration

    PRINT 'PayStub GUID to INT migration completed successfully!'
    PRINT 'Final counts - PayStubs: ' + CAST(@FinalPayStubCount AS NVARCHAR(10)) + ', PayStubDetails: ' + CAST(@FinalDetailCount AS NVARCHAR(10))

END TRY
BEGIN CATCH
    -- ========================================================================
    -- Error Handling and Rollback
    -- ========================================================================

    -- Rollback transaction if still active
    IF @@TRANCOUNT > 0
    BEGIN
        ROLLBACK TRANSACTION PayStubGuidToIntMigration
        PRINT 'Transaction rolled back due to error'
    END

    -- Clean up temporary tables in case of error
    IF OBJECT_ID('tempdb..#TempPayStubs') IS NOT NULL
    BEGIN
        DROP TABLE #TempPayStubs
        PRINT 'Cleaned up #TempPayStubs after error'
    END

    IF OBJECT_ID('tempdb..#TempPayStubDetails') IS NOT NULL
    BEGIN
        DROP TABLE #TempPayStubDetails
        PRINT 'Cleaned up #TempPayStubDetails after error'
    END

    -- Clean up any intermediate tables that might have been created
    IF OBJECT_ID('PayStubs_New') IS NOT NULL
    BEGIN
        DROP TABLE PayStubs_New
        PRINT 'Cleaned up PayStubs_New after error'
    END

    IF OBJECT_ID('PayStubDetails_New') IS NOT NULL
    BEGIN
        DROP TABLE PayStubDetails_New
        PRINT 'Cleaned up PayStubDetails_New after error'
    END

    -- Log detailed error information
    DECLARE @ErrorNumber INT = ERROR_NUMBER()
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @ErrorState INT = ERROR_STATE()
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @ErrorLine INT = ERROR_LINE()

    SET @ErrorMsg = 'PayStub GUID to INT migration failed at line ' + CAST(@ErrorLine AS NVARCHAR(10)) +
                   ' with error ' + CAST(@ErrorNumber AS NVARCHAR(10)) + ': ' + @ErrorMessage

    PRINT @ErrorMsg

    -- Log error but don't throw to prevent total migration failure
    PRINT 'Migration failed but system remains in original state due to transaction rollback'

END CATCH";

            migrationBuilder.Sql(sql);

            // Add the foreign keys that need EF registration
            migrationBuilder.AddForeignKey(
                name: "FK_PayStubs_Employees_EmployeeId",
                table: "PayStubs",
                column: "EmployeeId",
                principalSchema: "Core",
                principalTable: "Employees",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubDetails_ReportLineItems_ReportLineItemId",
                table: "PayStubDetails",
                column: "ReportLineItemId",
                principalSchema: "Core",
                principalTable: "ReportLineItems",
                principalColumn: "ID"
            );
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // ========================================================================
            // Rollback Phase 1: Convert PayStubs back to GUID primary key
            // ========================================================================

            // Step 1: Create temp table with current INT IDs and restore original GUIDs
            migrationBuilder.Sql(
                @"
                SELECT Id AS NewId, OldId, TimeSheetId, EmployeeId, Name
                INTO #TempPayStubsRollback
                FROM PayStubs
                WHERE OldId IS NOT NULL;
            "
            );

            // Step 2: Create new PayStubs table with GUID primary key
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubs_Rollback (
                    Id UNIQUEIDENTIFIER PRIMARY KEY,
                    TimeSheetId INT NOT NULL,
                    EmployeeId INT NOT NULL,
                    Name NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data using original GUID IDs
            migrationBuilder.Sql(
                @"
                INSERT INTO PayStubs_Rollback (Id, TimeSheetId, EmployeeId, Name)
                SELECT OldId, TimeSheetId, EmployeeId, Name
                FROM #TempPayStubsRollback;
            "
            );

            // Step 4: Drop current PayStubs table and rename rollback table
            migrationBuilder.Sql("DROP TABLE PayStubs;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubs_Rollback', 'PayStubs';");

            // ========================================================================
            // Rollback Phase 2: Update PayStubDetails.PayStubId back to GUID
            // ========================================================================

            // Step 1: Add new PayStubId_Rollback column
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD PayStubId_Rollback UNIQUEIDENTIFIER NULL;
            "
            );

            // Step 2: Update PayStubId_Rollback with corresponding GUID values
            migrationBuilder.Sql(
                @"
                UPDATE psd
                SET PayStubId_Rollback = t.OldId
                FROM PayStubDetails psd
                INNER JOIN #TempPayStubsRollback t ON psd.PayStubId = t.NewId;
            "
            );

            // Step 3: Drop old PayStubId column and rename rollback column
            migrationBuilder.Sql("ALTER TABLE PayStubDetails DROP COLUMN PayStubId;");
            migrationBuilder.Sql(
                "EXEC sp_rename 'PayStubDetails.PayStubId_Rollback', 'PayStubId', 'COLUMN';"
            );
            migrationBuilder.Sql(
                "ALTER TABLE PayStubDetails ALTER COLUMN PayStubId UNIQUEIDENTIFIER NOT NULL;"
            );

            // ========================================================================
            // Rollback Phase 3: Convert PayStubDetails back to GUID primary key
            // ========================================================================

            // Step 1: Create temp table with current INT IDs and restore original GUIDs
            migrationBuilder.Sql(
                @"
                SELECT Id AS NewId, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
                INTO #TempPayStubDetailsRollback
                FROM PayStubDetails
                WHERE OldId IS NOT NULL;
            "
            );

            // Step 2: Create new PayStubDetails table with GUID primary key
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubDetails_Rollback (
                    Id UNIQUEIDENTIFIER PRIMARY KEY,
                    PayStubId UNIQUEIDENTIFIER NOT NULL,
                    Name NVARCHAR(MAX) NULL,
                    WorkDate DATE NOT NULL,
                    STHours REAL NULL,
                    OTHours REAL NULL,
                    DTHours REAL NULL,
                    JobCode NVARCHAR(MAX) NULL,
                    AgreementId INT NULL,
                    ClassificationId INT NULL,
                    CostCenter NVARCHAR(MAX) NULL,
                    HourlyRate REAL NULL,
                    Bonus REAL NULL,
                    Expenses REAL NULL,
                    ReportLineItemId INT NULL,
                    SubClassificationId INT NULL,
                    EarningsCode NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data using original GUID IDs
            migrationBuilder.Sql(
                @"
                INSERT INTO PayStubDetails_Rollback (Id, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                                                    JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                                                    Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode)
                SELECT OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
                FROM #TempPayStubDetailsRollback;
            "
            );

            // Step 4: Drop current PayStubDetails table and rename rollback table
            migrationBuilder.Sql("DROP TABLE PayStubDetails;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubDetails_Rollback', 'PayStubDetails';");

            // ========================================================================
            // Rollback Phase 4: Restore Foreign Key Constraints and Indexes
            // ========================================================================

            // Restore PayStubs constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubs
                ADD CONSTRAINT FK_PayStubs_TimeSheets_TimeSheetId
                FOREIGN KEY (TimeSheetId) REFERENCES TimeSheets(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubs_Employees_EmployeeId",
                table: "PayStubs",
                column: "EmployeeId",
                principalSchema: "Core",
                principalTable: "Employees",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade
            );

            // Restore PayStubDetails constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD CONSTRAINT FK_PayStubDetails_PayStubs_PayStubId
                FOREIGN KEY (PayStubId) REFERENCES PayStubs(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubDetails_ReportLineItems_ReportLineItemId",
                table: "PayStubDetails",
                column: "ReportLineItemId",
                principalSchema: "Core",
                principalTable: "ReportLineItems",
                principalColumn: "ID"
            );

            // Restore Indexes
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_TimeSheetId ON PayStubs(TimeSheetId);");
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_EmployeeId ON PayStubs(EmployeeId);");
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_PayStubId ON PayStubDetails(PayStubId);"
            );
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_ReportLineItemId ON PayStubDetails(ReportLineItemId);"
            );

            // Clean up temporary tables
            migrationBuilder.Sql("DROP TABLE #TempPayStubsRollback;");
            migrationBuilder.Sql("DROP TABLE #TempPayStubDetailsRollback;");

            // Verify rollback data integrity
            migrationBuilder.Sql(
                @"
                DECLARE @PayStubCount INT, @PayStubDetailCount INT;
                SELECT @PayStubCount = COUNT(*) FROM PayStubs;
                SELECT @PayStubDetailCount = COUNT(*) FROM PayStubDetails;

                IF @PayStubCount = 0 OR @PayStubDetailCount = 0
                BEGIN
                    PRINT 'Rollback completed successfully. PayStubs: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', PayStubDetails: ' + CAST(@PayStubDetailCount AS NVARCHAR(10));
                END
            "
            );
        }
    }
}
