using System.ComponentModel;
using System.Security.Claims;
using backend.Data.HttpQueries;
using backend.Data.Models;
using OpenIddict.Abstractions;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace backend.Utils;

public static class AuthUtils
{
    /// <summary>
    /// Gets the organization GUID, allowing system administrators to specify a different organization
    /// </summary>
    /// <param name="query">Query containing optional GUID parameter</param>
    /// <param name="request">HTTP request</param>
    /// <returns>The organization GUID</returns>
    /// <exception cref="ArgumentNullException">Thrown when request is null</exception>
    public static Guid GetOrgGUID(OptionalGuidQuery query, HttpRequest request)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));
        ArgumentNullException.ThrowIfNull(query, nameof(query));

        if (
            request.HttpContext?.User?.IsInRole(Constants.Roles.SystemAdministrator) == true
            && query.GUID.HasValue
        )
        {
            return query.GUID.Value;
        }
        else
        {
            return GetUserOrgGUID(request.HttpContext);
        }
    }

    /// <summary>
    /// Gets the organization GUID for the current authenticated user
    /// </summary>
    /// <param name="httpContext">The HTTP context containing user claims</param>
    /// <returns>The organization GUID</returns>
    /// <exception cref="ArgumentNullException">Thrown when httpContext is null</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown when organization GUID claim is not found</exception>
    public static Guid GetUserOrgGUID(HttpContext httpContext)
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));

        if (httpContext.User?.Identity?.IsAuthenticated != true)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var orgGuidClaim = httpContext.User.GetClaim(Constants.Claims.OrganizationGuid);
        if (string.IsNullOrEmpty(orgGuidClaim))
        {
            throw new UnauthorizedAccessException("Organization GUID claim not found");
        }

        if (!Guid.TryParse(orgGuidClaim, out var orgGuid))
        {
            throw new UnauthorizedAccessException("Invalid organization GUID format");
        }

        return orgGuid;
    }

    /// <summary>
    /// Gets the user email from HTTP context
    /// </summary>
    /// <param name="httpContext">The HTTP context</param>
    /// <returns>The user email</returns>
    /// <exception cref="ArgumentNullException">Thrown when httpContext is null</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown when email claim is not found</exception>
    public static string GetUserEmail(HttpContext httpContext)
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));

        if (httpContext.User?.Identity?.IsAuthenticated != true)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var email = httpContext.User.GetClaim(Claims.Email);
        if (string.IsNullOrEmpty(email))
        {
            throw new UnauthorizedAccessException("Email claim not found in user token");
        }

        return email;
    }

    public static int GetCurrentChapterId(
        OptionalGuidQuery query,
        HttpRequest request,
        EPRLiveDBContext db,
        IWebHostEnvironment env
    )
    {
        ArgumentNullException.ThrowIfNull(query, nameof(query));
        ArgumentNullException.ThrowIfNull(request, nameof(request));
        ArgumentNullException.ThrowIfNull(db, nameof(db));
        ArgumentNullException.ThrowIfNull(env, nameof(env));

        try
        {
            Guid UserOrgGUID = GetOrgGUID(query, request);
            int OrgID = db.Roots.Where((r) => r.Guid == UserOrgGUID).First().Id;
            Organization org = db.Organizations.Where((o) => o.Id == OrgID).First();

            if (org.DorganizationTypeId == 2)
            {
                return OrgID;
            }
            else
            {
                int relationshipType = -1;
                switch (org.DorganizationTypeId)
                {
                    case 3:
                        relationshipType = 3;
                        break;
                    case 4:
                        relationshipType = 4;
                        break;
                    case 5:
                        relationshipType = 5;
                        break;
                }
                Relationship? relationship = db
                    .Relationships.Where(
                        (r) => r.DrelationshipTypeId == relationshipType && r.RightPartyId == OrgID
                    )
                    .FirstOrDefault();
                if (relationship != null)
                {
                    return relationship.LeftPartyId;
                }
            }
        }
        catch (Exception ex) when (!(ex is ArgumentNullException))
        {
            // Log the exception but don't expose details to the caller
            throw new UnauthorizedAccessException("Unable to determine chapter access", ex);
        }

        return -1;
    }

    /// <summary>
    /// Gets the username from HTTP context
    /// </summary>
    /// <param name="httpContext">The HTTP context</param>
    /// <returns>The username</returns>
    /// <exception cref="ArgumentNullException">Thrown when httpContext is null</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown when username claim is not found</exception>
    public static string GetUsername(HttpContext httpContext)
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));

        if (httpContext.User?.Identity?.IsAuthenticated != true)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var username = httpContext.User.GetClaim(Claims.Username);
        if (string.IsNullOrEmpty(username))
        {
            throw new UnauthorizedAccessException("Username claim not found in user token");
        }

        return username;
    }

    /// <summary>
    /// Gets the user GUID from HTTP context
    /// </summary>
    /// <param name="httpContext">The HTTP context</param>
    /// <returns>The user GUID</returns>
    /// <exception cref="ArgumentNullException">Thrown when httpContext is null</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown when subject claim is not found or invalid</exception>
    public static Guid GetUserGuid(HttpContext httpContext)
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));

        if (httpContext.User?.Identity?.IsAuthenticated != true)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var subjectClaim = httpContext.User.GetClaim(Claims.Subject);
        if (string.IsNullOrEmpty(subjectClaim))
        {
            throw new UnauthorizedAccessException("Subject claim not found in user token");
        }

        if (!Guid.TryParse(subjectClaim, out var userGuid))
        {
            throw new UnauthorizedAccessException("Invalid subject GUID format in user token");
        }

        return userGuid;
    }

    /// <summary>
    /// Attempts to retrieve and convert the first value of a specified claim from the HttpContext's user.
    /// </summary>
    /// <typeparam name="T">The type to convert the claim value to (e.g., Guid, int, string).</typeparam>
    /// <param name="httpContext">The HttpContext containing the user claims.</param>
    /// <param name="claimType">The type of claim to retrieve (e.g., "sub", "email").</param>
    /// <param name="value">When this method returns, contains the converted claim value if successful; otherwise, default(T).</param>
    /// <returns>True if the claim was found and successfully converted; otherwise, false.</returns>
    /// <exception cref="ArgumentNullException">Thrown if httpContext or claimType is null.</exception>
    public static bool TryGetUserClaim<T>(HttpContext httpContext, string claimType, out T value)
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));
        ArgumentNullException.ThrowIfNull(claimType, nameof(claimType));

        value = default;

        // Additional null checks for defensive programming
        if (httpContext.User?.Identity?.IsAuthenticated != true)
        {
            return false;
        }

        var claimValue = httpContext.User?.FindFirstValue(claimType);
        if (claimValue == null)
        {
            return false;
        }

        // Handle string directly since it's a common claim type
        if (typeof(T) == typeof(string))
        {
            value = (T)(object)claimValue;
            return true;
        }

        // Handle value types and other convertible types
        if (typeof(T).IsValueType || typeof(T) == typeof(string))
        {
            var converter = TypeDescriptor.GetConverter(typeof(T));
            if (converter.CanConvertFrom(typeof(string)))
            {
                try
                {
                    value = (T)converter.ConvertFromString(claimValue)!;
                    return true;
                }
                catch (Exception)
                {
                    // Conversion failed (e.g., format exception)
                    return false;
                }
            }
        }

        // Type not supported for conversion
        return false;
    }

    /// <summary>
    /// Attempts to retrieve and convert all values of a specified claim from the HttpContext's user.
    /// </summary>
    /// <typeparam name="T">The type to convert each claim value to (e.g., string for roles).</typeparam>
    /// <param name="httpContext">The HttpContext containing the user claims.</param>
    /// <param name="claimType">The type of claim to retrieve (e.g., OpenIddictConstants.Claims.Role).</param>
    /// <param name="values">When this method returns, contains the list of converted claim values if successful; otherwise, null.</param>
    /// <returns>True if at least one claim value was found and converted; otherwise, false.</returns>
    /// <exception cref="ArgumentNullException">Thrown if httpContext or claimType is null.</exception>
    public static bool TryGetUserClaims<T>(
        HttpContext httpContext,
        string claimType,
        out IEnumerable<T> values
    )
    {
        ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));
        ArgumentNullException.ThrowIfNull(claimType, nameof(claimType));

        values = Array.Empty<T>();

        // Map common OpenIddict claims to standard ClaimTypes
        string actualClaimType = claimType;
        if (claimType.Equals("role", StringComparison.OrdinalIgnoreCase))
        {
            actualClaimType = ClaimTypes.Role; // This will be the full URI
        }

        var claimValues = httpContext.User?.FindAll(actualClaimType)?.Select(c => c.Value);
        if (claimValues == null || !claimValues.Any())
        {
            // Fallback to original claim type if the mapped one returned nothing
            claimValues = httpContext.User?.FindAll(claimType)?.Select(c => c.Value);
            if (claimValues == null || !claimValues.Any())
            {
                return false;
            }
        }

        // Rest of the method remains the same
        var convertedValues = new List<T>();
        foreach (var claimValue in claimValues)
        {
            if (typeof(T) == typeof(string))
            {
                convertedValues.Add((T)(object)claimValue);
            }
            else if (typeof(T).IsValueType)
            {
                var converter = TypeDescriptor.GetConverter(typeof(T));
                if (converter.CanConvertFrom(typeof(string)))
                {
                    try
                    {
                        var convertedValue = (T)converter.ConvertFromString(claimValue)!;
                        convertedValues.Add(convertedValue);
                    }
                    catch
                    {
                        // Skip invalid values, continue with others
                    }
                }
            }
        }

        values = convertedValues;
        return convertedValues.Any();
    }
}
