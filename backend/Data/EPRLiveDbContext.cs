using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace backend.Data.Models;

public partial class EPRLiveDBContext : DbContext
{
    public EPRLiveDBContext() { }

    public EPRLiveDBContext(DbContextOptions<EPRLiveDBContext> options)
        : base(options) { }

    public virtual DbSet<Address> Addresses { get; set; }

    public virtual DbSet<Agreement> Agreements { get; set; }

    public virtual DbSet<AgreementClassification> AgreementClassifications { get; set; }

    public virtual DbSet<AgreementsToBenefit> AgreementsToBenefits { get; set; }

    public virtual DbSet<AgreementsToContract> AgreementsToContracts { get; set; }

    public virtual DbSet<ApplicationConfig> ApplicationConfigs { get; set; }

    public virtual DbSet<AspnetApplication> AspnetApplications { get; set; }

    public virtual DbSet<AspnetMembership> AspnetMemberships { get; set; }

    public virtual DbSet<AspnetPath> AspnetPaths { get; set; }

    public virtual DbSet<AspnetPersonalizationAllUser> AspnetPersonalizationAllUsers { get; set; }

    public virtual DbSet<AspnetPersonalizationPerUser> AspnetPersonalizationPerUsers { get; set; }

    public virtual DbSet<AspnetProfile> AspnetProfiles { get; set; }

    public virtual DbSet<AspnetRole> AspnetRoles { get; set; }

    public virtual DbSet<AspnetSchemaVersion> AspnetSchemaVersions { get; set; }

    public virtual DbSet<AspnetUser> AspnetUsers { get; set; }

    public virtual DbSet<AspnetWebEventEvent> AspnetWebEventEvents { get; set; }

    public virtual DbSet<AspstateTempApplication> AspstateTempApplications { get; set; }

    public virtual DbSet<AspstateTempSession> AspstateTempSessions { get; set; }

    public virtual DbSet<Benefit> Benefits { get; set; }

    public virtual DbSet<BenefitOverride> BenefitOverrides { get; set; }

    public virtual DbSet<BenefitOverridesOrig> BenefitOverridesOrigs { get; set; }

    public virtual DbSet<Chapter> Chapters { get; set; }

    public virtual DbSet<ChapterToEmployerRelationship> ChapterToEmployerRelationships { get; set; }

    public virtual DbSet<ChaptersTradeAdministrator> ChaptersTradeAdministrators { get; set; }

    public virtual DbSet<ClassificationName> ClassificationNames { get; set; }

    public virtual DbSet<ClassificationXref> ClassificationXrefs { get; set; }

    public virtual DbSet<ContactMechanism> ContactMechanisms { get; set; }

    public virtual DbSet<Contract> Contracts { get; set; }

    public virtual DbSet<Control> Controls { get; set; }

    public virtual DbSet<CostCode> CostCodes { get; set; }

    public virtual DbSet<CreateRecurringPaymentProfileResponse> CreateRecurringPaymentProfileResponses { get; set; }

    public virtual DbSet<CreditCardPaymentMethod> CreditCardPaymentMethods { get; set; }

    public virtual DbSet<CustomReport> CustomReports { get; set; }

    public virtual DbSet<CustomReportPivot> CustomReportPivots { get; set; }

    public virtual DbSet<CustomReportProduct> CustomReportProducts { get; set; }

    public virtual DbSet<CustomReportSubtotal> CustomReportSubtotals { get; set; }

    public virtual DbSet<DaddressType> DaddressTypes { get; set; }

    public virtual DbSet<DagreementType> DagreementTypes { get; set; }

    public virtual DbSet<DamendmentAction> DamendmentActions { get; set; }

    public virtual DbSet<DatabaseMigration> DatabaseMigrations { get; set; }

    public virtual DbSet<DatabaseSpace> DatabaseSpaces { get; set; }

    public virtual DbSet<DcalculationMethod> DcalculationMethods { get; set; }

    public virtual DbSet<DcalculationModifier> DcalculationModifiers { get; set; }

    public virtual DbSet<Dcategory> Dcategories { get; set; }

    public virtual DbSet<DclassificationCode> DclassificationCodes { get; set; }

    public virtual DbSet<DcontactMechanismType> DcontactMechanismTypes { get; set; }

    public virtual DbSet<Dcountry> Dcountries { get; set; }

    public virtual DbSet<DcustomReportType> DcustomReportTypes { get; set; }

    public virtual DbSet<DelectronicPaymentOption> DelectronicPaymentOptions { get; set; }

    public virtual DbSet<DelinquencyLetter> DelinquencyLetters { get; set; }

    public virtual DbSet<DemailAddressType> DemailAddressTypes { get; set; }

    public virtual DbSet<DentityType> DentityTypes { get; set; }

    public virtual DbSet<DeventSubType> DeventSubTypes { get; set; }

    public virtual DbSet<DeventType> DeventTypes { get; set; }

    public virtual DbSet<Dgender> Dgenders { get; set; }

    public virtual DbSet<DguidSubstitutionType> DguidSubstitutionTypes { get; set; }

    public virtual DbSet<DoExpressCheckoutPaymentResponse> DoExpressCheckoutPaymentResponses { get; set; }

    public virtual DbSet<DorderStatus> DorderStatuses { get; set; }

    public virtual DbSet<DorganizationType> DorganizationTypes { get; set; }

    public virtual DbSet<DpartyType> DpartyTypes { get; set; }

    public virtual DbSet<DpaymentMethodType> DpaymentMethodTypes { get; set; }

    public virtual DbSet<DpaymentType> DpaymentTypes { get; set; }

    public virtual DbSet<DpersonType> DpersonTypes { get; set; }

    public virtual DbSet<DphoneNumberType> DphoneNumberTypes { get; set; }

    public virtual DbSet<Dprovince> Dprovinces { get; set; }

    public virtual DbSet<DrelationshipStatus> DrelationshipStatuses { get; set; }

    public virtual DbSet<DrelationshipSubType> DrelationshipSubTypes { get; set; }

    public virtual DbSet<DrelationshipType> DrelationshipTypes { get; set; }

    public virtual DbSet<DreportStatus> DreportStatuses { get; set; }

    public virtual DbSet<DreportSubscriptionType> DreportSubscriptionTypes { get; set; }

    public virtual DbSet<DsettingsType> DsettingsTypes { get; set; }

    public virtual DbSet<Dstatus> Dstatuses { get; set; }

    public virtual DbSet<DtaMv0> DtaMv0s { get; set; }

    public virtual DbSet<DwebsiteType> DwebsiteTypes { get; set; }

    public virtual DbSet<Eftpayment> Eftpayments { get; set; }

    public virtual DbSet<EftpaymentMethod> EftpaymentMethods { get; set; }

    public virtual DbSet<ElectronicBatch> ElectronicBatches { get; set; }

    public virtual DbSet<ElectronicPayment> ElectronicPayments { get; set; }

    public virtual DbSet<ElectronicPaymentConfiguration> ElectronicPaymentConfigurations { get; set; }

    public virtual DbSet<EmailAddress> EmailAddresses { get; set; }

    public virtual DbSet<Employee> Employees { get; set; }

    public virtual DbSet<EmployeeImport> EmployeeImports { get; set; }

    public virtual DbSet<Employer> Employers { get; set; }

    public virtual DbSet<EmployersToAgreement> EmployersToAgreements { get; set; }

    public virtual DbSet<Epruser> Eprusers { get; set; }

    public virtual DbSet<FundAdministrator> FundAdministrators { get; set; }

    public virtual DbSet<FundingComment> FundingComments { get; set; }

    public virtual DbSet<FundingCommentDetail> FundingCommentDetails { get; set; }

    public virtual DbSet<GetExpressCheckoutDetailsResponse> GetExpressCheckoutDetailsResponses { get; set; }

    public virtual DbSet<GuidSubstitution> GuidSubstitutions { get; set; }

    public virtual DbSet<Image> Images { get; set; }

    public virtual DbSet<Kbaanswer> Kbaanswers { get; set; }

    public virtual DbSet<Kbaquestion> Kbaquestions { get; set; }

    public virtual DbSet<LoggedEvent> LoggedEvents { get; set; }

    public virtual DbSet<MemberDiscountsForAssociationReport> MemberDiscountsForAssociationReports { get; set; }

    public virtual DbSet<MemberDiscountsForAssociationReportBackup> MemberDiscountsForAssociationReportBackups { get; set; }

    public virtual DbSet<MfaRecord> MfaRecords { get; set; }

    public virtual DbSet<Mm8> Mm8s { get; set; }

    public virtual DbSet<Mm9> Mm9s { get; set; }

    public virtual DbSet<Nachaconfiguration> Nachaconfigurations { get; set; }

    public virtual DbSet<NecaAmfRate> NecaAmfRates { get; set; }

    public virtual DbSet<NecaAmfRateSchedule> NecaAmfRateSchedules { get; set; }

    public virtual DbSet<NecaAmfStep> NecaAmfSteps { get; set; }

    public virtual DbSet<NewsItem> NewsItems { get; set; }

    public virtual DbSet<Newtable> Newtables { get; set; }

    public virtual DbSet<Note> Notes { get; set; }

    public virtual DbSet<Order> Orders { get; set; }

    public virtual DbSet<OrderDetail> OrderDetails { get; set; }

    public virtual DbSet<Organization> Organizations { get; set; }

    public virtual DbSet<PaperProcessingAuthorization> PaperProcessingAuthorizations { get; set; }

    public virtual DbSet<PartiesToContactMechanism> PartiesToContactMechanisms { get; set; }

    public virtual DbSet<Party> Parties { get; set; }

    public virtual DbSet<PasswordResetAuthorization> PasswordResetAuthorizations { get; set; }

    public virtual DbSet<PayStub> PayStubs { get; set; }

    public virtual DbSet<PayStubDetail> PayStubDetails { get; set; }

    public virtual DbSet<Payment> Payments { get; set; }

    public virtual DbSet<PaymentDetail> PaymentDetails { get; set; }

    public virtual DbSet<PaymentMethod> PaymentMethods { get; set; }

    public virtual DbSet<Person> Persons { get; set; }

    public virtual DbSet<PhoneNumber> PhoneNumbers { get; set; }

    public virtual DbSet<Product> Products { get; set; }

    public virtual DbSet<Product1> Products1 { get; set; }

    public virtual DbSet<ProductCategory> ProductCategories { get; set; }

    public virtual DbSet<ProductsToOrganizationType> ProductsToOrganizationTypes { get; set; }

    public virtual DbSet<Rate> Rates { get; set; }

    public virtual DbSet<RateSchedule> RateSchedules { get; set; }

    public virtual DbSet<Relationship> Relationships { get; set; }

    public virtual DbSet<RelationshipStatus> RelationshipStatuses { get; set; }

    public virtual DbSet<Report> Reports { get; set; }

    public virtual DbSet<ReportLineItem> ReportLineItems { get; set; }

    public virtual DbSet<ReportLineItemDetail> ReportLineItemDetails { get; set; }

    public virtual DbSet<ReportSubscription> ReportSubscriptions { get; set; }

    public virtual DbSet<ReportSuppression> ReportSuppressions { get; set; }

    public virtual DbSet<ReportedBenefitReleaseAuthorization> ReportedBenefitReleaseAuthorizations { get; set; }

    public virtual DbSet<RoleGroup> RoleGroups { get; set; }

    public virtual DbSet<Root> Roots { get; set; }

    public virtual DbSet<ServiceSubscription> ServiceSubscriptions { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<SpExecutionLog> SpExecutionLogs { get; set; }

    public virtual DbSet<SubClassification> SubClassifications { get; set; }

    public virtual DbSet<SubscriptionService> SubscriptionServices { get; set; }

    public virtual DbSet<ThirdPartyEmployerId> ThirdPartyEmployerIds { get; set; }

    public virtual DbSet<TimeSheet> TimeSheets { get; set; }

    public virtual DbSet<Timeline> Timelines { get; set; }

    public virtual DbSet<TimelinesOrig> TimelinesOrigs { get; set; }

    public virtual DbSet<Trade> Trades { get; set; }

    public virtual DbSet<Union> Unions { get; set; }

    public virtual DbSet<VClassificationXref> VClassificationXrefs { get; set; }

    public virtual DbSet<VCurrentRelationship> VCurrentRelationships { get; set; }

    public virtual DbSet<VEffectiveUserRole> VEffectiveUserRoles { get; set; }

    public virtual DbSet<VOrganization> VOrganizations { get; set; }

    public virtual DbSet<VPayment> VPayments { get; set; }

    public virtual DbSet<VRoleGroupsAndRole> VRoleGroupsAndRoles { get; set; }

    public virtual DbSet<VwAspnetApplication> VwAspnetApplications { get; set; }

    public virtual DbSet<VwAspnetMembershipUser> VwAspnetMembershipUsers { get; set; }

    public virtual DbSet<VwAspnetProfile> VwAspnetProfiles { get; set; }

    public virtual DbSet<VwAspnetRole> VwAspnetRoles { get; set; }

    public virtual DbSet<VwAspnetUser> VwAspnetUsers { get; set; }

    public virtual DbSet<VwAspnetUsersInRole> VwAspnetUsersInRoles { get; set; }

    public virtual DbSet<VwAspnetWebPartStatePath> VwAspnetWebPartStatePaths { get; set; }

    public virtual DbSet<VwAspnetWebPartStateShared> VwAspnetWebPartStateShareds { get; set; }

    public virtual DbSet<VwAspnetWebPartStateUser> VwAspnetWebPartStateUsers { get; set; }

    public virtual DbSet<Website> Websites { get; set; }

    public virtual DbSet<DelinquencyPayment> DelinquencyPayments { get; set; }

    public virtual DbSet<TfEmail> TfEmails { get; set; }

    public virtual DbSet<EmployerRosterView> EmployerRosterViews { get; set; }

    public virtual DbSet<EmployerSimpleId> EmployersSimpleId { get; set; }
    public virtual DbSet<AgreementSimpleId> AgreementsSimpleId { get; set; }
    public virtual DbSet<ClassificationSimpleId> ClassificationsSimpleId { get; set; }
    public virtual DbSet<BenefitSimpleId> BenefitsSimpleId { get; set; }
    public virtual DbSet<OregonPacificReportRow> OregonPacificReportRows { get; set; }
    public virtual DbSet<ManhoursReportRow> ManhoursReportRows { get; set; }
    public virtual DbSet<AgreementToContractFull> AgreementsToContractsFull { get; set; }
    public virtual DbSet<BenefitToContract> BenefitToContracts { get; set; }
    public virtual DbSet<SystemPassword> SystemPasswords { get; set; }

    public virtual DbSet<DownloadContribution> DownloadContributions { get; set; }

    public virtual DbSet<TimeSheetExportLineItem> TimeSheetExportLineItems { get; set; }
    public virtual DbSet<TimeSheetEmployeeDeductibleBenefit> TimeSheetEmployeeDeductibleBenefits { get; set; }
    public virtual DbSet<TimeSheetUnionEmployerPaidBenefit> TimeSheetUnionEmployerPaidBenefits { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure these entities as query types to prevent EF Core from creating tables for them
        modelBuilder.Entity<TimeSheetExportLineItem>().ToView("TimeSheetExportLineItems");
        modelBuilder
            .Entity<TimeSheetEmployeeDeductibleBenefit>()
            .ToView("TimeSheetEmployeeDeductibleBenefits");
        modelBuilder
            .Entity<TimeSheetUnionEmployerPaidBenefit>()
            .ToView("TimeSheetUnionEmployerPaidBenefits");
        modelBuilder.Entity<Address>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Addresses_1");

            entity.ToTable("Addresses", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Addresses_ContactMechanisms");

            entity.HasIndex(e => e.DaddressTypeId, "IX_FK_Addresses_DAddressTypes");

            entity.HasIndex(e => e.DcountryId, "IX_FK_Addresses_DCountries");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.City).HasMaxLength(50);
            entity.Property(e => e.County).HasMaxLength(256);
            entity.Property(e => e.DaddressTypeId).HasColumnName("DAddressTypeID");
            entity
                .Property(e => e.DcountryId)
                .HasDefaultValueSql("('c273ce18-e0ba-49ed-9f34-82263bda4cde')")
                .HasColumnName("DCountryID");
            entity.Property(e => e.PostalCode).HasMaxLength(15);
            entity.Property(e => e.Province).HasMaxLength(50);

            entity
                .HasOne(d => d.DaddressType)
                .WithMany(p => p.Addresses)
                .HasForeignKey(d => d.DaddressTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Addresses_DAddressTypes");

            entity
                .HasOne(d => d.Dcountry)
                .WithMany(p => p.Addresses)
                .HasForeignKey(d => d.DcountryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Addresses_DCountries");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Address)
                .HasForeignKey<Address>(d => d.Id)
                .HasConstraintName("FK_Addresses_ContactMechanisms");
        });

        modelBuilder.Entity<Agreement>(entity =>
        {
            entity.ToTable("Agreements", "Core");

            entity.HasIndex(e => e.ChapterId, "IX_Agreements_ChapterID").HasFillFactor(80);

            entity
                .HasIndex(
                    e => new { e.EffectiveStartDate, e.EffectiveEndDate },
                    "IX_Agreements_EffectiveStartDateEffectiveEndDate"
                )
                .HasFillFactor(80);

            entity.HasIndex(e => e.Name, "IX_Agreements_Name");

            entity.HasIndex(e => e.ChapterId, "IX_ChapterID_Name");

            entity.HasIndex(e => e.ChapterId, "IX_ChapterID_Name_Dates_Covering");

            entity.HasIndex(e => e.ChapterId, "IX_FK_Agreements_Chapters");

            entity.HasIndex(e => e.DagreementTypeId, "IX_FK_Agreements_DAgreementTypes");

            entity.HasIndex(e => e.UnionContactId, "IX_FK_Agreements_Employees");

            entity.HasIndex(e => e.Id, "IX_FK_Agreements_Root");

            entity.HasIndex(e => e.UnionId, "IX_FK_Agreements_Unions");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.AllowCurrentMonthReporting)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.CertificationLanguage).IsUnicode(false);
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.DagreementTypeId).HasColumnName("DAgreementTypeID");
            entity.Property(e => e.EffectiveEndDate).HasColumnType("datetime");
            entity.Property(e => e.EffectiveStartDate).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(150);
            entity
                .Property(e => e.RequiresSignatoryStatus)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.UnionContactId).HasColumnName("UnionContactID");
            entity.Property(e => e.UnionId).HasColumnName("UnionID");

            entity
                .HasOne(d => d.Chapter)
                .WithMany(p => p.Agreements)
                .HasForeignKey(d => d.ChapterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Agreements_Chapters");

            entity
                .HasOne(d => d.DagreementType)
                .WithMany(p => p.Agreements)
                .HasForeignKey(d => d.DagreementTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Agreements_DAgreementTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Agreement)
                .HasForeignKey<Agreement>(d => d.Id)
                .HasConstraintName("FK_Agreements_Root");

            entity
                .HasOne(d => d.UnionContact)
                .WithMany(p => p.Agreements)
                .HasForeignKey(d => d.UnionContactId)
                .HasConstraintName("FK_Agreements_Employees");

            entity
                .HasOne(d => d.Union)
                .WithMany(p => p.Agreements)
                .HasForeignKey(d => d.UnionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Agreements_Unions");

            entity
                .HasMany(d => d.Organizations)
                .WithMany(p => p.Agreements)
                .UsingEntity<Dictionary<string, object>>(
                    "AuditAuthorization",
                    r =>
                        r.HasOne<Organization>()
                            .WithMany()
                            .HasForeignKey("OrganizationId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_AuditAuthorizations_Organizations"),
                    l =>
                        l.HasOne<Agreement>()
                            .WithMany()
                            .HasForeignKey("AgreementId")
                            .HasConstraintName("FK_AuditAuthorizations_Agreements"),
                    j =>
                    {
                        j.HasKey("AgreementId", "OrganizationId");
                        j.ToTable("AuditAuthorizations", "Core");
                        j.HasIndex(new[] { "AgreementId" }, "IX_FK_AuditAuthorizations_Agreements");
                        j.HasIndex(
                            new[] { "OrganizationId" },
                            "IX_FK_AuditAuthorizations_Organizations"
                        );
                        j.IndexerProperty<int>("AgreementId").HasColumnName("AgreementID");
                        j.IndexerProperty<int>("OrganizationId").HasColumnName("OrganizationID");
                    }
                );
        });

        modelBuilder.Entity<AgreementClassification>(entity =>
        {
            entity.HasNoKey().ToTable("AgreementClassifications", "Core");

            entity
                .HasIndex(
                    e => new
                    {
                        e.AgreementId,
                        e.ClassificationNameId,
                        e.SubClassificationId,
                    },
                    "IX_AgreementClassifications"
                )
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity.HasIndex(e => e.AgreementId, "IX_FK_AgreementClassifications_Agreements");

            entity.HasIndex(
                e => e.ClassificationNameId,
                "IX_FK_AgreementClassifications_ClassificationNames"
            );

            entity.HasIndex(
                e => e.SubClassificationId,
                "IX_FK_AgreementClassifications_SubClassifications"
            );

            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.ClassificationNameId).HasColumnName("ClassificationNameID");
            entity.Property(e => e.SubClassificationId).HasColumnName("SubClassificationID");

            entity
                .HasOne(d => d.Agreement)
                .WithMany()
                .HasForeignKey(d => d.AgreementId)
                .HasConstraintName("FK_AgreementClassifications_Agreements");

            entity
                .HasOne(d => d.ClassificationName)
                .WithMany()
                .HasForeignKey(d => d.ClassificationNameId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AgreementClassifications_ClassificationNames");

            entity
                .HasOne(d => d.SubClassification)
                .WithMany()
                .HasForeignKey(d => d.SubClassificationId)
                .HasConstraintName("FK_AgreementClassifications_SubClassifications");
        });

        modelBuilder.Entity<AgreementsToBenefit>(entity =>
        {
            entity.HasKey(e => new { e.AgreementId, e.BenefitId });

            entity.ToTable("AgreementsToBenefits", "Core");

            entity
                .HasIndex(
                    e => new { e.AgreementId, e.BenefitId },
                    "IX_AgreementsToBenefitsGetFAOrCAID"
                )
                .HasFillFactor(80);

            entity
                .HasIndex(e => e.CollectingAgentId, "IX_AgreementsToBenefits_CollectingAgent")
                .HasFillFactor(80);

            entity
                .HasIndex(e => e.FundAdministratorId, "IX_AgreementsToBenefits_FundAdministrator")
                .HasFillFactor(80);

            entity.HasIndex(e => e.AgreementId, "IX_FK_AgreementsToBenefits_Agreements");

            entity.HasIndex(e => e.BenefitId, "IX_FK_AgreementsToBenefits_Benefits");

            entity.HasIndex(e => e.CollectingAgentId, "IX_FK_AgreementsToBenefits_Organizations");

            entity.HasIndex(
                e => e.FundAdministratorId,
                "IX_FK_AgreementsToBenefits_Organizations1"
            );

            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.CollectingAgentId).HasColumnName("CollectingAgentID");
            entity.Property(e => e.FundAdministratorId).HasColumnName("FundAdministratorID");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.AgreementsToBenefits)
                .HasForeignKey(d => d.AgreementId)
                .HasConstraintName("FK_AgreementsToBenefits_Agreements");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.AgreementsToBenefits)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AgreementsToBenefits_Benefits");

            entity
                .HasOne(d => d.CollectingAgent)
                .WithMany(p => p.AgreementsToBenefitCollectingAgents)
                .HasForeignKey(d => d.CollectingAgentId)
                .HasConstraintName("FK_AgreementsToBenefits_Organizations");

            entity
                .HasOne(d => d.FundAdministrator)
                .WithMany(p => p.AgreementsToBenefitFundAdministrators)
                .HasForeignKey(d => d.FundAdministratorId)
                .HasConstraintName("FK_AgreementsToBenefits_Organizations1");
        });

        modelBuilder.Entity<AgreementsToContract>(entity =>
        {
            entity.HasKey(e => e.AgreementId);
            entity.ToTable("AgreementsToContracts", "Core");

            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.ContractId).HasColumnName("ContractID");

            entity
                .HasOne(d => d.Agreement)
                .WithMany()
                .HasForeignKey(d => d.AgreementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Agreement__Agree__264A745A");
        });

        modelBuilder.Entity<ApplicationConfig>(entity =>
        {
            entity.HasNoKey().ToTable("ApplicationConfig", "Core");

            entity
                .HasIndex(
                    e => new { e.ConfigCategory, e.ConfigKey },
                    "IX_ApplicationConfig_Category_Key"
                )
                .IsUnique();

            entity.Property(e => e.ConfigCategory).HasMaxLength(50).IsUnicode(false);
            entity.Property(e => e.ConfigKey).HasMaxLength(100).IsUnicode(false);
            entity.Property(e => e.ConfigValue).HasMaxLength(256).IsUnicode(false);
        });

        modelBuilder.Entity<AspnetApplication>(entity =>
        {
            entity
                .HasKey(e => e.ApplicationId)
                .HasName("PK__aspnet_Applicati__4D0CD9BB")
                .IsClustered(false);

            entity.ToTable("aspnet_Applications");

            entity
                .HasIndex(e => e.LoweredApplicationName, "UQ__aspnet_Applicati__4E00FDF4")
                .IsUnique();

            entity.HasIndex(e => e.ApplicationName, "UQ__aspnet_Applicati__4EF5222D").IsUnique();

            entity
                .HasIndex(e => e.LoweredApplicationName, "aspnet_Applications_Index")
                .IsClustered()
                .HasFillFactor(80);

            entity.Property(e => e.ApplicationId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ApplicationName).HasMaxLength(256);
            entity.Property(e => e.Description).HasMaxLength(256);
            entity.Property(e => e.LoweredApplicationName).HasMaxLength(256);
        });

        modelBuilder.Entity<AspnetMembership>(entity =>
        {
            entity
                .HasKey(e => e.UserId)
                .HasName("PK__aspnet_Membershi__6207F6A1")
                .IsClustered(false);

            entity.ToTable("aspnet_Membership");

            entity.HasIndex(e => e.ApplicationId, "IX_FK__aspnet_Me__Appli__62FC1ADA");

            entity.HasIndex(e => e.UserId, "IX_FK__aspnet_Me__UserI__63F03F13");

            entity
                .HasIndex(e => new { e.ApplicationId, e.LoweredEmail }, "aspnet_Membership_index")
                .IsClustered()
                .HasFillFactor(80);

            entity.Property(e => e.UserId).ValueGeneratedNever();
            entity.Property(e => e.Comment).HasColumnType("ntext");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(256);
            entity
                .Property(e => e.FailedPasswordAnswerAttemptWindowStart)
                .HasColumnType("datetime");
            entity.Property(e => e.FailedPasswordAttemptWindowStart).HasColumnType("datetime");
            entity.Property(e => e.LastLockoutDate).HasColumnType("datetime");
            entity.Property(e => e.LastLoginDate).HasColumnType("datetime");
            entity.Property(e => e.LastPasswordChangedDate).HasColumnType("datetime");
            entity.Property(e => e.LoweredEmail).HasMaxLength(256);
            entity.Property(e => e.MobilePin).HasMaxLength(16).HasColumnName("MobilePIN");
            entity.Property(e => e.PasswordAnswer).HasMaxLength(128);
            entity.Property(e => e.PasswordQuestion).HasMaxLength(256);
            entity.Property(e => e.PasswordSalt).HasMaxLength(128);

            entity
                .HasOne(d => d.Application)
                .WithMany(p => p.AspnetMemberships)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Me__Appli__62FC1ADA");

            entity
                .HasOne(d => d.User)
                .WithOne(p => p.AspnetMembership)
                .HasForeignKey<AspnetMembership>(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Me__UserI__63F03F13");
        });

        modelBuilder.Entity<AspnetPath>(entity =>
        {
            entity.HasKey(e => e.PathId).HasName("PK__aspnet_Paths__139F5235").IsClustered(false);

            entity.ToTable("aspnet_Paths");

            entity.HasIndex(e => e.ApplicationId, "IX_FK__aspnet_Pa__Appli__1493766E");

            entity
                .HasIndex(e => new { e.ApplicationId, e.LoweredPath }, "aspnet_Paths_index")
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity.Property(e => e.PathId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LoweredPath).HasMaxLength(256);
            entity.Property(e => e.Path).HasMaxLength(256);

            entity
                .HasOne(d => d.Application)
                .WithMany(p => p.AspnetPaths)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Pa__Appli__1493766E");
        });

        modelBuilder.Entity<AspnetPersonalizationAllUser>(entity =>
        {
            entity.HasKey(e => e.PathId).HasName("PK__aspnet_Personali__19582B8B");

            entity.ToTable("aspnet_PersonalizationAllUsers");

            entity.HasIndex(e => e.PathId, "IX_FK__aspnet_Pe__PathI__1A4C4FC4");

            entity.Property(e => e.PathId).ValueGeneratedNever();
            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
            entity.Property(e => e.PageSettings).HasColumnType("image");

            entity
                .HasOne(d => d.Path)
                .WithOne(p => p.AspnetPersonalizationAllUser)
                .HasForeignKey<AspnetPersonalizationAllUser>(d => d.PathId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Pe__PathI__1A4C4FC4");
        });

        modelBuilder.Entity<AspnetPersonalizationPerUser>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__aspnet_Personali__1C349836").IsClustered(false);

            entity.ToTable("aspnet_PersonalizationPerUser");

            entity.HasIndex(e => e.PathId, "IX_FK__aspnet_Pe__PathI__1E1CE0A8");

            entity.HasIndex(e => e.UserId, "IX_FK__aspnet_Pe__UserI__1F1104E1");

            entity
                .HasIndex(e => new { e.PathId, e.UserId }, "aspnet_PersonalizationPerUser_index1")
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity
                .HasIndex(e => new { e.UserId, e.PathId }, "aspnet_PersonalizationPerUser_ncindex2")
                .IsUnique()
                .HasFillFactor(80);

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
            entity.Property(e => e.PageSettings).HasColumnType("image");

            entity
                .HasOne(d => d.Path)
                .WithMany(p => p.AspnetPersonalizationPerUsers)
                .HasForeignKey(d => d.PathId)
                .HasConstraintName("FK__aspnet_Pe__PathI__1E1CE0A8");

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.AspnetPersonalizationPerUsers)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK__aspnet_Pe__UserI__1F1104E1");
        });

        modelBuilder.Entity<AspnetProfile>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK__aspnet_Profile__77031387");

            entity.ToTable("aspnet_Profile");

            entity.HasIndex(e => e.UserId, "IX_FK__aspnet_Pr__UserI__77F737C0");

            entity.Property(e => e.UserId).ValueGeneratedNever();
            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
            entity.Property(e => e.PropertyNames).HasColumnType("ntext");
            entity.Property(e => e.PropertyValuesBinary).HasColumnType("image");
            entity.Property(e => e.PropertyValuesString).HasColumnType("ntext");

            entity
                .HasOne(d => d.User)
                .WithOne(p => p.AspnetProfile)
                .HasForeignKey<AspnetProfile>(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Pr__UserI__77F737C0");
        });

        modelBuilder.Entity<AspnetRole>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__aspnet_Roles__008C7DC1").IsClustered(false);

            entity.ToTable("aspnet_Roles");

            entity
                .HasIndex(e => new { e.ApplicationId, e.LoweredRoleName }, "aspnet_Roles_index1")
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity.Property(e => e.RoleId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Description).HasMaxLength(256);
            entity.Property(e => e.LoweredRoleName).HasMaxLength(256);
            entity.Property(e => e.RoleName).HasMaxLength(256);

            entity
                .HasOne(d => d.Application)
                .WithMany(p => p.AspnetRoles)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Ro__Appli__0180A1FA");
        });

        modelBuilder.Entity<AspnetSchemaVersion>(entity =>
        {
            entity
                .HasKey(e => new { e.Feature, e.CompatibleSchemaVersion })
                .HasName("PK__aspnet_SchemaVer__578A682E");

            entity.ToTable("aspnet_SchemaVersions");

            entity.Property(e => e.Feature).HasMaxLength(128);
            entity.Property(e => e.CompatibleSchemaVersion).HasMaxLength(128);
        });

        modelBuilder.Entity<AspnetUser>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK__aspnet_Users__51D18ED8").IsClustered(false);

            entity.ToTable("aspnet_Users");

            entity.HasIndex(e => e.ApplicationId, "IX_FK__aspnet_Us__Appli__52C5B311");

            entity
                .HasIndex(e => new { e.ApplicationId, e.LoweredUserName }, "aspnet_Users_Index")
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity
                .HasIndex(e => new { e.ApplicationId, e.LastActivityDate }, "aspnet_Users_Index2")
                .HasFillFactor(80);

            entity.Property(e => e.UserId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LastActivityDate).HasColumnType("datetime");
            entity.Property(e => e.LoweredUserName).HasMaxLength(256);
            entity.Property(e => e.MobileAlias).HasMaxLength(16);
            entity.Property(e => e.UserName).HasMaxLength(256);

            entity
                .HasOne(d => d.Application)
                .WithMany(p => p.AspnetUsers)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__aspnet_Us__Appli__52C5B311");

            entity
                .HasMany(d => d.Roles)
                .WithMany(p => p.Users)
                .UsingEntity<Dictionary<string, object>>(
                    "AspnetUsersInRole",
                    r =>
                        r.HasOne<AspnetRole>()
                            .WithMany()
                            .HasForeignKey("RoleId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK__aspnet_Us__RoleI__06455717"),
                    l =>
                        l.HasOne<AspnetUser>()
                            .WithMany()
                            .HasForeignKey("UserId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK__aspnet_Us__UserI__055132DE"),
                    j =>
                    {
                        j.HasKey("UserId", "RoleId").HasName("PK__aspnet_UsersInRo__045D0EA5");
                        j.ToTable("aspnet_UsersInRoles");
                        j.HasIndex(new[] { "UserId" }, "IX_FK__aspnet_Us__UserI__055132DE");
                        j.HasIndex(new[] { "RoleId" }, "aspnet_UsersInRoles_index")
                            .HasFillFactor(80);
                    }
                );
        });

        modelBuilder.Entity<AspnetWebEventEvent>(entity =>
        {
            entity.HasKey(e => e.EventId).HasName("PK__aspnet_WebEvent___2E534871");

            entity.ToTable("aspnet_WebEvent_Events");

            entity.Property(e => e.EventId).HasMaxLength(32).IsUnicode(false).IsFixedLength();
            entity.Property(e => e.ApplicationPath).HasMaxLength(256);
            entity.Property(e => e.ApplicationVirtualPath).HasMaxLength(256);
            entity.Property(e => e.Details).HasColumnType("ntext");
            entity.Property(e => e.EventOccurrence).HasColumnType("decimal(19, 0)");
            entity.Property(e => e.EventSequence).HasColumnType("decimal(19, 0)");
            entity.Property(e => e.EventTime).HasColumnType("datetime");
            entity.Property(e => e.EventTimeUtc).HasColumnType("datetime");
            entity.Property(e => e.EventType).HasMaxLength(256);
            entity.Property(e => e.ExceptionType).HasMaxLength(256);
            entity.Property(e => e.MachineName).HasMaxLength(256);
            entity.Property(e => e.Message).HasMaxLength(1024);
            entity.Property(e => e.RequestUrl).HasMaxLength(1024);
        });

        modelBuilder.Entity<AspstateTempApplication>(entity =>
        {
            entity.HasKey(e => e.AppId).HasName("PK__ASPState__8E2CF7F9FAD27830");

            entity.ToTable("ASPStateTempApplications");

            entity.HasIndex(e => e.AppName, "Index_AppName");

            entity.Property(e => e.AppId).ValueGeneratedNever();
            entity.Property(e => e.AppName).HasMaxLength(280).IsUnicode(false).IsFixedLength();
        });

        modelBuilder.Entity<AspstateTempSession>(entity =>
        {
            entity.HasKey(e => e.SessionId).HasName("PK__ASPState__C9F4929071302149");

            entity.ToTable("ASPStateTempSessions");

            entity.HasIndex(e => e.Expires, "Index_Expires");

            entity.Property(e => e.SessionId).HasMaxLength(88);
            entity
                .Property(e => e.Created)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Expires).HasColumnType("datetime");
            entity.Property(e => e.LockDate).HasColumnType("datetime");
            entity.Property(e => e.LockDateLocal).HasColumnType("datetime");
            entity.Property(e => e.SessionItemLong).HasColumnType("image");
            entity.Property(e => e.SessionItemShort).HasMaxLength(7000);
        });

        modelBuilder.Entity<Benefit>(entity =>
        {
            entity.ToTable("Benefits", "Core");

            entity.HasIndex(e => e.Id, "IX_Benefits").HasFillFactor(80);

            entity.HasIndex(e => e.ChapterId, "IX_FK_Benefits_Chapters");

            entity.HasIndex(e => e.Id, "IX_FK_Benefits_Root");

            entity.HasIndex(e => new { e.ChapterId, e.Name }, "IX_Name_ChapterID");

            entity
                .Property(e => e.Id)
                .ValueGeneratedNever()
                .HasComment("BenefitID identifies Benefits")
                .HasColumnName("ID");
            entity
                .Property(e => e.ChapterAdministratorOnly)
                .HasComment(
                    "Determines if a benefit can only be toggled by a chapter administrator type role. Was AdminOnly = 0x1000"
                );
            entity
                .Property(e => e.ChapterId)
                .HasComment("If null, then this benefit is for all chapters.")
                .HasColumnName("ChapterID");
            entity
                .Property(e => e.DisplayOnFundingOnly)
                .HasComment(
                    "Determines if this benefit is used by a collecting agent to manage internal \"accounts\" such as overages. Was FundingOnly = 1024."
                );
            entity
                .Property(e => e.DstatusId)
                .HasDefaultValueSql("((1))")
                .HasComment("Determines if the benefit is available for new agreements.")
                .HasColumnName("DStatusID");
            entity
                .Property(e => e.EmployeeElectionOverridable)
                .HasComment(
                    "Determines if the benefit is picked at an employee level (it shows up on the employee details page). Was EmployeeOption = 0x20"
                );
            entity
                .Property(e => e.EmployeeRateOverridable)
                .HasComment("Determines if the employee can have a custom rate.");
            entity
                .Property(e => e.EmployerElectionOverridable)
                .HasComment(
                    "Determines if the benefit is also shown on supplemental reports. Was AvailableOnSupplementalReports = 0x40."
                );
            entity
                .Property(e => e.InformationalOnly)
                .HasComment(
                    "Determines if the benefit is for reporting purposes only and is excluded from calculations and funding. New."
                );
            entity.Property(e => e.Name).HasMaxLength(50).HasComment("The name of the benefit");
            entity.Property(e => e.OncePerEe).HasColumnName("OncePerEE");
            entity
                .Property(e => e.Selectable)
                .IsRequired()
                .HasDefaultValueSql("((1))")
                .HasComment(
                    "Determines if the benefit can be manually selected for inclusion into an agreement."
                );

            entity
                .HasOne(d => d.Chapter)
                .WithMany(p => p.Benefits)
                .HasForeignKey(d => d.ChapterId)
                .HasConstraintName("FK_Benefits_Chapters");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Benefit)
                .HasForeignKey<Benefit>(d => d.Id)
                .HasConstraintName("FK_Benefits_Root");
        });

        modelBuilder.Entity<BenefitOverride>(entity =>
        {
            entity.HasNoKey().ToTable("BenefitOverrides", "Core");

            entity.HasIndex(
                e => new { e.BenefitId, e.ChapterId },
                "IX_BenefitOverrides_BenefitID_ChapterID_Covering"
            );

            entity.HasIndex(e => e.ChapterId, "IX_BenefitOverrides_ChapterID_Covering");

            entity.HasIndex(e => e.PartyId, "IX_FK_BenefitOverrides_Parties");

            entity.HasIndex(e => e.Id, "IX_FK_BenefitOverrides_Root");

            entity.HasIndex(
                e => new
                {
                    e.PartyId,
                    e.BenefitId,
                    e.ChapterId,
                },
                "IX_Party_Benefit_Chapter"
            );

            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.PartyId).HasColumnName("PartyID");
            entity.Property(e => e.Value).HasColumnType("decimal(28, 10)");
        });

        modelBuilder.Entity<BenefitOverridesOrig>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_BenefitOverrides");

            entity.ToTable("BenefitOverridesOrig", "Core");

            entity.HasIndex(
                e => new { e.BenefitId, e.ChapterId },
                "IX_BenefitOverrides_BenefitID_ChapterID_Covering"
            );

            entity.HasIndex(e => e.ChapterId, "IX_BenefitOverrides_ChapterID_Covering");

            entity.HasIndex(e => e.PartyId, "IX_FK_BenefitOverrides_Parties");

            entity.HasIndex(e => e.Id, "IX_FK_BenefitOverrides_Root");

            entity.HasIndex(
                e => new
                {
                    e.PartyId,
                    e.BenefitId,
                    e.ChapterId,
                },
                "IX_Party_Benefit_Chapter"
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.PartyId).HasColumnName("PartyID");
            entity.Property(e => e.Value).HasColumnType("decimal(18, 4)");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.BenefitOverridesOrig)
                .HasForeignKey<BenefitOverridesOrig>(d => d.Id)
                .HasConstraintName("FK_BenefitOverrides_Root");

            entity
                .HasOne(d => d.Party)
                .WithMany(p => p.BenefitOverridesOrigs)
                .HasForeignKey(d => d.PartyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BenefitOverrides_Parties");
        });

        modelBuilder.Entity<Chapter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Carriers_1");

            entity.ToTable("Chapters", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Carriers_Organizations");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.EmployeeAssociationId)
                .HasMaxLength(50)
                .HasColumnName("EmployeeAssociationID");
            entity
                .Property(e => e.EmployerAssociationId)
                .HasMaxLength(50)
                .HasColumnName("EmployerAssociationID");
            entity
                .Property(e => e.Limited)
                .HasComment(
                    "Indicates that this chapter has access to a reduced functionality set."
                );

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Chapter)
                .HasForeignKey<Chapter>(d => d.Id)
                .HasConstraintName("FK_Carriers_Organizations");
        });

        modelBuilder.Entity<ChapterToEmployerRelationship>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ChapterToEmployerRelationships_1");

            entity.ToTable("ChapterToEmployerRelationships", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_ChapterToEmployerRelationships_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.AssociationId).HasMaxLength(50).HasColumnName("AssociationID");
            entity.Property(e => e.DatePriviledgedAuthorized).HasColumnType("datetime");

            entity
                .HasOne(d => d.Relationship)
                .WithOne(p => p.ChapterToEmployerRelationship)
                .HasForeignKey<ChapterToEmployerRelationship>(d => d.Id)
                .HasConstraintName("FK_ChapterToEmployerRelationships_Root");
        });

        modelBuilder.Entity<ChaptersTradeAdministrator>(entity =>
        {
            entity.HasKey(e => e.Guid).HasName("PK__ChaptersTradeAdm__60221034");

            entity.ToTable("ChaptersTradeAdministrator", "Core");

            entity.Property(e => e.Guid).ValueGeneratedNever().HasColumnName("GUID");
        });

        modelBuilder.Entity<ClassificationName>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_JobClassificationAliases");

            entity.ToTable("ClassificationNames", "Core");

            entity.HasIndex(e => new { e.ChapterId, e.Name }, "IX_ChapterID_Name");

            entity.HasIndex(e => e.ChapterId, "IX_FK_JobClassificationAliases_Chapters");

            entity.HasIndex(
                e => e.DclassificationCodeId,
                "IX_FK_JobClassificationAliases_DJobClassifications"
            );

            entity.HasIndex(e => e.DstatusId, "IX_FK_JobClassificationAliases_DStatuses");

            entity.HasIndex(e => e.Id, "IX_FK_JobClassificationAliases_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.DclassificationCodeId).HasColumnName("DClassificationCodeID");
            entity.Property(e => e.DstatusId).HasColumnName("DStatusID");
            entity.Property(e => e.Name).HasMaxLength(100);

            entity
                .HasOne(d => d.Chapter)
                .WithMany(p => p.ClassificationNames)
                .HasForeignKey(d => d.ChapterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobClassificationAliases_Chapters");

            entity
                .HasOne(d => d.DclassificationCode)
                .WithMany(p => p.ClassificationNames)
                .HasForeignKey(d => d.DclassificationCodeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobClassificationAliases_DJobClassifications");

            entity
                .HasOne(d => d.Dstatus)
                .WithMany(p => p.ClassificationNames)
                .HasForeignKey(d => d.DstatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobClassificationAliases_DStatuses");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ClassificationName)
                .HasForeignKey<ClassificationName>(d => d.Id)
                .HasConstraintName("FK_JobClassificationAliases_Root");
        });

        modelBuilder.Entity<ClassificationXref>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Core.ClassificationXref");

            entity.ToTable("ClassificationXref", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ClassificationNameId).HasColumnName("ClassificationNameID");
            entity.Property(e => e.FundAdminId).HasColumnName("FundAdminID");
            entity.Property(e => e.SubClassificationId).HasColumnName("SubClassificationID");
            entity.Property(e => e.UnionId).HasColumnName("UnionID");
            entity.Property(e => e.Xrefvalue).HasMaxLength(255);
        });

        modelBuilder.Entity<ContactMechanism>(entity =>
        {
            entity.ToTable("ContactMechanisms", "Core");

            entity.HasIndex(
                e => e.DcontactMechanismTypeId,
                "IX_FK_ContactMechanisms_DContactMechanismTypes"
            );

            entity.HasIndex(e => e.Id, "IX_FK_ContactMechanisms_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.DcontactMechanismTypeId)
                .HasColumnName("DContactMechanismTypeID");

            entity
                .HasOne(d => d.DcontactMechanismType)
                .WithMany(p => p.ContactMechanisms)
                .HasForeignKey(d => d.DcontactMechanismTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ContactMechanisms_DContactMechanismTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ContactMechanism)
                .HasForeignKey<ContactMechanism>(d => d.Id)
                .HasConstraintName("FK_ContactMechanisms_Root");
        });

        modelBuilder.Entity<Contract>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Contract__3214EC278A3CF31F");

            entity.ToTable("Contracts", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AmfbenefitId).HasColumnName("AMFBenefitID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.NecabenefitId).HasColumnName("NECABenefitID");
        });

        modelBuilder.Entity<Control>(entity =>
        {
            entity.HasKey(e => e.Ssn);

            entity.ToTable("Control");

            entity.Property(e => e.Ssn).HasMaxLength(9).HasColumnName("SSN");
        });

        modelBuilder.Entity<CostCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CostCode__3214EC27CB2926EA");

            entity.ToTable("CostCodes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.EmployerId).HasColumnName("EmployerID");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.CostCodes)
                .HasForeignKey(d => d.AgreementId)
                .HasConstraintName("FK__CostCodes__Agree__6A008029");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.CostCodes)
                .HasForeignKey(d => d.BenefitId)
                .HasConstraintName("FK__CostCodes__Benef__6AF4A462");

            entity
                .HasOne(d => d.Employer)
                .WithMany(p => p.CostCodes)
                .HasForeignKey(d => d.EmployerId)
                .HasConstraintName("FK__CostCodes__Emplo__690C5BF0");
        });

        modelBuilder.Entity<CreateRecurringPaymentProfileResponse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_PayPal.CreateRecurringPaymentProfileResponse");

            entity.ToTable("CreateRecurringPaymentProfileResponse", "PayPal");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Ack).HasMaxLength(256).HasColumnName("ACK");
            entity.Property(e => e.Build).HasMaxLength(50).HasColumnName("BUILD");
            entity.Property(e => e.Correlationid).HasMaxLength(128).HasColumnName("CORRELATIONID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Errorcode).HasColumnName("ERRORCODE");
            entity.Property(e => e.Longmessage).HasColumnName("LONGMESSAGE");
            entity.Property(e => e.OrderDetailId).HasColumnName("OrderDetailID");
            entity.Property(e => e.Profileid).HasMaxLength(14).HasColumnName("PROFILEID");
            entity.Property(e => e.Profilestatus).HasMaxLength(50).HasColumnName("PROFILESTATUS");
            entity.Property(e => e.SessionId).HasMaxLength(256).HasColumnName("SessionID");
            entity.Property(e => e.Severritycode).HasColumnName("SEVERRITYCODE");
            entity.Property(e => e.Shortmessage).HasColumnName("SHORTMESSAGE");
            entity.Property(e => e.Token).HasMaxLength(20).HasColumnName("TOKEN");
            entity.Property(e => e.Version).HasMaxLength(25).HasColumnName("VERSION");
        });

        modelBuilder.Entity<CreditCardPaymentMethod>(entity =>
        {
            entity.HasKey(e => e.AssociatedGuid);

            entity.ToTable("CreditCardPaymentMethods", "Core");

            entity.HasIndex(e => e.AssociatedGuid, "IX_FK_CreditCardPaymentMethods_PaymentMethods");

            entity
                .Property(e => e.AssociatedGuid)
                .ValueGeneratedNever()
                .HasColumnName("AssociatedGUID");
            entity.Property(e => e.Expiration).HasColumnType("datetime");

            entity
                .HasOne(d => d.Associated)
                .WithOne(p => p.CreditCardPaymentMethod)
                .HasForeignKey<CreditCardPaymentMethod>(d => d.AssociatedGuid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CreditCardPaymentMethods_PaymentMethods");
        });

        modelBuilder.Entity<CustomReport>(entity =>
        {
            entity.ToTable("CustomReports", "Custom");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity
                .Property(e => e.NewApp)
                .IsRequired()
                .HasDefaultValueSql("(CONVERT([bit],(0),0))");
            entity.Property(e => e.ReportTypeId).HasColumnName("ReportTypeID");
        });

        modelBuilder.Entity<CustomReportPivot>(entity =>
        {
            entity.HasKey(e => e.ReportId);

            entity.ToTable("CustomReportPivots", "Custom");

            entity.Property(e => e.ReportId).ValueGeneratedNever().HasColumnName("ReportID");

            entity
                .HasOne(d => d.Report)
                .WithOne(p => p.CustomReportPivot)
                .HasForeignKey<CustomReportPivot>(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CustomReportPivots_CustomReports");
        });

        modelBuilder.Entity<CustomReportProduct>(entity =>
        {
            entity.ToTable(
                "CustomReportProduct",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_CustomReportProduct_LastModifiedDate")
            );

            entity
                .HasIndex(e => new { e.CustomReportId, e.ProductId }, "Unique_CustomReportProduct")
                .IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CustomReportId).HasColumnName("CustomReportID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity
                .HasOne(d => d.CustomReport)
                .WithMany(p => p.CustomReportProducts)
                .HasForeignKey(d => d.CustomReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CustomReportProduct_CustomReport");

            entity
                .HasOne(d => d.Product)
                .WithMany(p => p.CustomReportProducts)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CustomReportProduct_Product");
        });

        modelBuilder.Entity<CustomReportSubtotal>(entity =>
        {
            entity.HasNoKey().ToTable("CustomReportSubtotals", "Custom");

            entity.Property(e => e.ReportId).HasColumnName("ReportID");

            entity
                .HasOne(d => d.Report)
                .WithMany()
                .HasForeignKey(d => d.ReportId)
                .HasConstraintName("FK_CustomReportSubtotals_CustomReports");
        });

        modelBuilder.Entity<DaddressType>(entity =>
        {
            entity.ToTable("DAddressTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DagreementType>(entity =>
        {
            entity.ToTable("DAgreementTypes", "Core");

            entity
                .HasIndex(e => new { e.Id, e.Name }, "IX_DAgreementTypes")
                .IsUnique()
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.Name)
                .HasMaxLength(50)
                .HasComment("Changing the names of the agreement types can affect code.");
        });

        modelBuilder.Entity<DamendmentAction>(entity =>
        {
            entity.ToTable("DAmendmentActions", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DatabaseMigration>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Database__3214EC278CE884C9");

            entity.ToTable(tb =>
                tb.HasComment(
                    "This is an ad-hoc database versioning scheme to support the migrations found in EPR Database/Migrations. It should be removed when we have a proper migration solution."
                )
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.MigrationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<DatabaseSpace>(entity =>
        {
            entity.ToTable("DatabaseSpace");

            entity.Property(e => e.DatabaseSpaceId).HasColumnName("DatabaseSpaceID");
            entity.Property(e => e.DatabaseName).HasMaxLength(100).IsUnicode(false);
            entity.Property(e => e.FileSizeMb).HasColumnName("FileSizeMB");
            entity.Property(e => e.FreeSpaceMb).HasColumnName("FreeSpaceMB");
            entity.Property(e => e.FreeSpacePct).HasMaxLength(7).IsUnicode(false);
            entity.Property(e => e.LogicalFileName).HasMaxLength(128);
            entity.Property(e => e.PhysicalFileName).HasMaxLength(520);
            entity.Property(e => e.PollDate).HasColumnType("datetime");
            entity.Property(e => e.RecoveryMode).HasMaxLength(128);
            entity.Property(e => e.ServerName).HasMaxLength(100).IsUnicode(false);
            entity.Property(e => e.Status).HasMaxLength(128);
            entity.Property(e => e.Updateability).HasMaxLength(128);
        });

        modelBuilder.Entity<DcalculationMethod>(entity =>
        {
            entity.ToTable("DCalculationMethods", "Core");

            entity
                .Property(e => e.Id)
                .ValueGeneratedNever()
                .HasComment("Primary Key")
                .HasColumnName("ID");
            entity
                .Property(e => e.FormatString)
                .HasMaxLength(50)
                .HasComment(
                    "a formatting string suitable for String.Format used when displaying the string."
                );
            entity
                .Property(e => e.Name)
                .HasMaxLength(50)
                .HasComment("A human-consummable description of the method");
            entity
                .Property(e => e.Uisymbol)
                .HasMaxLength(1)
                .IsFixedLength()
                .HasColumnName("UISymbol");
        });

        modelBuilder.Entity<DcalculationModifier>(entity =>
        {
            entity.ToTable("DCalculationModifiers", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<Dcategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Category");

            entity.ToTable(
                "DCategory",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_DCategory_LastModifiedDate")
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Active).IsRequired().HasDefaultValueSql("((1))");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Keywords).HasMaxLength(512);
            entity.Property(e => e.LargeImagePath).HasMaxLength(256);
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.MediumImagePath).HasMaxLength(256);
            entity.Property(e => e.Name).HasMaxLength(256);
            entity.Property(e => e.SmallImagePath).HasMaxLength(256);
        });

        modelBuilder.Entity<DclassificationCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DJobClassifications");

            entity.ToTable("DClassificationCodes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Category).HasMaxLength(100);
            entity
                .Property(e => e.ClassName)
                .HasMaxLength(100)
                .HasComment("the \"full\" classification name");
        });

        modelBuilder.Entity<DcontactMechanismType>(entity =>
        {
            entity.ToTable("DContactMechanismTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<Dcountry>(entity =>
        {
            entity.ToTable("DCountries", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(250);
            entity.Property(e => e.NumericCode).HasMaxLength(3).IsFixedLength();
            entity.Property(e => e.ThreeLetterCode).HasMaxLength(3).IsFixedLength();
            entity.Property(e => e.TwoLetterCode).HasMaxLength(2).IsFixedLength();
        });

        modelBuilder.Entity<DcustomReportType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DCustomReportType");

            entity.ToTable("DCustomReportTypes", "Custom");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DelectronicPaymentOption>(entity =>
        {
            entity.ToTable("DElectronicPaymentOptions", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DelinquencyLetter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DL");

            entity.ToTable("DelinquencyLetters", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Cclist).HasColumnName("CCList");
            entity.Property(e => e.OrgId).HasColumnName("OrgID");
            entity.Property(e => e.TypeId).HasColumnName("TypeID");
        });

        modelBuilder.Entity<DemailAddressType>(entity =>
        {
            entity.ToTable("DEmailAddressTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<DentityType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DEntities");

            entity.ToTable(
                "DEntityTypes",
                "Core",
                tb =>
                    tb.HasComment(
                        "Identifies the underlying type of entity represented by this record. These values are mirrored, as applicable, in DOrganizationTypes and DPersonTypes."
                    )
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DeventSubType>(entity =>
        {
            entity.ToTable("DEventSubTypes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DeventType>(entity =>
        {
            entity.ToTable("DEventTypes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<Dgender>(entity =>
        {
            entity.ToTable("DGenders", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DguidSubstitutionType>(entity =>
        {
            entity.HasKey(e => e.SubstitutionTypeId).HasName("PK__DGuidSubstitutio__620A58A6");

            entity.ToTable("DGuidSubstitutionType", "Core");

            entity.Property(e => e.SubstitutionTypeId).ValueGeneratedNever();
            entity.Property(e => e.Description).HasMaxLength(100);
        });

        modelBuilder.Entity<DoExpressCheckoutPaymentResponse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_PayPal.DoExpressCheckoutPaymentResponse");

            entity.ToTable("DoExpressCheckoutPaymentResponse", "PayPal");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Ack).HasMaxLength(256).HasColumnName("ACK");
            entity.Property(e => e.Build).HasMaxLength(50).HasColumnName("BUILD");
            entity.Property(e => e.Correlationid).HasMaxLength(128).HasColumnName("CORRELATIONID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Errorcode).HasColumnName("ERRORCODE");
            entity.Property(e => e.Longmessage).HasColumnName("LONGMESSAGE");
            entity.Property(e => e.OrderId).HasColumnName("OrderID");
            entity.Property(e => e.Paymenrequest0Ack).HasColumnName("PAYMENREQUEST_0_ACK");
            entity
                .Property(e => e.Paymenrequest0Errorcode)
                .HasColumnName("PAYMENREQUEST_0_ERRORCODE");
            entity
                .Property(e => e.Paymenrequest0Longmessage)
                .HasColumnName("PAYMENREQUEST_0_LONGMESSAGE");
            entity
                .Property(e => e.Paymenrequest0Severitycode)
                .HasColumnName("PAYMENREQUEST_0_SEVERITYCODE");
            entity.Property(e => e.Paymentinfo0Ack).HasColumnName("PAYMENTINFO_0_ACK");
            entity
                .Property(e => e.Paymentinfo0Amt)
                .HasColumnType("decimal(6, 2)")
                .HasColumnName("PAYMENTINFO_0_AMT");
            entity
                .Property(e => e.Paymentinfo0Currencycode)
                .HasMaxLength(128)
                .HasColumnName("PAYMENTINFO_0_CURRENCYCODE");
            entity.Property(e => e.Paymentinfo0Errorcode).HasColumnName("PAYMENTINFO_0_ERRORCODE");
            entity
                .Property(e => e.Paymentinfo0Feeamt)
                .HasColumnType("decimal(6, 2)")
                .HasColumnName("PAYMENTINFO_0_FEEAMT");
            entity
                .Property(e => e.Paymentinfo0Ordertime)
                .HasColumnType("datetime")
                .HasColumnName("PAYMENTINFO_0_ORDERTIME");
            entity
                .Property(e => e.Paymentinfo0Paymentstatus)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_PAYMENTSTATUS");
            entity
                .Property(e => e.Paymentinfo0Paymenttype)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_PAYMENTTYPE");
            entity
                .Property(e => e.Paymentinfo0Pendingreason)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_PENDINGREASON");
            entity
                .Property(e => e.Paymentinfo0Reasoncode)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_REASONCODE");
            entity
                .Property(e => e.Paymentinfo0Securemerchantaccountid)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_SECUREMERCHANTACCOUNTID");
            entity
                .Property(e => e.Paymentinfo0Taxamt)
                .HasColumnType("decimal(6, 2)")
                .HasColumnName("PAYMENTINFO_0_TAXAMT");
            entity
                .Property(e => e.Paymentinfo0Transactionid)
                .HasMaxLength(50)
                .HasColumnName("PAYMENTINFO_0_TRANSACTIONID");
            entity
                .Property(e => e.Paymentinfo0Transactiontype)
                .HasMaxLength(15)
                .HasColumnName("PAYMENTINFO_0_TRANSACTIONTYPE");
            entity
                .Property(e => e.Paymentrequest0Shortmessage)
                .HasColumnName("PAYMENTREQUEST_0_SHORTMESSAGE");
            entity.Property(e => e.SessionId).HasMaxLength(256).HasColumnName("SessionID");
            entity.Property(e => e.Severritycode).HasColumnName("SEVERRITYCODE");
            entity.Property(e => e.Shortmessage).HasColumnName("SHORTMESSAGE");
            entity.Property(e => e.Token).HasMaxLength(20).HasColumnName("TOKEN");
            entity.Property(e => e.Version).HasMaxLength(25).HasColumnName("VERSION");
        });

        modelBuilder.Entity<DorderStatus>(entity =>
        {
            entity.ToTable("DOrderStatus", "ShoppingCart");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DorganizationType>(entity =>
        {
            entity.ToTable(
                "DOrganizationTypes",
                "Core",
                tb =>
                    tb.HasComment(
                        "Identifies the underlying type of entity represented by this record. These values mirror, as applicable, the values in DEntityTypes."
                    )
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DpartyType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_dEntityTypes");

            entity.ToTable("DPartyTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<DpaymentMethodType>(entity =>
        {
            entity.ToTable("DPaymentMethodTypes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DpaymentType>(entity =>
        {
            entity.ToTable("DPaymentTypes", "ShoppingCart");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DpersonType>(entity =>
        {
            entity.ToTable(
                "DPersonTypes",
                "Core",
                tb =>
                    tb.HasComment(
                        "Identifies the underlying type of entity represented by this record. These values mirror, as applicable, the values in DEntityTypes."
                    )
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DphoneNumberType>(entity =>
        {
            entity.ToTable("DPhoneNumberTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<Dprovince>(entity =>
        {
            entity.ToTable("DProvinces", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Abbrieviation).HasMaxLength(2).IsFixedLength();
            entity.Property(e => e.DcountryId).HasColumnName("DCountryID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DrelationshipStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DStatuses");

            entity.ToTable("DRelationshipStatuses", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<DrelationshipSubType>(entity =>
        {
            entity
                .HasKey(e => new { e.Id, e.DrelationshipTypeId })
                .HasName("PK_DEmploymentStatuses");

            entity.ToTable("DRelationshipSubTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.DrelationshipTypeId).HasColumnName("DRelationshipTypeID");
        });

        modelBuilder.Entity<DrelationshipType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__tdPartyRelations__683FF2E4");

            entity.ToTable("DRelationshipTypes", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.IsOneToOne)
                .IsRequired()
                .HasDefaultValueSql("((1))")
                .HasComment(
                    "Most relationships between parties are a one-to-many (e.g., Employer to Employees). Once this type of relationship is created, it is never updated--only its status. \r\n\r\nHowever, parties can have multiple relationships between them, and some of these are a one-to-one type of relationship that can be changed over time without needing to be temporally aware (e.g., the primary contact for an employer).\r\n\r\nWhen retrieving employees for an entity, only the one-to-many relationships should be used, otherwise employees with a second relationship would be duplicated."
                );
            entity.Property(e => e.LeftDentityTypeId).HasColumnName("LeftDEntityTypeID");
            entity.Property(e => e.RightDentityTypeId).HasColumnName("RightDEntityTypeID");
        });

        modelBuilder.Entity<DreportStatus>(entity =>
        {
            entity.ToTable("DReportStatuses", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<DreportSubscriptionType>(entity =>
        {
            entity.ToTable("DReportSubscriptionTypes", "Custom");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DsettingsType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__DSetting__3214EC273357A0F0");
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.ToTable("DSettingsTypes", "Core");
        });

        modelBuilder.Entity<Dstatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_DStatuses_1");

            entity.ToTable("DStatuses", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<DtaMv0>(entity =>
        {
            entity.HasNoKey().ToView("_dta_mv_0", "Core");

            entity.Property(e => e.Col1).HasColumnName("_col_1");
            entity.Property(e => e.Col10).HasColumnType("datetime").HasColumnName("_col_10");
            entity.Property(e => e.Col11).HasColumnName("_col_11");
            entity.Property(e => e.Col2).HasColumnName("_col_2");
            entity.Property(e => e.Col3).HasColumnName("_col_3");
            entity.Property(e => e.Col4).HasColumnName("_col_4");
            entity.Property(e => e.Col5).HasColumnType("datetime").HasColumnName("_col_5");
            entity.Property(e => e.Col6).HasColumnName("_col_6");
            entity.Property(e => e.Col7).HasColumnType("decimal(18, 2)").HasColumnName("_col_7");
            entity.Property(e => e.Col8).HasColumnName("_col_8");
            entity.Property(e => e.Col9).HasColumnName("_col_9");
        });

        modelBuilder.Entity<DwebsiteType>(entity =>
        {
            entity.ToTable("DWebsiteTypes", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<Eftpayment>(entity =>
        {
            entity.ToTable("EFTPayments", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_EFTPayments_ElectronicPayments");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Eftpayment)
                .HasForeignKey<Eftpayment>(d => d.Id)
                .HasConstraintName("FK_EFTPayments_ElectronicPayments");
        });

        modelBuilder.Entity<EftpaymentMethod>(entity =>
        {
            entity.HasKey(e => e.AssociatedGuid);

            entity.ToTable("EFTPaymentMethods", "Core");

            entity.HasIndex(e => e.AssociatedGuid, "IX_FK_EFTPaymentMethods_PaymentMethods");

            entity
                .Property(e => e.AssociatedGuid)
                .ValueGeneratedNever()
                .HasColumnName("AssociatedGUID");

            entity
                .HasOne(d => d.Associated)
                .WithOne(p => p.EftpaymentMethod)
                .HasForeignKey<EftpaymentMethod>(d => d.AssociatedGuid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EFTPaymentMethods_PaymentMethods");
        });

        modelBuilder.Entity<ElectronicBatch>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_EFTBatches");

            entity.ToTable("ElectronicBatches", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_EFTBatches_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.BatchDate).HasColumnType("datetime");
            entity.Property(e => e.CollectingAgentId).HasColumnName("CollectingAgentID");
            entity.Property(e => e.LastDownloaded).HasColumnType("datetime");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ElectronicBatch)
                .HasForeignKey<ElectronicBatch>(d => d.Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EFTBatches_Root");
        });

        modelBuilder.Entity<ElectronicPayment>(entity =>
        {
            entity.ToTable("ElectronicPayments", "Core");

            entity.HasIndex(e => e.BenefitId, "IX_FK_ElectronicPayments_Benefits");

            entity.HasIndex(
                e => e.DpaymentMethodTypeId,
                "IX_FK_ElectronicPayments_DPaymentMethodTypes"
            );

            entity.HasIndex(e => e.ElectronicBatchId, "IX_FK_ElectronicPayments_ElectronicBatches");

            entity.HasIndex(e => e.PaymentsId, "IX_FK_ElectronicPayments_Payments");

            entity.HasIndex(e => e.ReportId, "IX_FK_ElectronicPayments_Reports");

            entity.HasIndex(e => e.Id, "IX_FK_ElectronicPayments_Root");

            entity.HasIndex(
                e => new { e.ReportId, e.ElectronicBatchId },
                "IX_ReportID_ElectronicBatchID"
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.DpaymentMethodTypeId).HasColumnName("DPaymentMethodTypeID");
            entity.Property(e => e.ElectronicBatchId).HasColumnName("ElectronicBatchID");
            entity.Property(e => e.PaymentsId).HasColumnName("PaymentsID");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.ElectronicPayments)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPayments_Benefits");

            entity
                .HasOne(d => d.DpaymentMethodType)
                .WithMany(p => p.ElectronicPayments)
                .HasForeignKey(d => d.DpaymentMethodTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPayments_DPaymentMethodTypes");

            entity
                .HasOne(d => d.ElectronicBatch)
                .WithMany(p => p.ElectronicPayments)
                .HasForeignKey(d => d.ElectronicBatchId)
                .HasConstraintName("FK_ElectronicPayments_ElectronicBatches");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ElectronicPayment)
                .HasForeignKey<ElectronicPayment>(d => d.Id)
                .HasConstraintName("FK_ElectronicPayments_Root");

            entity
                .HasOne(d => d.Payments)
                .WithMany(p => p.ElectronicPayments)
                .HasForeignKey(d => d.PaymentsId)
                .HasConstraintName("FK_ElectronicPayments_Payments");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ElectronicPayments)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPayments_Reports");
        });

        modelBuilder.Entity<ElectronicPaymentConfiguration>(entity =>
        {
            entity.ToTable(
                "ElectronicPaymentConfigurations",
                "Core",
                tb => tb.HasTrigger("Trigger_ElectronicPaymentConfigurations_LastModifiedDate")
            );

            entity
                .HasIndex(
                    e => new
                    {
                        e.OrganizationId,
                        e.BenefitId,
                        e.UnionId,
                    },
                    "IX_ElectronicPaymentConfigurations_OrgBeneUnion"
                )
                .IsUnique();

            entity.HasIndex(e => e.BenefitId, "IX_FK_ElectronicPaymentConfigurations_Benefits");

            entity.HasIndex(
                e => e.ChapterId,
                "IX_FK_ElectronicPaymentConfigurations_ChapterOrganizations"
            );

            entity.HasIndex(
                e => e.DelectronicPaymentOptionId,
                "IX_FK_ElectronicPaymentConfigurations_DElectronicPaymentOptions"
            );

            entity.HasIndex(
                e => e.NachaconfigurationId,
                "IX_FK_ElectronicPaymentConfigurations_NACHAConfigurations"
            );

            entity.HasIndex(
                e => e.OrganizationId,
                "IX_FK_ElectronicPaymentConfigurations_Organizations"
            );

            entity.HasIndex(e => e.Id, "IX_FK_ElectronicPaymentConfigurations_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.CreatedBy).HasMaxLength(256);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity
                .Property(e => e.DelectronicPaymentOptionId)
                .HasColumnName("DElectronicPaymentOptionID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NachaconfigurationId).HasColumnName("NACHAConfigurationID");
            entity.Property(e => e.OrganizationId).HasColumnName("OrganizationID");
            entity.Property(e => e.UnionId).HasColumnName("UnionID");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.ElectronicPaymentConfigurations)
                .HasForeignKey(d => d.BenefitId)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_Benefits");

            entity
                .HasOne(d => d.Chapter)
                .WithMany(p => p.ElectronicPaymentConfigurationChapters)
                .HasForeignKey(d => d.ChapterId)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_ChapterOrganizations");

            entity
                .HasOne(d => d.DelectronicPaymentOption)
                .WithMany(p => p.ElectronicPaymentConfigurations)
                .HasForeignKey(d => d.DelectronicPaymentOptionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_DElectronicPaymentOptions");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ElectronicPaymentConfiguration)
                .HasForeignKey<ElectronicPaymentConfiguration>(d => d.Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_Root");

            entity
                .HasOne(d => d.Nachaconfiguration)
                .WithMany(p => p.ElectronicPaymentConfigurations)
                .HasForeignKey(d => d.NachaconfigurationId)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_NACHAConfigurations");

            entity
                .HasOne(d => d.Organization)
                .WithMany(p => p.ElectronicPaymentConfigurationOrganizations)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ElectronicPaymentConfigurations_Organizations");
        });

        modelBuilder.Entity<EmailAddress>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_EmailAddress");

            entity.ToTable("EmailAddresses", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_EmailAddresses_ContactMechanisms");

            entity.HasIndex(e => e.DemailAddressTypeId, "IX_FK_EmailAddresses_DEmailAddressTypes");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DemailAddressTypeId).HasColumnName("DEmailAddressTypeID");
            entity.Property(e => e.EmailAddress1).HasMaxLength(256).HasColumnName("EmailAddress");

            entity
                .HasOne(d => d.DemailAddressType)
                .WithMany(p => p.EmailAddresses)
                .HasForeignKey(d => d.DemailAddressTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmailAddresses_DEmailAddressTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.EmailAddress)
                .HasForeignKey<EmailAddress>(d => d.Id)
                .HasConstraintName("FK_EmailAddresses_ContactMechanisms");
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            entity.ToTable("Employees", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Employees_Persons");

            entity.HasIndex(e => e.SearchSsn, "IX_SearchSSN");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.BirthDate).HasColumnType("datetime");
            entity.Property(e => e.DateOfHire).HasColumnType("datetime");
            entity.Property(e => e.DateOfTermination).HasColumnType("datetime");
            entity.Property(e => e.ExternalEmployeeId).HasMaxLength(50).HasColumnName("EmployeeID");
            entity.Property(e => e.HomeLocalId).HasColumnName("HomeLocalID");
            entity.Property(e => e.SearchSsn).HasMaxLength(20).HasColumnName("SearchSSN");
            entity.Property(e => e.Ssn).HasColumnName("SSN");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Employee)
                .HasForeignKey<Employee>(d => d.Id)
                .HasConstraintName("FK_Employees_Persons");
        });

        modelBuilder.Entity<EmployeeImport>(entity =>
        {
            entity.HasNoKey().ToTable("EmployeeImport", "Core");

            entity.Property(e => e.Agreement).HasMaxLength(50).HasColumnName("AGREEMENT");
            entity.Property(e => e.Classification).HasMaxLength(50).HasColumnName("CLASSIFICATION");
            entity.Property(e => e.EmployeeNumber).HasColumnName("Employee_Number");
            entity.Property(e => e.Eprid).HasColumnName("EPRID");
            entity.Property(e => e.FirstName).HasMaxLength(50).HasColumnName("FIRST_NAME");
            entity
                .Property(e => e.HourlyRate)
                .HasColumnType("decimal(18, 10)")
                .HasColumnName("Hourly_Rate");
            entity.Property(e => e.LastName).HasMaxLength(50).HasColumnName("LAST_NAME");
            entity.Property(e => e.Ssn).HasMaxLength(50).HasColumnName("SSN");
        });

        modelBuilder.Entity<Employer>(entity =>
        {
            entity.ToTable("Employers", "Core");

            entity.HasIndex(e => e.FEIN, "IX_EmployerIdentificationNumber");
            entity.HasIndex(e => e.Id, "IX_FK_Employers_Organizations");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");

            entity.Property(e => e.AssociationId).HasColumnName("AssociationID");
            entity.Property(e => e.Dba).HasColumnName("DBA");
            entity
                .Property(e => e.FEIN)
                .HasColumnName("EmployerIdentificationNumber") // Ensure the correct column name mapping
                .HasMaxLength(9);

            entity
                .HasOne(d => d.Organization) // Updated Navigation Property
                .WithOne(p => p.Employer)
                .HasForeignKey<Employer>(d => d.Id)
                .HasConstraintName("FK_Employers_Organizations");
        });

        modelBuilder.Entity<EmployersToAgreement>(entity =>
        {
            entity.ToTable("EmployersToAgreements", "Core");

            entity
                .HasIndex(
                    e => new
                    {
                        e.EmployerId,
                        e.AgreementId,
                        e.Id,
                    },
                    "IX_EmployersToAgreements_Covering"
                )
                .HasFillFactor(80);

            entity.HasIndex(e => e.AgreementId, "IX_FK_EmployersToAgreements_Agreements");

            entity.HasIndex(e => e.EmployerId, "IX_FK_EmployersToAgreements_Employers");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.EmployerId).HasColumnName("EmployerID");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.EmployersToAgreements)
                .HasForeignKey(d => d.AgreementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployersToAgreements_Agreements");

            entity
                .HasOne(d => d.Employer)
                .WithMany(p => p.EmployersToAgreements)
                .HasForeignKey(d => d.EmployerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployersToAgreements_Employers");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.EmployersToAgreement)
                .HasForeignKey<EmployersToAgreement>(d => d.Id)
                .HasConstraintName("FK_EmployersToAgreements_Root");
        });

        modelBuilder.Entity<Epruser>(entity =>
        {
            entity.HasKey(e => e.AspnetUserId);

            entity.ToTable("EPRUsers", "Core");

            entity.HasIndex(e => e.BannerImageId, "IX_FK_EPRUsers_Images");

            entity.HasIndex(e => e.OrganizationId, "IX_FK_EPRUsers_Organizations");

            entity.HasIndex(e => e.AspnetUserId, "IX_FK_EPRUsers_aspnet_Users");

            entity
                .Property(e => e.AspnetUserId)
                .ValueGeneratedNever()
                .HasColumnName("ASPNetUserID");
            entity.Property(e => e.BannerImageId).HasColumnName("BannerImageID");
            entity
                .Property(e => e.CurrentSessionId)
                .HasMaxLength(256)
                .HasColumnName("CurrentSessionID");
            entity.Property(e => e.OrganizationId).HasColumnName("OrganizationID");
            entity.Property(e => e.PhoneNumber).HasMaxLength(15).IsUnicode(false);
            entity
                .Property(e => e.PreferredMfamethod)
                .HasMaxLength(256)
                .HasColumnName("PreferredMFAMethod");

            entity
                .HasOne(d => d.AspnetUser)
                .WithOne(p => p.Epruser)
                .HasForeignKey<Epruser>(d => d.AspnetUserId)
                .HasConstraintName("FK_EPRUsers_aspnet_Users");

            entity
                .HasOne(d => d.BannerImage)
                .WithMany(p => p.Eprusers)
                .HasForeignKey(d => d.BannerImageId)
                .HasConstraintName("FK_EPRUsers_Images");

            entity
                .HasOne(d => d.Organization)
                .WithMany(p => p.Eprusers)
                .HasForeignKey(d => d.OrganizationId)
                .HasConstraintName("FK_EPRUsers_Organizations");
        });

        modelBuilder.Entity<FundAdministrator>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Families_1");

            entity.ToTable("FundAdministrators", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_FundAdministrators_Organizations");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.FundAdministrator)
                .HasForeignKey<FundAdministrator>(d => d.Id)
                .HasConstraintName("FK_FundAdministrators_Organizations");
        });

        modelBuilder.Entity<FundingComment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Core.FundingComments");

            entity.ToTable(
                "FundingComments",
                "Core",
                tb => tb.HasTrigger("Trigger_FundingComments_LastModifiedDate")
            );

            entity.HasIndex(e => e.ReportId, "IX_FK_Core.FundingComments_Core.Reports");

            entity.HasIndex(e => e.AgreementId, "IX_FK_FundingComments_Agreements");

            entity.HasIndex(e => e.BenefitId, "IX_FK_FundingComments_Benefits");

            entity.HasIndex(e => e.EmployerId, "IX_FK_FundingComments_Employers");

            entity.HasIndex(e => new { e.ReportId, e.BenefitId }, "IX_FundingComments").IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.EmployerId).HasColumnName("EmployerID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");
            entity.Property(e => e.WorkMonth).HasColumnType("datetime");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.FundingComments)
                .HasForeignKey(d => d.AgreementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FundingComments_Agreements");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.FundingComments)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FundingComments_Benefits");

            entity
                .HasOne(d => d.Employer)
                .WithMany(p => p.FundingComments)
                .HasForeignKey(d => d.EmployerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FundingComments_Employers");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.FundingComments)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Core.FundingComments_Core.Reports");
        });

        modelBuilder.Entity<FundingCommentDetail>(entity =>
        {
            entity.ToTable(
                "FundingCommentDetails",
                "Core",
                tb => tb.HasTrigger("Trigger_FundingCommentDetails_LastModifiedDate")
            );

            entity.HasIndex(e => e.OrganizationId, "IX_FK_FundingCommentDetails_Organizations");

            entity.HasIndex(e => e.Id, "IX_FundingCommentDetails");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FundingCommentsId).HasColumnName("FundingCommentsID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrganizationId).HasColumnName("OrganizationID");

            entity
                .HasOne(d => d.Organization)
                .WithMany(p => p.FundingCommentDetails)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FundingCommentDetails_Organizations");
        });

        modelBuilder.Entity<GetExpressCheckoutDetailsResponse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_PayPal.GetExpressCheckoutDetailsResponse");

            entity.ToTable("GetExpressCheckoutDetailsResponse", "PayPal");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Ack).HasMaxLength(256).HasColumnName("ACK");
            entity
                .Property(e => e.Billingagreementacceptedstatus)
                .HasColumnName("BILLINGAGREEMENTACCEPTEDSTATUS");
            entity.Property(e => e.Build).HasMaxLength(50).HasColumnName("BUILD");
            entity.Property(e => e.Checkoutstatus).HasColumnName("CHECKOUTSTATUS");
            entity.Property(e => e.Correlationid).HasMaxLength(128).HasColumnName("CORRELATIONID");
            entity.Property(e => e.Countrycode).HasMaxLength(2).HasColumnName("COUNTRYCODE");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(256).HasColumnName("EMAIL");
            entity.Property(e => e.Errorcode).HasColumnName("ERRORCODE");
            entity.Property(e => e.Firstname).HasMaxLength(25).HasColumnName("FIRSTNAME");
            entity.Property(e => e.Lastname).HasMaxLength(25).HasColumnName("LASTNAME");
            entity.Property(e => e.Longmessage).HasColumnName("LONGMESSAGE");
            entity.Property(e => e.OrderId).HasColumnName("OrderID");
            entity.Property(e => e.Payerid).HasMaxLength(13).HasColumnName("PAYERID");
            entity.Property(e => e.Payerstatus).HasMaxLength(50).HasColumnName("PAYERSTATUS");
            entity.Property(e => e.Phonenum).HasMaxLength(50).HasColumnName("PHONENUM");
            entity.Property(e => e.SessionId).HasMaxLength(256).HasColumnName("SessionID");
            entity.Property(e => e.Severritycode).HasColumnName("SEVERRITYCODE");
            entity.Property(e => e.Shortmessage).HasColumnName("SHORTMESSAGE");
            entity.Property(e => e.Timestamp).HasColumnType("datetime").HasColumnName("TIMESTAMP");
            entity.Property(e => e.Token).HasMaxLength(20).HasColumnName("TOKEN");
            entity.Property(e => e.Version).HasMaxLength(25).HasColumnName("VERSION");
        });

        modelBuilder.Entity<GuidSubstitution>(entity =>
        {
            entity.HasKey(e => new { e.Guid, e.SubstitutionTypeId });

            entity.ToTable("GuidSubstitution", "Core");

            entity.HasIndex(
                e => e.SubstitutionTypeId,
                "IX_FK_GuidSubstitution_DGuidSubstitutionType"
            );

            entity.Property(e => e.Guid).HasColumnName("GUID");
            entity.Property(e => e.SubstitutionGuid).HasColumnName("SubstitutionGUID");

            entity
                .HasOne(d => d.SubstitutionType)
                .WithMany(p => p.GuidSubstitutions)
                .HasForeignKey(d => d.SubstitutionTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_GuidSubstitution_DGuidSubstitutionType");
        });

        modelBuilder.Entity<Image>(entity =>
        {
            entity.ToTable("Images", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Images");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Image)
                .HasForeignKey<Image>(d => d.Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Images");
        });

        modelBuilder.Entity<Kbaanswer>(entity =>
        {
            entity.ToTable("KBAAnswers", "Core");

            entity.HasIndex(e => e.QuestionId, "IX_KBAAnswers_QuestionID");

            entity.HasIndex(e => e.UserGuid, "IX_KBAAnswers_UserGUID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.QuestionId).HasColumnName("QuestionID");
            entity.Property(e => e.UserGuid).HasColumnName("UserGUID");

            entity
                .HasOne(d => d.Question)
                .WithMany(p => p.Kbaanswers)
                .HasForeignKey(d => d.QuestionId);

            entity.HasOne(d => d.User).WithMany(p => p.Kbaanswers).HasForeignKey(d => d.UserGuid);
        });

        modelBuilder.Entity<Kbaquestion>(entity =>
        {
            entity.ToTable("KBAQuestions", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<LoggedEvent>(entity =>
        {
            entity.ToTable("LoggedEvents", "Core");
            entity.HasIndex(e => e.DeventSubTypeId, "IX_FK_LoggedEvents_DEventSubTypes");

            entity.HasIndex(e => e.DeventTypeId, "IX_FK_LoggedEvents_DEventTypes");

            entity
                .HasIndex(e => e.EventDateTime, "IX_LoggedEvents_EventDateTime")
                .IsClustered()
                .HasFillFactor(80);

            entity
                .HasIndex(
                    e => new { e.EventDateTime, e.DeventTypeId },
                    "IX_LoggedEvents_EventDateTime_DEventTypeID"
                )
                .HasFillFactor(80);

            entity
                .HasIndex(
                    e => new { e.EventDateTime, e.UserName },
                    "IX_LoggedEvents_EventDateTime_UserName"
                )
                .HasFillFactor(80);

            entity.Property(e => e.DeventSubTypeId).HasColumnName("DEventSubTypeID");
            entity.Property(e => e.DeventTypeId).HasColumnName("DEventTypeID");
            entity.Property(e => e.EventDateTime).HasColumnType("datetime");
            entity.Property(e => e.SessionId).HasColumnName("SessionID");
            entity.Property(e => e.UserName).HasMaxLength(256);

            entity
                .HasOne(d => d.DeventSubType)
                .WithMany()
                .HasForeignKey(d => d.DeventSubTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LoggedEvents_DEventSubTypes");

            entity
                .HasOne(d => d.DeventType)
                .WithMany()
                .HasForeignKey(d => d.DeventTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LoggedEvents_DEventTypes");
        });

        modelBuilder.Entity<MemberDiscountsForAssociationReport>(entity =>
        {
            entity.HasKey(e => e.Necaid).HasName("PK_MemberDiscountsForAssociationReport031422");

            entity.ToTable("MemberDiscountsForAssociationReport", "NECA");

            entity.Property(e => e.Necaid).HasMaxLength(50).HasColumnName("NECAID");
            entity.Property(e => e.Discount).HasColumnType("decimal(18, 10)");
        });

        modelBuilder.Entity<MemberDiscountsForAssociationReportBackup>(entity =>
        {
            entity.HasNoKey().ToTable("MemberDiscountsForAssociationReportBackup", "NECA");

            entity.Property(e => e.Discount).HasColumnType("decimal(18, 10)");
            entity.Property(e => e.Necaid).HasMaxLength(50).HasColumnName("NECAID");
        });

        modelBuilder.Entity<MfaRecord>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MfaRecor__3214EC27F19CCED5");

            entity.ToTable("MfaRecords", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.ExpiresAt).HasColumnType("datetime");
        });

        modelBuilder.Entity<Mm8>(entity =>
        {
            entity.HasNoKey().ToTable("mm8", "NECA");

            entity.Property(e => e.ConstitId).HasColumnName("constit_id");
            entity.Property(e => e.Discount).HasMaxLength(50).HasColumnName("discount");
        });

        modelBuilder.Entity<Mm9>(entity =>
        {
            entity.HasNoKey().ToTable("mm9", "NECA");

            entity.Property(e => e.ConstitId).HasMaxLength(50).HasColumnName("constit_id");
            entity.Property(e => e.Discount).HasMaxLength(50).HasColumnName("discount");
        });

        modelBuilder.Entity<Nachaconfiguration>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_NATCHAConfigurations");

            entity.ToTable("NACHAConfigurations", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.CollectingAgentId).HasColumnName("CollectingAgentID");
            entity.Property(e => e.CompanyDescriptiveDate).HasMaxLength(6).IsUnicode(false);
            entity.Property(e => e.CompanyDiscretionaryData).HasMaxLength(20).IsUnicode(false);
            entity.Property(e => e.CompanyIdentification).HasMaxLength(20).IsUnicode(false);
            entity.Property(e => e.CompanyName).HasMaxLength(16).IsUnicode(false);
            entity.Property(e => e.ImmediateDestinationName).HasMaxLength(23).IsUnicode(false);
            entity.Property(e => e.ImmediateOrigin).HasMaxLength(10).IsUnicode(false);
            entity.Property(e => e.ImmediateOriginName).HasMaxLength(23).IsUnicode(false);
            entity.Property(e => e.OffsetAccountNumber).HasMaxLength(17).IsUnicode(false);
            entity.Property(e => e.OffsetDescription).HasMaxLength(22).IsUnicode(false);
            entity.Property(e => e.OffsetRoutingNumber).HasMaxLength(9).IsUnicode(false);
            entity.Property(e => e.ReferenceCode).HasMaxLength(8).IsUnicode(false);
        });

        modelBuilder.Entity<NecaAmfRate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__NecaAmfR__3214EC27CB276B63");

            entity.ToTable("NecaAmfRates", "Custom");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.CalculationId).HasColumnName("CalculationID");
            entity.Property(e => e.CategoryId).HasColumnName("CategoryID");
            entity.Property(e => e.RateScheduleId).HasColumnName("RateScheduleID");
        });

        modelBuilder.Entity<NecaAmfRateSchedule>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__NecaAmfR__3214EC27FA7E15EA");

            entity.ToTable("NecaAmfRateSchedules", "Custom");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.EffectiveEndDate).HasColumnType("datetime");
            entity.Property(e => e.EffectiveStartDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<NecaAmfStep>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__NecaAmfS__3214EC2743D5B285");

            entity.ToTable("NecaAmfSteps", "Custom");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Maximum).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Minimum).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.RateId).HasColumnName("RateID");
            entity.Property(e => e.Value).HasColumnType("decimal(28, 10)");
        });

        modelBuilder.Entity<NewsItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__NewsItems__41D2933E");

            entity.ToTable(
                "NewsItems",
                "Core",
                tb => tb.HasTrigger("Trigger_NewsItems_LastModifiedDate")
            );

            entity.HasIndex(e => e.RoleGroup, "IX_FK_NewsItems_ToRoleGroups");

            entity.Property(e => e.CreatedBy).HasMaxLength(256);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ImageUrl).HasColumnName("ImageURL");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NavigationUrl).HasColumnName("NavigationURL");
            entity.Property(e => e.PublishDate).HasColumnType("datetime");

            entity
                .HasOne(d => d.RoleGroupNavigation)
                .WithMany(p => p.NewsItems)
                .HasForeignKey(d => d.RoleGroup)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_NewsItems_ToRoleGroups");
        });

        modelBuilder.Entity<Newtable>(entity =>
        {
            entity.HasNoKey().ToTable("newtable", "NECA");

            entity.Property(e => e.Discount).HasMaxLength(50);
            entity.Property(e => e.Necaid).HasMaxLength(50).HasColumnName("NECAID");
        });

        modelBuilder.Entity<Note>(entity =>
        {
            entity.HasKey(e => new { e.RootId, e.Id });

            entity.ToTable("Notes", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Notes_Root");

            entity.HasIndex(e => e.RootId, "IX_FK_Notes_RootOwner");

            entity
                .Property(e => e.RootId)
                .HasComment("The ID of the root object with which this note is associated.")
                .HasColumnName("RootID");
            entity.Property(e => e.Id).HasColumnName("ID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithMany(p => p.NoteIdNavigations)
                .HasForeignKey(d => d.Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Notes_Root");

            entity
                .HasOne(d => d.Root)
                .WithMany(p => p.NoteRoots)
                .HasForeignKey(d => d.RootId)
                .HasConstraintName("FK_Notes_RootOwner");
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.ToTable("Orders", "ShoppingCart");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ConfirmationNumber).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(256);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrganizationId).HasColumnName("OrganizationID");
            entity.Property(e => e.StatusId).HasDefaultValueSql("((1))").HasColumnName("StatusID");

            entity
                .HasOne(d => d.Organization)
                .WithMany(p => p.Orders)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Orders_Organizations");

            entity
                .HasOne(d => d.Status)
                .WithMany(p => p.Orders)
                .HasForeignKey(d => d.StatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Orders_DOrderStatus");
        });

        modelBuilder.Entity<OrderDetail>(entity =>
        {
            entity.ToTable(
                "OrderDetails",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_OrderDetails_LastModifiedDate")
            );

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(256);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrderId).HasColumnName("OrderID");
            entity.Property(e => e.PaymentTypeId).HasColumnName("PaymentTypeID");
            entity.Property(e => e.Price).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity
                .HasOne(d => d.Order)
                .WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.OrderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrderDetails_Order");

            entity
                .HasOne(d => d.PaymentType)
                .WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.PaymentTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrderDetails_PaymentType");

            entity
                .HasOne(d => d.Product)
                .WithMany(p => p.OrderDetails)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrderDetails_Products");
        });

        modelBuilder.Entity<Organization>(entity =>
        {
            entity.ToTable("Organizations", "Core");

            entity.HasIndex(e => e.DorganizationTypeId, "IX_FK_Organizations_DOrganizationTypes");

            entity.HasIndex(e => e.Id, "IX_FK_Organizations_Parties");

            entity.HasIndex(e => new { e.Id, e.DorganizationTypeId }, "IX_Organizations_Covering");

            entity.HasIndex(e => e.Name, "IX_Organizations_Name");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.DorganizationTypeId)
                .HasComment("This should correspond to a value in Core.DEntityTypes")
                .HasColumnName("DOrganizationTypeID");
            entity.Property(e => e.Name).HasMaxLength(150);

            entity
                .HasOne(d => d.DorganizationType)
                .WithMany(p => p.Organizations)
                .HasForeignKey(d => d.DorganizationTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Organizations_DOrganizationTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Organization)
                .HasForeignKey<Organization>(d => d.Id)
                .HasConstraintName("FK_Organizations_Parties");
        });

        modelBuilder.Entity<PaperProcessingAuthorization>(entity =>
        {
            entity.HasKey(e => new { e.ChapterId, e.PartyId });

            entity.ToTable("PaperProcessingAuthorizations", "Core");

            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.PartyId).HasColumnName("PartyID");
        });

        modelBuilder.Entity<PartiesToContactMechanism>(entity =>
        {
            entity.ToTable("PartiesToContactMechanisms", "Core");

            entity.HasIndex(
                e => e.ContactMechanismId,
                "IX_FK_PartiesToContactMechanisms_ContactMechanisms"
            );

            entity.HasIndex(e => e.PartyId, "IX_FK_PartiesToContactMechanisms_Parties");

            entity.HasIndex(e => e.Id, "IX_FK_PartiesToContactMechanisms_Root");

            entity
                .HasIndex(
                    e => new { e.PartyId, e.ContactMechanismId },
                    "IX_PartiesToContactMechanisms"
                )
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.ContactMechanismId).HasColumnName("ContactMechanismID");
            entity.Property(e => e.PartyId).HasColumnName("PartyID");

            entity
                .HasOne(d => d.ContactMechanism)
                .WithMany(p => p.PartiesToContactMechanisms)
                .HasForeignKey(d => d.ContactMechanismId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartiesToContactMechanisms_ContactMechanisms");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.PartiesToContactMechanism)
                .HasForeignKey<PartiesToContactMechanism>(d => d.Id)
                .HasConstraintName("FK_PartiesToContactMechanisms_Root");

            entity
                .HasOne(d => d.Party)
                .WithMany(p => p.PartiesToContactMechanisms)
                .HasForeignKey(d => d.PartyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartiesToContactMechanisms_Parties");
        });

        modelBuilder.Entity<Party>(entity =>
        {
            entity.ToTable("Parties", "Core");

            entity.HasIndex(e => e.DpartyTypeId, "IX_FK_Parties_DPartyTypes");

            entity.HasIndex(e => e.Id, "IX_FK_Parties_Root");

            entity.HasIndex(e => new { e.Id, e.DpartyTypeId }, "IX_Parties").HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DpartyTypeId).HasColumnName("DPartyTypeID");

            entity
                .HasOne(d => d.DpartyType)
                .WithMany(p => p.Parties)
                .HasForeignKey(d => d.DpartyTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Parties_DPartyTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Party)
                .HasForeignKey<Party>(d => d.Id)
                .HasConstraintName("FK_Parties_Root");
        });

        modelBuilder.Entity<PasswordResetAuthorization>(entity =>
        {
            entity.HasNoKey().ToTable("PasswordResetAuthorizations", "Core");

            entity.Property(e => e.AspnetUserGuid).HasColumnName("ASPNetUserGUID");
            entity
                .Property(e => e.CreationTimestamp)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.PublicGuid).HasColumnName("PublicGUID");
        });

        modelBuilder.Entity<PayStub>(entity =>
        {
            entity.HasIndex(e => e.TimeSheetId, "IX_PayStubs_TimeSheetId");

            entity
                .HasOne(d => d.TimeSheet)
                .WithMany(p => p.PayStubs)
                .HasForeignKey(d => d.TimeSheetId);

            entity
                .HasOne(d => d.Employee)
                .WithMany()
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired();

        });

        modelBuilder.Entity<PayStubDetail>(entity =>
        {
            entity.Property(e => e.DTHours).HasColumnName("DTHours");
            entity.Property(e => e.OTHours).HasColumnName("OTHours");
            entity.Property(e => e.STHours).HasColumnName("STHours");
        });

        modelBuilder.Entity<Payment>(entity =>
        {
            entity.ToTable("Payments", "Core");

            entity.HasIndex(e => e.ReportId, "IX_FK_Payments_Reports");

            entity.HasIndex(e => e.Id, "IX_FK_Payments_Root");

            entity
                .HasIndex(e => e.CollectingAgentId, "IX_Payments_CollectingAgentID")
                .HasFillFactor(80);

            entity.HasIndex(e => e.ReportId, "IX_Payments_ReportID").HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");
            entity
                .Property(e => e.CollectingAgentId)
                .HasComment(
                    "The ID of the collecting agent's organization that created the payment. This ensures that the overpayments are always tied to the organization."
                )
                .HasColumnName("CollectingAgentID");
            entity.Property(e => e.PaymentDate).HasColumnType("datetime");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Payment)
                .HasForeignKey<Payment>(d => d.Id)
                .HasConstraintName("FK_Payments_Root");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.Payments)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Payments_Reports");
        });

        modelBuilder.Entity<PaymentDetail>(entity =>
        {
            entity.HasKey(e => new { e.PaymentId, e.BenefitId });

            entity.ToTable("PaymentDetails", "Core");

            entity.HasIndex(e => e.BenefitId, "IX_FK_PaymentDetails_Benefits");

            entity.HasIndex(e => e.PaymentId, "IX_FK_PaymentDetails_Payments");

            entity.Property(e => e.PaymentId).HasColumnName("PaymentID");
            entity
                .Property(e => e.BenefitId)
                .HasComment(
                    "If this is null, then the amount represents money that went to no benefit (i.e., an overpayment)"
                )
                .HasColumnName("BenefitID");
            entity.Property(e => e.Amount).HasColumnType("decimal(18, 2)");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.PaymentDetails)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PaymentDetails_Benefits");

            entity
                .HasOne(d => d.Payment)
                .WithMany(p => p.PaymentDetails)
                .HasForeignKey(d => d.PaymentId)
                .HasConstraintName("FK_PaymentDetails_Payments");
        });

        modelBuilder.Entity<PaymentMethod>(entity =>
        {
            entity.HasKey(e => e.AssociatedGuid).HasName("PK_PaymentMethods_1");

            entity.ToTable("PaymentMethods", "Core");

            entity.HasIndex(e => e.DpaymentMethodTypeId, "IX_FK_PaymentMethods_DPaymentMethodType");

            entity.HasIndex(e => e.Id, "IX_FK_PaymentMethods_Root");

            entity
                .Property(e => e.AssociatedGuid)
                .ValueGeneratedNever()
                .HasComment(
                    "Either a default payment method for an employer or an elected payment method for a given payment."
                )
                .HasColumnName("AssociatedGUID");
            entity.Property(e => e.DpaymentMethodTypeId).HasColumnName("DPaymentMethodTypeID");
            entity.Property(e => e.Id).HasColumnName("ID");

            entity
                .HasOne(d => d.DpaymentMethodType)
                .WithMany(p => p.PaymentMethods)
                .HasForeignKey(d => d.DpaymentMethodTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PaymentMethods_DPaymentMethodType");

            entity
                .HasOne(d => d.IdNavigation)
                .WithMany(p => p.PaymentMethods)
                .HasForeignKey(d => d.Id)
                .HasConstraintName("FK_PaymentMethods_Root");
        });

        modelBuilder.Entity<Person>(entity =>
        {
            entity.ToTable("Persons", "Core");

            entity.HasIndex(e => e.DgenderId, "IX_FK_Persons_DGenders");

            entity.HasIndex(e => e.DpersonTypeId, "IX_FK_Persons_DPersonTypes");

            entity.HasIndex(e => e.Id, "IX_FK_Persons_Parties");

            entity.HasIndex(e => e.LastName, "IX_LastNameCovering");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DgenderId).HasColumnName("DGenderID");
            entity.Property(e => e.DpersonTypeId).HasColumnName("DPersonTypeID");
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.MiddleName).HasMaxLength(50);
            entity.Property(e => e.Suffix).HasMaxLength(50);
            entity.Property(e => e.Title).HasMaxLength(50);

            entity
                .HasOne(d => d.Dgender)
                .WithMany(p => p.People)
                .HasForeignKey(d => d.DgenderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Persons_DGenders");

            entity
                .HasOne(d => d.DpersonType)
                .WithMany(p => p.People)
                .HasForeignKey(d => d.DpersonTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Persons_DPersonTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Person)
                .HasForeignKey<Person>(d => d.Id)
                .HasConstraintName("FK_Persons_Parties");
        });

        modelBuilder.Entity<PhoneNumber>(entity =>
        {
            entity.ToTable("PhoneNumbers", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_PhoneNumbers_ContactMechanisms");

            entity.HasIndex(e => e.DphoneNumberTypeId, "IX_FK_PhoneNumbers_DPhoneNumberTypes");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DphoneNumberTypeId).HasColumnName("DPhoneNumberTypeID");
            entity.Property(e => e.PhoneNumber1).HasColumnName("PhoneNumber");

            entity
                .HasOne(d => d.DphoneNumberType)
                .WithMany(p => p.PhoneNumbers)
                .HasForeignKey(d => d.DphoneNumberTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PhoneNumbers_DPhoneNumberTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.PhoneNumber)
                .HasForeignKey<PhoneNumber>(d => d.Id)
                .HasConstraintName("FK_PhoneNumbers_ContactMechanisms");
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Products__478B6C94");

            entity.ToTable(
                "Products",
                "Core",
                tb => tb.HasTrigger("Trigger_Products_LastModifiedDate")
            );

            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(256);
        });

        modelBuilder.Entity<Product1>(entity =>
        {
            entity.ToTable(
                "Products",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_Products_LastModifiedDate")
            );

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Active).IsRequired().HasDefaultValueSql("((1))");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Keywords).HasMaxLength(512);
            entity.Property(e => e.LargeImagePath).HasMaxLength(256);
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.MediumImagePath).HasMaxLength(256);
            entity.Property(e => e.Name).HasMaxLength(256);
            entity.Property(e => e.PaymentTypeId).HasColumnName("PaymentTypeID");
            entity.Property(e => e.Price).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ShortDescription).HasMaxLength(256);
            entity.Property(e => e.SmallImagePath).HasMaxLength(256);

            entity
                .HasOne(d => d.PaymentType)
                .WithMany(p => p.Product1s)
                .HasForeignKey(d => d.PaymentTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Products_PaymentType");
        });

        modelBuilder.Entity<ProductCategory>(entity =>
        {
            entity.ToTable(
                "ProductCategory",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_ProductCategory_LastModifiedDate")
            );

            entity
                .HasIndex(e => new { e.DcategoryId, e.ProductId }, "Unique_ProductCategory")
                .IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DcategoryId).HasColumnName("DCategoryID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity
                .HasOne(d => d.Dcategory)
                .WithMany(p => p.ProductCategories)
                .HasForeignKey(d => d.DcategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductCategory_DCategory");

            entity
                .HasOne(d => d.Product)
                .WithMany(p => p.ProductCategories)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductCategory_Product");
        });

        modelBuilder.Entity<ProductsToOrganizationType>(entity =>
        {
            entity.ToTable(
                "ProductsToOrganizationTypes",
                "ShoppingCart",
                tb => tb.HasTrigger("Trigger_ProductsToOrganizationTypes_LastModifiedDate")
            );

            entity
                .HasIndex(
                    e => new { e.OrganizationTypeId, e.ProductId },
                    "Unique_ProductsToOrganizationTypes"
                )
                .IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrganizationTypeId).HasColumnName("OrganizationTypeID");
            entity.Property(e => e.ProductId).HasColumnName("ProductID");

            entity
                .HasOne(d => d.OrganizationType)
                .WithMany(p => p.ProductsToOrganizationTypes)
                .HasForeignKey(d => d.OrganizationTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductsToOrganizationTypes_OrganizationType");

            entity
                .HasOne(d => d.Product)
                .WithMany(p => p.ProductsToOrganizationTypes)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductsToOrganizationTypes_Product");
        });

        modelBuilder.Entity<Rate>(entity =>
        {
            entity.HasNoKey().ToTable("Rates", "Core");

            entity.HasIndex(
                e => new { e.DcalculationMethodId, e.BenefitId },
                "IX_DCalculationMethodID_BenefitID"
            );

            entity.HasIndex(e => e.BenefitId, "IX_FK_Rates_Benefits");

            entity.HasIndex(e => e.ClassificationNameId, "IX_FK_Rates_ClassificationNames");

            entity.HasIndex(e => e.DcalculationMethodId, "IX_FK_Rates_DCalculationMethods");

            entity.HasIndex(e => e.DcalculationModifierId, "IX_FK_Rates_DCalculationModifiers");

            entity.HasIndex(e => e.RateScheduleId, "IX_FK_Rates_RateSchedules");

            entity.HasIndex(e => e.SubClassificationId, "IX_FK_Rates_SubClassifications");

            entity
                .HasIndex(
                    e => new
                    {
                        e.RateScheduleId,
                        e.BenefitId,
                        e.ClassificationNameId,
                        e.SubClassificationId,
                    },
                    "IX_Rates"
                )
                .IsUnique()
                .IsClustered()
                .HasFillFactor(80);

            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ClassificationNameId).HasColumnName("ClassificationNameID");
            entity.Property(e => e.DcalculationMethodId).HasColumnName("DCalculationMethodID");
            entity.Property(e => e.DcalculationModifierId).HasColumnName("DCalculationModifierID");
            entity.Property(e => e.MaximumBound).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.MinimumBound).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.RateScheduleId).HasColumnName("RateScheduleID");
            entity.Property(e => e.SubClassificationId).HasColumnName("SubClassificationID");
            entity
                .Property(e => e.Value)
                .HasComment("Value can be null if the calculation method is \"Not Applicable\"")
                .HasColumnType("decimal(28, 10)");

            entity
                .HasOne(d => d.Benefit)
                .WithMany()
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Rates_Benefits");

            entity
                .HasOne(d => d.ClassificationName)
                .WithMany()
                .HasForeignKey(d => d.ClassificationNameId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Rates_ClassificationNames");

            entity
                .HasOne(d => d.DcalculationMethod)
                .WithMany()
                .HasForeignKey(d => d.DcalculationMethodId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Rates_DCalculationMethods");

            entity
                .HasOne(d => d.DcalculationModifier)
                .WithMany()
                .HasForeignKey(d => d.DcalculationModifierId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Rates_DCalculationModifiers");

            entity
                .HasOne(d => d.RateSchedule)
                .WithMany()
                .HasForeignKey(d => d.RateScheduleId)
                .HasConstraintName("FK_Rates_RateSchedules");

            entity
                .HasOne(d => d.SubClassification)
                .WithMany()
                .HasForeignKey(d => d.SubClassificationId)
                .HasConstraintName("FK_Rates_SubClassifications");
        });

        modelBuilder.Entity<RateSchedule>(entity =>
        {
            entity.ToTable("RateSchedules", "Core");

            entity.HasIndex(e => e.AgreementId, "IX_AgreementID");

            entity.HasIndex(e => e.AgreementId, "IX_FK_RateSchedules_Agreements");

            entity.HasIndex(e => e.Id, "IX_FK_RateSchedules_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.EffectiveEndDate).HasColumnType("datetime");
            entity.Property(e => e.EffectiveStartDate).HasColumnType("datetime");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.RateSchedules)
                .HasForeignKey(d => d.AgreementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RateSchedules_Agreements");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.RateSchedule)
                .HasForeignKey<RateSchedule>(d => d.Id)
                .HasConstraintName("FK_RateSchedules_Root");
        });

        modelBuilder.Entity<Relationship>(entity =>
        {
            entity.ToTable("Relationships", "Core");

            entity.HasIndex(
                e => e.DrelationshipTypeId,
                "IX_FK_Relationships_DRelationshipSubTypes"
            );

            entity.HasIndex(e => e.DrelationshipTypeId, "IX_FK_Relationships_DRelationshipTypes");

            entity.HasIndex(e => e.LeftPartyId, "IX_FK_Relationships_LeftParties");

            entity.HasIndex(e => e.RightPartyId, "IX_FK_Relationships_RightParties");

            entity.HasIndex(e => e.Id, "IX_FK_Relationships_Root");

            entity
                .HasIndex(
                    e => new
                    {
                        e.LeftPartyId,
                        e.RightPartyId,
                        e.DrelationshipTypeId,
                        e.DrelationshipSubTypeId,
                        e.Id,
                    },
                    "IX_Relationships_Covering"
                )
                .HasFillFactor(80);

            entity
                .HasIndex(
                    e => new
                    {
                        e.DrelationshipTypeId,
                        e.RightPartyId,
                        e.LeftPartyId,
                    },
                    "IX_Relationships_DRelationshipTypeIDRightPartyIDLeftPartyID"
                )
                .HasFillFactor(80);

            entity.HasIndex(
                e => e.DrelationshipTypeId,
                "IX_Relationships_DRelationshipTypeID_Covering"
            );

            entity.HasIndex(e => e.RightPartyId, "IX_RightPartyIDCovering");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DrelationshipSubTypeId).HasColumnName("DRelationshipSubTypeID");
            entity.Property(e => e.DrelationshipTypeId).HasColumnName("DRelationshipTypeID");
            entity.Property(e => e.LeftPartyId).HasColumnName("LeftPartyID");
            entity
                .Property(e => e.RightPartyId)
                .HasComment(
                    "The RightPartyID should only be null for one-to-one type relationships."
                )
                .HasColumnName("RightPartyID");

            entity
                .HasOne(d => d.DrelationshipType)
                .WithMany(p => p.Relationships)
                .HasForeignKey(d => d.DrelationshipTypeId)
                .HasConstraintName("FK_Relationships_DRelationshipTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Relationship)
                .HasForeignKey<Relationship>(d => d.Id)
                .HasConstraintName("FK_Relationships_Root");

            entity
                .HasOne(d => d.LeftParty)
                .WithMany(p => p.RelationshipLeftParties)
                .HasForeignKey(d => d.LeftPartyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Relationships_LeftParties");

            entity
                .HasOne(d => d.RightParty)
                .WithMany(p => p.RelationshipRightParties)
                .HasForeignKey(d => d.RightPartyId)
                .HasConstraintName("FK_Relationships_RightParties");

            entity
                .HasOne(d => d.Drelationship)
                .WithMany(p => p.Relationships)
                .HasForeignKey(d => new { d.DrelationshipSubTypeId, d.DrelationshipTypeId })
                .HasConstraintName("FK_Relationships_DRelationshipSubTypes");
        });

        modelBuilder.Entity<RelationshipStatus>(entity =>
        {
            entity.ToTable("RelationshipStatuses", "Core");

            entity.HasIndex(
                e => e.DrelationshipStatusId,
                "IX_FK_RelationshipStatuses_DRelationshipStatuses"
            );

            entity.HasIndex(e => e.RelationshipId, "IX_FK_RelationshipStatuses_Relationships");

            entity.HasIndex(e => e.Id, "IX_FK_RelationshipStatuses_Root");

            entity
                .HasIndex(
                    e => new
                    {
                        e.RelationshipId,
                        e.DrelationshipStatusId,
                        e.Id,
                    },
                    "IX_RelationshipStatuses_Covering"
                )
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DrelationshipStatusId).HasColumnName("DRelationshipStatusID");
            entity.Property(e => e.RelationshipId).HasColumnName("RelationshipID");

            entity
                .HasOne(d => d.DrelationshipStatus)
                .WithMany(p => p.RelationshipStatuses)
                .HasForeignKey(d => d.DrelationshipStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RelationshipStatuses_DRelationshipStatuses");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.RelationshipStatus)
                .HasForeignKey<RelationshipStatus>(d => d.Id)
                .HasConstraintName("FK_RelationshipStatuses_Root");

            entity
                .HasOne(d => d.Relationship)
                .WithMany(p => p.RelationshipStatuses)
                .HasForeignKey(d => d.RelationshipId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RelationshipStatuses_Relationships");
        });

        modelBuilder.Entity<Report>(entity =>
        {
            entity.ToTable("Reports", "Core");

            entity.HasIndex(e => e.AgreementId, "IX_FK_Reports_Agreements");

            entity.HasIndex(e => e.DreportStatusId, "IX_FK_Reports_DReportStatuses");

            entity.HasIndex(e => e.EmployerId, "IX_FK_Reports_Employers");

            entity.HasIndex(e => e.RateScheduleId, "IX_FK_Reports_RateSchedules");

            entity.HasIndex(e => e.AmendedReportId, "IX_FK_Reports_Reports");

            entity.HasIndex(e => e.Id, "IX_FK_Reports_Root");

            entity.HasIndex(e => e.AgreementId, "IX_Reports_AgreementID").HasFillFactor(80);

            entity.HasIndex(e => e.EmployerId, "IX_Reports_EmployerID").HasFillFactor(80);

            entity
                .HasIndex(
                    e => new
                    {
                        e.EmployerId,
                        e.AgreementId,
                        e.WorkMonth,
                    },
                    "IX_Reports_EmployerIDAgreementIDWorkMonth"
                )
                .HasFillFactor(80);

            entity
                .HasIndex(
                    e => new
                    {
                        e.Id,
                        e.AmendedReportId,
                        e.DreportStatusId,
                    },
                    "IX_Reports_IDAmendedReportIDDReportStatusID"
                )
                .HasFillFactor(80);

            entity.HasIndex(
                e => new
                {
                    e.Id,
                    e.ZeroHour,
                    e.WorkMonth,
                    e.AgreementId,
                    e.EmployerId,
                },
                "_dta_index_Reports_11_453120905__K1_K14_K7_K2_K4_9"
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity
                .Property(e => e.AmendedReportId)
                .HasDefaultValueSql("((0))")
                .HasColumnName("AmendedReportID");
            entity.Property(e => e.DreportStatusId).HasColumnName("DReportStatusID");
            entity.Property(e => e.EmployerId).HasColumnName("EmployerID");
            entity.Property(e => e.PeriodEndDate).HasColumnType("datetime");
            entity.Property(e => e.PeriodStartDate).HasColumnType("datetime");
            entity.Property(e => e.RateScheduleId).HasColumnName("RateScheduleID");
            entity.Property(e => e.SubmissionDate).HasColumnType("datetime");
            entity
                .Property(e => e.WorkMonth)
                .HasComment(
                    "This should always be the first of the month with no time elements (e.g., 1/1/2008 00:00:00.000)"
                )
                .HasColumnType("datetime");

            entity
                .HasOne(d => d.Agreement)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.AgreementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Reports_Agreements");

            entity
                .HasOne(d => d.AmendedReport)
                .WithMany(p => p.InverseAmendedReport)
                .HasForeignKey(d => d.AmendedReportId)
                .HasConstraintName("FK_Reports_Reports");

            entity
                .HasOne(d => d.DreportStatus)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.DreportStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Reports_DReportStatuses");

            entity
                .HasOne(d => d.Employer)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.EmployerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Reports_Employers");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Report)
                .HasForeignKey<Report>(d => d.Id)
                .HasConstraintName("FK_Reports_Root");

            entity
                .HasOne(d => d.RateSchedule)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.RateScheduleId)
                .HasConstraintName("FK_Reports_RateSchedules");
        });

        modelBuilder.Entity<ReportLineItem>(entity =>
        {
            entity.ToTable("ReportLineItems", "Core");

            entity.HasIndex(
                e => e.ClassificationNameId,
                "IX_FK_ReportLineItems_ClassificationNames"
            );

            entity.HasIndex(e => e.DamendmentActionId, "IX_FK_ReportLineItems_DAmendmentActions");

            entity.HasIndex(e => e.EmployeeId, "IX_FK_ReportLineItems_Employees");

            entity.HasIndex(e => e.ReportId, "IX_FK_ReportLineItems_Reports");

            entity.HasIndex(e => e.Id, "IX_FK_ReportLineItems_Root");

            entity.HasIndex(e => e.SubClassificationId, "IX_FK_ReportLineItems_SubClassifications");

            entity.HasIndex(e => e.EmployeeId, "IX_ReportLineItems_EmployeeID").HasFillFactor(80);

            entity.HasIndex(e => e.ReportId, "IX_ReportLineItems_ReportID").HasFillFactor(80);

            entity
                .HasIndex(
                    e => new { e.ReportId, e.AmendedLineItemId },
                    "IX_ReportLineItems_ReportIDAmendedLineItemID"
                )
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasComment("").HasColumnName("ID");
            entity.Property(e => e.AmendedLineItemId).HasColumnName("AmendedLineItemID");
            entity.Property(e => e.ClassificationNameId).HasColumnName("ClassificationNameID");
            entity.Property(e => e.DamendmentActionId).HasColumnName("DAmendmentActionID");
            entity.Property(e => e.EmployeeId).HasColumnName("EmployeeID");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");
            entity.Property(e => e.SubClassificationId).HasColumnName("SubClassificationID");

            entity
                .HasOne(d => d.ClassificationName)
                .WithMany(p => p.ReportLineItems)
                .HasForeignKey(d => d.ClassificationNameId)
                .HasConstraintName("FK_ReportLineItems_ClassificationNames");

            entity
                .HasOne(d => d.DamendmentAction)
                .WithMany(p => p.ReportLineItems)
                .HasForeignKey(d => d.DamendmentActionId)
                .HasConstraintName("FK_ReportLineItems_DAmendmentActions");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.ReportLineItems)
                .HasForeignKey(d => d.EmployeeId)
                .HasConstraintName("FK_ReportLineItems_Employees");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ReportLineItem)
                .HasForeignKey<ReportLineItem>(d => d.Id)
                .HasConstraintName("FK_ReportLineItems_Root");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ReportLineItems)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportLineItems_Reports");

            entity
                .HasOne(d => d.SubClassification)
                .WithMany(p => p.ReportLineItems)
                .HasForeignKey(d => d.SubClassificationId)
                .HasConstraintName("FK_ReportLineItems_SubClassifications");
        });

        modelBuilder.Entity<ReportLineItemDetail>(entity =>
        {
            entity.HasKey(e => new { e.ReportLineItemId, e.BenefitId });

            entity.ToTable("ReportLineItemDetails", "Core");

            entity.HasIndex(e => e.BenefitId, "IX_FK_ReportLineItemDetails_Benefits");

            entity.HasIndex(e => e.ReportLineItemId, "IX_FK_ReportLineItemDetails_ReportLineItems");

            entity.Property(e => e.ReportLineItemId).HasColumnName("ReportLineItemID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.Amount).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.VariantValue).HasColumnType("sql_variant");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.ReportLineItemDetails)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportLineItemDetails_Benefits");

            entity
                .HasOne(d => d.ReportLineItem)
                .WithMany(p => p.ReportLineItemDetails)
                .HasForeignKey(d => d.ReportLineItemId)
                .HasConstraintName("FK_ReportLineItemDetails_ReportLineItems");
        });

        modelBuilder.Entity<ReportSubscription>(entity =>
        {
            entity.HasKey(e => new { e.OrganizationId, e.CustomReportId });

            entity.ToTable("ReportSubscriptions", "Custom");

            entity.Property(e => e.OrganizationId).HasColumnName("OrganizationID");
            entity.Property(e => e.CustomReportId).HasColumnName("CustomReportID");
            entity.Property(e => e.Id).ValueGeneratedOnAdd().HasColumnName("ID");
            entity
                .Property(e => e.ReportSubscriptionTypeId)
                .HasColumnName("ReportSubscriptionTypeID");

            entity
                .HasOne(d => d.ReportSubscriptionType)
                .WithMany(p => p.ReportSubscriptions)
                .HasForeignKey(d => d.ReportSubscriptionTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportSubscriptions_ReportSubscriptionType");
        });

        modelBuilder.Entity<ReportSuppression>(entity =>
        {
            entity.ToTable("ReportSuppressions", "Core");

            entity.HasIndex(e => e.ReportId, "IX_FK_ReportSuppressions_Reports");

            entity.HasIndex(e => e.Id, "IX_FK_ReportSuppressions_Root");

            entity.HasIndex(
                e => new { e.CollectingAgentId, e.ReportId },
                "IX_ReportSuppressions_Report_CollectingAgent"
            );

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.CollectingAgentId).HasColumnName("CollectingAgentID");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ReportSuppression)
                .HasForeignKey<ReportSuppression>(d => d.Id)
                .HasConstraintName("FK_ReportSuppressions_Root");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ReportSuppressions)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportSuppressions_Reports");
        });

        modelBuilder.Entity<ReportedBenefitReleaseAuthorization>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_ReportedBenefitReleaseAuthentications");

            entity.ToTable("ReportedBenefitReleaseAuthorizations", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_ReportReleaseAuthentications_Root");

            entity.HasIndex(
                e => e.BenefitId,
                "IX_FK_ReportedBenefitReleaseAuthentications_Benefits"
            );

            entity.HasIndex(e => e.ReportId, "IX_FK_ReportedBenefitReleaseAuthorizations_Reports");

            entity
                .HasIndex(
                    e => new { e.ReportId, e.BenefitId },
                    "IX_ReportedBenefitReleaseAuthorizations"
                )
                .IsUnique()
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");

            entity
                .HasOne(d => d.Benefit)
                .WithMany(p => p.ReportedBenefitReleaseAuthorizations)
                .HasForeignKey(d => d.BenefitId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportedBenefitReleaseAuthentications_Benefits");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.ReportedBenefitReleaseAuthorization)
                .HasForeignKey<ReportedBenefitReleaseAuthorization>(d => d.Id)
                .HasConstraintName("FK_ReportReleaseAuthentications_Root");

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ReportedBenefitReleaseAuthorizations)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportedBenefitReleaseAuthorizations_Reports");
        });

        modelBuilder.Entity<RoleGroup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__RoleGroups__063C00CD");

            entity.ToTable("RoleGroups", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.Name).HasMaxLength(150);

            entity
                .HasMany(d => d.AspnetRoles)
                .WithMany(p => p.RoleGroups)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleGroupsToAspnetRole",
                    r =>
                        r.HasOne<AspnetRole>()
                            .WithMany()
                            .HasForeignKey("AspnetRoleId")
                            .HasConstraintName("fk_RoleGroupsToASPNetRoles_aspnet_Roles"),
                    l =>
                        l.HasOne<RoleGroup>()
                            .WithMany()
                            .HasForeignKey("RoleGroupId")
                            .HasConstraintName("fk_RoleGroupsToASPNetRoles_RoleGroups"),
                    j =>
                    {
                        j.HasKey("RoleGroupId", "AspnetRoleId")
                            .HasName("PK__RoleGroupsToASPN__0824493F");
                        j.ToTable("RoleGroupsToASPNetRoles", "Core");
                        j.HasIndex(
                            new[] { "RoleGroupId" },
                            "IX_fk_RoleGroupsToASPNetRoles_RoleGroups"
                        );
                        j.HasIndex(
                            new[] { "AspnetRoleId" },
                            "IX_fk_RoleGroupsToASPNetRoles_aspnet_Roles"
                        );
                        j.IndexerProperty<int>("RoleGroupId").HasColumnName("RoleGroupID");
                        j.IndexerProperty<Guid>("AspnetRoleId").HasColumnName("ASPNetRoleID");
                    }
                );

            entity
                .HasMany(d => d.AspnetUsers)
                .WithMany(p => p.RoleGroups)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleGroupsToAspnetUser",
                    r =>
                        r.HasOne<AspnetUser>()
                            .WithMany()
                            .HasForeignKey("AspnetUserGuid")
                            .HasConstraintName("fk_RoleGroupsToASPNetUsers_aspnet_Users"),
                    l =>
                        l.HasOne<RoleGroup>()
                            .WithMany()
                            .HasForeignKey("RoleGroupId")
                            .HasConstraintName("fk_RoleGroupsToASPNetUsers_RoleGroups"),
                    j =>
                    {
                        j.HasKey("RoleGroupId", "AspnetUserGuid")
                            .HasName("PK__RoleGroupsToASPN__0BF4DA23");
                        j.ToTable("RoleGroupsToASPNetUsers", "Core");
                        j.HasIndex(
                            new[] { "RoleGroupId" },
                            "IX_fk_RoleGroupsToASPNetUsers_RoleGroups"
                        );
                        j.HasIndex(
                            new[] { "AspnetUserGuid" },
                            "IX_fk_RoleGroupsToASPNetUsers_aspnet_Users"
                        );
                        j.IndexerProperty<int>("RoleGroupId").HasColumnName("RoleGroupID");
                        j.IndexerProperty<Guid>("AspnetUserGuid").HasColumnName("ASPNetUserGUID");
                    }
                );
        });

        modelBuilder.Entity<Root>(entity =>
        {
            entity.ToTable("Root", "Core");

            entity.HasIndex(e => e.Id, "IX_Root").HasFillFactor(80);

            entity.HasIndex(e => e.Guid, "IX_RootGUID").HasFillFactor(80);

            entity.HasIndex(e => e.Id, "_dta_index_Root_11_1306695953__K1_5");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(256);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Guid).HasColumnName("GUID");
            entity
                .Property(e => e.LastModificationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
        });

        modelBuilder.Entity<ServiceSubscription>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ServiceS__3214EC276BF44AF0");

            entity.ToTable("ServiceSubscriptions", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.PartyId).HasColumnName("PartyID");
            entity.Property(e => e.SubscriptionServiceId).HasColumnName("SubscriptionServiceID");

            entity
                .HasOne(d => d.Party)
                .WithMany(p => p.ServiceSubscriptions)
                .HasForeignKey(d => d.PartyId)
                .HasConstraintName("FK__ServiceSu__Party__7C1F3064");

            entity
                .HasOne(d => d.SubscriptionService)
                .WithMany(p => p.ServiceSubscriptions)
                .HasForeignKey(d => d.SubscriptionServiceId)
                .HasConstraintName("FK__ServiceSu__Subsc__7D13549D");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Settings__3214EC270CAC4B1B");
            entity.Property(e => e.Id).HasColumnName("ID");

            entity.Property(e => e.DSettingsTypeId).HasColumnName("DSettingsTypeID");
            entity.Property(e => e.OwnerId).HasColumnName("OwnerID");
            entity.Property(e => e.LastModifiedBy).HasColumnName("LastModifiedBy");

            // Define relationship to DsettingsType
            entity
                .HasOne(d => d.SettingsType)
                .WithMany(p => p.Settings!)
                .HasForeignKey(d => d.DSettingsTypeId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Settings_DSettingsTypes");

            entity.HasIndex(e => e.DSettingsTypeId, "IX_Settings_DSettingsTypeID");

            entity.ToTable("Settings", "Core");
        });

        modelBuilder.Entity<SpExecutionLog>(entity =>
        {
            entity.ToTable("SpExecutionLog");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.DateTime).HasColumnType("datetime");
            entity.Property(e => e.Guid).HasColumnName("GUID");
            entity.Property(e => e.Status).HasMaxLength(50);
        });

        modelBuilder.Entity<SubClassification>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_JobClassificationSubTypes");

            entity.ToTable("SubClassifications", "Core");

            entity.HasIndex(e => e.ChapterId, "IX_FK_JobClassificationSubTypes_Chapters");

            entity.HasIndex(e => e.DstatusId, "IX_FK_JobClassificationSubTypes_DStatuses");

            entity.HasIndex(e => e.Id, "IX_FK_JobClassificationSubTypes_Root");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.ChapterId).HasColumnName("ChapterID");
            entity.Property(e => e.DstatusId).HasColumnName("DStatusID");
            entity.Property(e => e.Name).HasMaxLength(100);

            entity
                .HasOne(d => d.Chapter)
                .WithMany(p => p.SubClassifications)
                .HasForeignKey(d => d.ChapterId)
                .HasConstraintName("FK_JobClassificationSubTypes_Chapters");

            entity
                .HasOne(d => d.Dstatus)
                .WithMany(p => p.SubClassifications)
                .HasForeignKey(d => d.DstatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobClassificationSubTypes_DStatuses");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.SubClassification)
                .HasForeignKey<SubClassification>(d => d.Id)
                .HasConstraintName("FK_JobClassificationSubTypes_Root");
        });

        modelBuilder.Entity<SubscriptionService>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Subscrip__3214EC27F4F9295F");

            entity.ToTable("SubscriptionServices", "Core");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
        });

        modelBuilder.Entity<ThirdPartyEmployerId>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable(
                    "ThirdPartyEmployerIDs",
                    "Core",
                    tb => tb.HasTrigger("Trigger_ThirdPartyEmployerIDs_LastModifiedDate")
                );

            entity.HasIndex(e => e.EmployerId, "IX_FK_ThirdPartyEmployerIDs_Employers");

            entity.HasIndex(e => e.Id, "IX_FK_ThirdPartyEmployerIDs_Root");

            entity.HasIndex(e => e.ThirdPartyId, "IX_FK_ThirdPartyEmployerIDs_ThirdPartyID");

            entity
                .HasIndex(e => new { e.EmployerId, e.ThirdPartyId }, "IX_ThirdPartyEmployerIDs")
                .IsUnique();

            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity
                .Property(e => e.CreationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.EmployerId).HasColumnName("EmployerID");
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(50);
            entity
                .Property(e => e.LastModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity
                .Property(e => e.ThirdPartyEmployerId1)
                .HasMaxLength(50)
                .HasColumnName("ThirdPartyEmployerID");
            entity.Property(e => e.ThirdPartyId).HasColumnName("ThirdPartyID");

            entity
                .HasOne(d => d.Employer)
                .WithMany()
                .HasForeignKey(d => d.EmployerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ThirdPartyEmployerIDs_Employers");

            entity
                .HasOne(d => d.IdNavigation)
                .WithMany()
                .HasForeignKey(d => d.Id)
                .HasConstraintName("FK_ThirdPartyEmployerIDs_Root");

            entity
                .HasOne(d => d.ThirdParty)
                .WithMany()
                .HasForeignKey(d => d.ThirdPartyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ThirdPartyEmployerIDs_ThirdPartyID");
        });

        modelBuilder.Entity<TimeSheet>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.ShowDTHoursColumn).HasColumnName("ShowDTHoursColumn");
        });

        modelBuilder.Entity<Timeline>(entity =>
        {
            entity.ToTable("Timelines", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Timelines_Root");

            entity.HasIndex(e => e.IsOverridden, "IX_TimeLines_IsOverridden_Covering");

            entity
                .HasIndex(
                    e => new
                    {
                        e.Id,
                        e.EffectiveStartDate,
                        e.EffectiveEndDate,
                        e.IsOverridden,
                    },
                    "IX_Timelines_Covering"
                )
                .HasFillFactor(80);

            entity.Property(e => e.EffectiveEndDate).HasColumnType("datetime");
            entity.Property(e => e.EffectiveStartDate).HasColumnType("datetime");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<TimelinesOrig>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Timelines");

            entity.ToTable("TimelinesOrig", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Timelines_Root");

            entity.HasIndex(e => e.IsOverridden, "IX_TimeLines_IsOverridden_Covering");

            entity
                .HasIndex(
                    e => new
                    {
                        e.Id,
                        e.EffectiveStartDate,
                        e.EffectiveEndDate,
                        e.IsOverridden,
                    },
                    "IX_Timelines_Covering"
                )
                .HasFillFactor(80);

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.EffectiveEndDate).HasColumnType("datetime");
            entity
                .Property(e => e.EffectiveStartDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.TimelinesOrig)
                .HasForeignKey<TimelinesOrig>(d => d.Id)
                .HasConstraintName("FK_Timelines_Root");
        });

        modelBuilder.Entity<Trade>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Affinities");

            entity.ToTable("Trades", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Affinities_Organizations");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Trade)
                .HasForeignKey<Trade>(d => d.Id)
                .HasConstraintName("FK_Affinities_Organizations");
        });

        modelBuilder.Entity<Union>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Brokerages_1");

            entity.ToTable("Unions", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Brokerages_Organizations");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity
                .Property(e => e.DefaultDelinquentDay)
                .HasDefaultValueSql("((20))")
                .HasComment(
                    "Determines the default day of the month that payroll reports are due on. If a payroll report is submitted after this date, it is delinquent."
                );

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Union)
                .HasForeignKey<Union>(d => d.Id)
                .HasConstraintName("FK_Brokerages_Organizations");
        });

        modelBuilder.Entity<VClassificationXref>(entity =>
        {
            entity.HasNoKey().ToView("vClassificationXRefs", "Core");

            entity.Property(e => e.AgreementEndDate).HasColumnType("datetime");
            entity.Property(e => e.AgreementId).HasColumnName("AgreementID");
            entity.Property(e => e.Benefit).HasMaxLength(50);
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.Classification).HasMaxLength(100);
            entity.Property(e => e.ClassificationNameId).HasColumnName("ClassificationNameID");
            entity.Property(e => e.FundAdministratorId).HasColumnName("FundAdministratorID");
            entity.Property(e => e.Id).HasMaxLength(64).IsUnicode(false);
            entity.Property(e => e.SubClassification).HasMaxLength(100);
            entity.Property(e => e.SubClassificationId).HasColumnName("SubClassificationID");
            entity.Property(e => e.UnionId).HasColumnName("UnionID");
            entity.Property(e => e.XrefId).HasColumnName("XrefID");
            entity.Property(e => e.Xrefvalue).HasMaxLength(255);
        });

        modelBuilder.Entity<VCurrentRelationship>(entity =>
        {
            entity.HasNoKey().ToView("vCurrentRelationships", "Dev");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LeftPartyId).HasColumnName("LeftPartyID");
            entity.Property(e => e.LeftPartyName).HasColumnName("Left Party Name");
            entity.Property(e => e.RightPartyId).HasColumnName("RightPartyID");
            entity.Property(e => e.RightPartyName).HasColumnName("Right Party Name");
        });

        modelBuilder.Entity<VEffectiveUserRole>(entity =>
        {
            entity.HasNoKey().ToView("vEffectiveUserRoles", "Core");

            entity.Property(e => e.LoweredRoleGroupName).HasMaxLength(150);
            entity.Property(e => e.LoweredRoleName).HasMaxLength(256);
            entity.Property(e => e.LoweredUserName).HasMaxLength(256);
            entity.Property(e => e.RoleGroupId).HasColumnName("RoleGroupID");
            entity.Property(e => e.RoleGroupName).HasMaxLength(150);
            entity.Property(e => e.RoleName).HasMaxLength(256);
            entity.Property(e => e.UserName).HasMaxLength(256);
        });

        modelBuilder.Entity<VOrganization>(entity =>
        {
            entity.HasNoKey().ToView("vOrganizations", "Dev");

            entity.Property(e => e.Guid).HasColumnName("GUID");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<VPayment>(entity =>
        {
            entity.HasNoKey().ToView("vPayments", "Dev");

            entity.Property(e => e.Benefit).HasMaxLength(50);
            entity
                .Property(e => e.BenefitAmount)
                .HasColumnType("decimal(18, 2)")
                .HasColumnName("Benefit Amount");
            entity.Property(e => e.BenefitId).HasColumnName("BenefitID");
            entity.Property(e => e.CreationDate).HasColumnType("datetime");
            entity.Property(e => e.LastModifiedBy).HasMaxLength(256);
            entity.Property(e => e.Payment).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.PaymentId).HasColumnName("PaymentID");
            entity.Property(e => e.ReportId).HasColumnName("ReportID");
            entity.Property(e => e.WorkMonth).HasColumnType("datetime");
        });

        modelBuilder.Entity<VRoleGroupsAndRole>(entity =>
        {
            entity.HasNoKey().ToView("vRoleGroupsAndRoles", "Core");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Name).HasMaxLength(150);
            entity.Property(e => e.RoleDescription).HasMaxLength(256);
            entity.Property(e => e.RoleGroupDescription).HasColumnType("text");
            entity.Property(e => e.RoleName).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetApplication>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_Applications");

            entity.Property(e => e.ApplicationName).HasMaxLength(256);
            entity.Property(e => e.Description).HasMaxLength(256);
            entity.Property(e => e.LoweredApplicationName).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetMembershipUser>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_MembershipUsers");

            entity.Property(e => e.Comment).HasColumnType("ntext");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(256);
            entity
                .Property(e => e.FailedPasswordAnswerAttemptWindowStart)
                .HasColumnType("datetime");
            entity.Property(e => e.FailedPasswordAttemptWindowStart).HasColumnType("datetime");
            entity.Property(e => e.LastActivityDate).HasColumnType("datetime");
            entity.Property(e => e.LastLockoutDate).HasColumnType("datetime");
            entity.Property(e => e.LastLoginDate).HasColumnType("datetime");
            entity.Property(e => e.LastPasswordChangedDate).HasColumnType("datetime");
            entity.Property(e => e.LoweredEmail).HasMaxLength(256);
            entity.Property(e => e.MobileAlias).HasMaxLength(16);
            entity.Property(e => e.MobilePin).HasMaxLength(16).HasColumnName("MobilePIN");
            entity.Property(e => e.PasswordAnswer).HasMaxLength(128);
            entity.Property(e => e.PasswordQuestion).HasMaxLength(256);
            entity.Property(e => e.UserName).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetProfile>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_Profiles");

            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<VwAspnetRole>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_Roles");

            entity.Property(e => e.Description).HasMaxLength(256);
            entity.Property(e => e.LoweredRoleName).HasMaxLength(256);
            entity.Property(e => e.RoleName).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetUser>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_Users");

            entity.Property(e => e.LastActivityDate).HasColumnType("datetime");
            entity.Property(e => e.LoweredUserName).HasMaxLength(256);
            entity.Property(e => e.MobileAlias).HasMaxLength(16);
            entity.Property(e => e.UserName).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetUsersInRole>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_UsersInRoles");
        });

        modelBuilder.Entity<VwAspnetWebPartStatePath>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_WebPartState_Paths");

            entity.Property(e => e.LoweredPath).HasMaxLength(256);
            entity.Property(e => e.Path).HasMaxLength(256);
        });

        modelBuilder.Entity<VwAspnetWebPartStateShared>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_WebPartState_Shared");

            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<VwAspnetWebPartStateUser>(entity =>
        {
            entity.HasNoKey().ToView("vw_aspnet_WebPartState_User");

            entity.Property(e => e.LastUpdatedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<Website>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Website");

            entity.ToTable("Websites", "Core");

            entity.HasIndex(e => e.Id, "IX_FK_Websites_ContactMechanisms");

            entity.HasIndex(e => e.DwebsiteTypeId, "IX_FK_Websites_DWebsiteTypes");

            entity.Property(e => e.Id).ValueGeneratedNever().HasColumnName("ID");
            entity.Property(e => e.DwebsiteTypeId).HasColumnName("DWebsiteTypeID");
            entity.Property(e => e.Url).HasColumnName("URL");

            entity
                .HasOne(d => d.DwebsiteType)
                .WithMany(p => p.Websites)
                .HasForeignKey(d => d.DwebsiteTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Websites_DWebsiteTypes");

            entity
                .HasOne(d => d.IdNavigation)
                .WithOne(p => p.Website)
                .HasForeignKey<Website>(d => d.Id)
                .HasConstraintName("FK_Websites_ContactMechanisms");
        });

        modelBuilder.Entity<EmployerRosterView>(entity =>
        {
            entity.HasNoKey().ToView("EmployerRosterView", "Core");
        });

        modelBuilder.Entity<DownloadContribution>(entity =>
        {
            entity.HasNoKey().ToView("DownloadContributionsView", "Core");
        });
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
