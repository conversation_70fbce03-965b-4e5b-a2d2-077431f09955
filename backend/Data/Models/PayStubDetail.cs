using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using HotChocolate;
using Microsoft.EntityFrameworkCore;

namespace backend.Data.Models;

[Node]
public partial class PayStubDetail
{
    [ID(nameof(PayStubDetail))]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [GraphQLIgnore]
    public Guid? OldId { get; set; }

    public string? Name { get; set; }

    public DateOnly WorkDate { get; set; }

    public float? OTHours { get; set; }

    public float? STHours { get; set; }

    public float? DTHours { get; set; }

    /// <summary>
    /// Computed property that calculates total hours from individual hour types.
    /// This property is not mapped to the database.
    /// </summary>
    [NotMapped]
    public float? TotalHours => (STHours ?? 0) + (OTHours ?? 0) + (DTHours ?? 0);

    public string? JobCode { get; set; }

    public int? AgreementId { get; set; }

    public int? ClassificationId { get; set; }

    public string? CostCenter { get; set; }

    public float? HourlyRate { get; set; }

    public float? Bonus { get; set; }

    public float? Expenses { get; set; }

    [ID(nameof(PayStub))]
    public int PayStubId { get; set; }

    public int? ReportLineItemId { get; set; }

    public int? SubClassificationId { get; set; }

    public string? EarningsCode { get; set; }

    public virtual PayStub PayStub { get; set; } = null!;

    public virtual ReportLineItem? ReportLineItem { get; set; }

    // Node resolver moved to Types/Queries/PayStubsQuery.cs for enhanced implementation
}
