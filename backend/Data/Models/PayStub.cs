using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using HotChocolate;
using Microsoft.EntityFrameworkCore;

namespace backend.Data.Models;

[Node]
public partial class PayStub
{
    [ID(nameof(PayStub))]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [GraphQLIgnore]
    public Guid? OldId { get; set; }

    public string? Name { get; set; }

    [ID(nameof(Employee))]
    public int EmployeeId { get; set; }

    public int TimeSheetId { get; set; } // Changed to integer

    /// <summary>
    /// Computed property that calculates total hours from PayStub details.
    /// This property is not mapped to the database.
    /// </summary>
    [NotMapped]
    public float? TotalHours =>
        Details?.Sum(d => (d.STHours ?? 0) + (d.OTHours ?? 0) + (d.DTHours ?? 0)) ?? 0;

    public virtual ICollection<PayStubDetail> Details { get; set; } = new List<PayStubDetail>();

    public virtual TimeSheet TimeSheet { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    // Node resolver moved to Types/Queries/PayStubsQuery.cs for enhanced implementation
}
