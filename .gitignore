## .gitignore for ASP.NET Core
# Ignore .NET Core build and runtime artifacts
bin/
obj/
out/

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows image file caches
Thumbs.db
ehthumbs.db

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# macOS
*.DS_Store
.AppleDouble
.LSOverride

# Visual Studio cache/options directory
.vs/
tmp
/aspire/kafka/kafka_data/

# AI tooling
.ai-workflow/
task-progress

backend/TestResults/
backend/logs/
identity/logs/

# Relay / GraphQL artefacts
ui/src/relay/__generated__/

# Allow unit-test mocks to live alongside tests
!**/__tests__/**/__generated__/

# Block old locations
ui/lib/**/__generated__/
